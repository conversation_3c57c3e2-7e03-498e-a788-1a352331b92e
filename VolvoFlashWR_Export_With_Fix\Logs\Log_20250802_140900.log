Log started at 8/2/2025 2:09:00 PM
2025-08-02 14:09:00.220 [Information] LoggingService: Logging service initialized
2025-08-02 14:09:00.242 [Information] App: Starting integrated application initialization
2025-08-02 14:09:00.245 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-02 14:09:00.249 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-02 14:09:00.251 [Information] IntegratedStartupService: Setting up application environment
2025-08-02 14:09:00.252 [Information] IntegratedStartupService: Application environment setup completed
2025-08-02 14:09:00.254 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-02 14:09:00.258 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-08-02 14:09:00.261 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-02 14:09:00.267 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-02 14:09:00.287 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.296 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.299 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.300 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-02 14:09:00.305 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.309 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.312 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.313 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:09:00.317 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.321 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.325 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.326 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:09:00.331 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.334 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.338 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.339 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:09:00.344 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.348 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.351 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.352 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:09:00.356 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.360 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.363 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.364 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:09:00.368 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:09:00.372 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.375 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:09:00.376 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:09:00.379 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-02 14:09:00.382 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-02 14:09:00.383 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-02 14:09:00.386 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-02 14:09:00.387 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-02 14:09:00.389 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-02 14:09:00.390 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-02 14:09:00.390 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-02 14:09:00.400 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-02 14:09:00.401 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-02 14:09:00.403 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-02 14:09:00.404 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:09:00.404 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:09:00.405 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:09:00.405 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:09:00.406 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:09:00.406 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:09:00.418 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-02 14:09:00.418 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-02 14:09:00.419 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-02 14:09:00.428 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-02 14:09:00.429 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-02 14:09:00.432 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-02 14:09:00.434 [Information] LibraryExtractor: Starting library extraction process
2025-08-02 14:09:00.438 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-02 14:09:00.444 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-02 14:09:00.447 [Information] LibraryExtractor: Copying system libraries
2025-08-02 14:09:00.464 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-02 14:09:00.508 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-08-02 14:09:30.531 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 14:09:31.535 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-08-02 14:10:01.539 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 14:10:01.540 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:10:31.543 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 14:10:32.544 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-08-02 14:11:02.548 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 14:11:02.549 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:11:32.551 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 14:11:33.552 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-08-02 14:12:03.556 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 14:12:03.557 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-08-02 14:12:03.557 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-08-02 14:12:03.561 [Information] LibraryExtractor: Verifying library extraction
2025-08-02 14:12:03.561 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-02 14:12:03.562 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-02 14:12:03.562 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-02 14:12:03.563 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-02 14:12:03.563 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-02 14:12:03.569 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-02 14:12:03.573 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-02 14:12:03.574 [Information] DependencyManager: Initializing dependency manager
2025-08-02 14:12:03.575 [Information] DependencyManager: Setting up library search paths
2025-08-02 14:12:03.577 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:12:03.578 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 14:12:03.578 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 14:12:03.578 [Information] DependencyManager: Updated PATH environment variable
2025-08-02 14:12:03.580 [Information] DependencyManager: Verifying required directories
2025-08-02 14:12:03.580 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:12:03.581 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 14:12:03.581 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-02 14:12:03.582 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 14:12:03.583 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-02 14:12:03.590 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-08-02 14:12:03.591 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 14:12:03.593 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 14:12:03.600 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 14:12:03.602 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-08-02 14:12:03.603 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 14:12:03.603 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 14:12:03.610 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 14:12:03.612 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 14:12:03.613 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 14:12:03.614 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-08-02 14:12:03.621 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-08-02 14:12:03.623 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-08-02 14:12:03.625 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-08-02 14:12:03.626 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:12:03.626 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:12:03.627 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:12:03.627 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:12:03.628 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:12:03.629 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:12:03.630 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:12:03.630 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:12:03.631 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:12:03.631 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:12:03.632 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:12:03.633 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:12:03.634 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 14:12:03.634 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 14:12:03.635 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 14:12:03.636 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 14:12:03.637 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 14:12:03.638 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 14:12:03.638 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-02 14:12:03.639 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-02 14:12:03.640 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-02 14:12:03.642 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-02 14:12:03.643 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 14:12:03.643 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-02 14:12:03.644 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 14:12:03.645 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 14:12:03.647 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-02 14:12:03.649 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 14:12:03.649 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-02 14:12:03.651 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 14:12:03.653 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-02 14:12:03.656 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-02 14:12:03.657 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 14:12:03.657 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-02 14:12:03.659 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 14:12:03.660 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-02 14:12:03.662 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-02 14:12:03.663 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 14:12:03.664 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-02 14:12:03.664 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 14:12:03.665 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-02 14:12:03.666 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-02 14:12:03.667 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-02 14:12:03.668 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 14:12:03.669 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 14:12:03.670 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-08-02 14:12:03.670 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 14:12:03.671 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 14:12:03.672 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 14:12:03.673 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-08-02 14:12:03.673 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 14:12:03.673 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 14:12:03.674 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 14:12:03.675 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-02 14:12:03.675 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-08-02 14:12:03.676 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-08-02 14:12:03.676 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-08-02 14:12:03.677 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-08-02 14:12:03.677 [Information] DependencyManager: Setting up environment variables
2025-08-02 14:12:03.678 [Information] DependencyManager: Environment variables configured
2025-08-02 14:12:03.680 [Information] DependencyManager: Verifying library loading status
2025-08-02 14:12:04.072 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-08-02 14:12:04.074 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-02 14:12:04.074 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-02 14:12:04.076 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-02 14:12:04.078 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-02 14:12:04.088 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-02 14:12:04.091 [Information] IntegratedStartupService: Verifying system readiness
2025-08-02 14:12:04.091 [Information] IntegratedStartupService: System readiness verification passed
2025-08-02 14:12:04.092 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-02 14:12:04.093 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-02 14:12:04.094 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 14:12:04.095 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:12:04.095 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 14:12:04.096 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-02 14:12:04.096 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-02 14:12:04.097 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-02 14:12:04.097 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:12:04.097 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-02 14:12:04.098 [Information] App: Integrated startup completed successfully
2025-08-02 14:12:04.101 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-02 14:12:04.125 [Information] App: Initializing application services
2025-08-02 14:12:04.128 [Information] AppConfigurationService: Initializing configuration service
2025-08-02 14:12:04.128 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 14:12:04.211 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 14:12:04.212 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-02 14:12:04.213 [Information] App: Configuration service initialized successfully
2025-08-02 14:12:04.215 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-02 14:12:04.215 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-02 14:12:04.224 [Information] App: Environment variable exists: True, not 'false': False
2025-08-02 14:12:04.224 [Information] App: Final useDummyImplementations value: False
2025-08-02 14:12:04.225 [Information] App: Updating config to NOT use dummy implementations
2025-08-02 14:12:04.228 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-02 14:12:04.249 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 14:12:04.252 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-02 14:12:04.252 [Information] App: usePatchedImplementation flag is: True
2025-08-02 14:12:04.253 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-02 14:12:04.253 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-02 14:12:04.254 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-02 14:12:04.255 [Information] App: verboseLogging flag is: True
2025-08-02 14:12:04.257 [Information] App: Verifying real hardware requirements...
2025-08-02 14:12:04.258 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-02 14:12:04.258 [Information] App: ✓ Found critical library: apci.dll
2025-08-02 14:12:04.258 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-02 14:12:04.259 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-02 14:12:04.260 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 14:12:04.261 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-02 14:12:04.261 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-02 14:12:04.262 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-02 14:12:04.273 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-02 14:12:04.276 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-02 14:12:04.276 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-02 14:12:04.279 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-02 14:12:04.285 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-02 14:13:46.235 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 14:13:46.235 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-02 14:13:46.236 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-02 14:13:46.236 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:13:46.237 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:13:46.237 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:13:46.238 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:13:46.238 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:13:46.238 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:13:46.238 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-02 14:13:46.240 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-02 14:13:46.241 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-02 14:13:46.241 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 14:13:46.242 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 14:13:46.242 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-02 14:13:46.243 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-02 14:13:46.243 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-02 14:13:46.243 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-02 14:13:46.245 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-02 14:13:46.246 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-02 14:13:46.248 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-02 14:13:46.249 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-02 14:13:46.249 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-02 14:13:46.250 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-02 14:13:46.252 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-02 14:13:46.252 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-08-02 14:13:46.255 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-08-02 14:13:46.256 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-08-02 14:13:46.258 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-08-02 14:13:46.258 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-08-02 14:13:46.287 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-08-02 14:13:46.287 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-08-02 14:13:46.294 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-08-02 14:13:46.294 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-08-02 14:13:46.295 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-08-02 14:13:46.296 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-08-02 14:13:46.299 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-08-02 14:13:46.299 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-08-02 14:13:46.299 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-08-02 14:13:46.300 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:13:46.300 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-08-02 14:13:46.300 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-08-02 14:13:46.300 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-08-02 14:13:46.303 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-08-02 14:13:46.307 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-08-02 14:13:46.321 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-08-02 14:13:46.323 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 14:13:46.323 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-08-02 14:13:46.349 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 14:13:46.395 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-08-02 14:13:46.397 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-08-02 14:13:46.398 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-08-02 14:13:46.399 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 14:13:46.399 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-08-02 14:13:46.400 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 14:13:46.400 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-08-02 14:13:46.401 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 14:13:46.402 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-08-02 14:13:46.403 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-08-02 14:13:46.403 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-08-02 14:13:46.404 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-08-02 14:13:46.406 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-08-02 14:13:46.407 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-08-02 14:13:46.408 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-08-02 14:13:46.416 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-08-02 14:13:46.418 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-08-02 14:13:46.419 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 14:13:46.419 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-08-02 14:13:46.419 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-08-02 14:13:46.422 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-08-02 14:13:46.425 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-08-02 14:13:46.425 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-08-02 14:13:46.426 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-08-02 14:13:46.427 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-08-02 14:13:46.427 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-08-02 14:13:46.427 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-08-02 14:13:46.429 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-08-02 14:13:46.429 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-08-02 14:13:46.430 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-08-02 14:13:46.432 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-08-02 14:13:46.433 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-08-02 14:13:46.433 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-08-02 14:13:46.433 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:13:46.435 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-08-02 14:13:46.438 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-08-02 14:13:46.439 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 14:13:46.439 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-08-02 14:13:46.440 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-08-02 14:13:46.442 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-08-02 14:13:46.442 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-08-02 14:13:46.443 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-08-02 14:13:46.447 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-08-02 14:13:46.450 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-08-02 14:13:46.450 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-08-02 14:13:46.461 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-08-02 14:13:46.462 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-08-02 14:13:46.462 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 14:13:46.462 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 14:13:46.467 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-08-02 14:13:46.469 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.470 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.470 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-08-02 14:13:46.471 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.472 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.473 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-08-02 14:13:46.474 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.475 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-08-02 14:13:46.478 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.478 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-08-02 14:13:46.479 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.480 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.480 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-08-02 14:13:46.481 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.483 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.483 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-08-02 14:13:46.484 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 14:13:46.485 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 14:13:46.486 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.487 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.488 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-08-02 14:13:46.488 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:13:46.490 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.490 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-08-02 14:13:46.492 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.493 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.494 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-08-02 14:13:46.495 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 14:13:46.496 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 14:13:46.497 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-08-02 14:13:46.498 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 14:13:46.500 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 14:13:46.501 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 14:13:46.505 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-08-02 14:13:46.505 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 14:13:46.506 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-08-02 14:13:46.507 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-08-02 14:13:46.508 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-08-02 14:13:46.510 [Information] VocomDriver: Initializing Vocom driver
2025-08-02 14:13:46.513 [Information] VocomNativeInterop: Initializing Vocom driver
2025-08-02 14:13:46.517 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-08-02 14:13:46.518 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 14:13:46.518 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 14:13:46.520 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 14:13:46.520 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 14:13:46.525 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-08-02 14:13:46.527 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-08-02 14:13:46.530 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-08-02 14:13:46.530 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-08-02 14:13:46.531 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-08-02 14:13:46.531 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:13:46.536 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-08-02 14:13:46.539 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-08-02 14:13:46.542 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-08-02 14:13:46.544 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-08-02 14:13:46.544 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-08-02 14:13:46.545 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-08-02 14:13:46.547 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-08-02 14:13:46.548 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-08-02 14:13:46.548 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-08-02 14:13:46.548 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-08-02 14:13:46.549 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-08-02 14:13:46.549 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-08-02 14:13:46.549 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-08-02 14:13:46.550 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-08-02 14:13:46.550 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-08-02 14:13:46.551 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-08-02 14:13:46.551 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-08-02 14:13:46.551 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-08-02 14:13:46.552 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-08-02 14:13:46.552 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-08-02 14:13:46.552 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-08-02 14:13:46.553 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-08-02 14:13:46.553 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-08-02 14:13:46.553 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-08-02 14:13:46.554 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-08-02 14:13:46.554 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-08-02 14:13:46.554 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-08-02 14:13:46.555 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-08-02 14:13:46.555 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-08-02 14:13:46.555 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-08-02 14:13:46.559 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-08-02 14:13:46.560 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-08-02 14:13:46.561 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-08-02 14:13:46.562 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-08-02 14:13:46.563 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-08-02 14:13:46.563 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-08-02 14:13:46.563 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-08-02 14:13:46.564 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-08-02 14:13:46.565 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-08-02 14:13:46.565 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-08-02 14:13:46.565 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-08-02 14:13:46.567 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-08-02 14:13:46.568 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-08-02 14:13:46.568 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-08-02 14:13:46.569 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-08-02 14:13:46.569 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-08-02 14:13:46.569 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-08-02 14:13:46.569 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-08-02 14:13:46.570 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-08-02 14:13:46.571 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-08-02 14:13:46.573 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-08-02 14:13:46.575 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-08-02 14:13:46.575 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-08-02 14:13:46.575 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-08-02 14:13:46.576 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-08-02 14:13:46.576 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-08-02 14:13:46.576 [Information] VocomDriver: Vocom driver initialized successfully
2025-08-02 14:13:46.585 [Information] VocomService: Initializing Vocom service with dependencies
2025-08-02 14:13:46.586 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-08-02 14:13:46.588 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-08-02 14:13:46.590 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-08-02 14:13:46.669 [Information] WiFiCommunicationService: WiFi is available
2025-08-02 14:13:46.670 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-08-02 14:13:46.672 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-08-02 14:13:46.674 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-08-02 14:13:46.678 [Information] BluetoothCommunicationService: Bluetooth is available
2025-08-02 14:13:46.679 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-08-02 14:13:46.680 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 14:13:46.682 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 14:13:46.683 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 14:13:46.684 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 14:13:46.689 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 14:13:46.689 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 14:13:46.689 [Information] VocomService: Native USB communication service initialized
2025-08-02 14:13:46.690 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 14:13:46.691 [Information] VocomService: Connection recovery service initialized
2025-08-02 14:13:46.692 [Information] VocomService: Enhanced services initialization completed
2025-08-02 14:13:46.695 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:46.715 [Information] VocomService: PTT application is not running
2025-08-02 14:13:46.717 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:46.720 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:46.721 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 14:13:46.722 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-08-02 14:13:46.722 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-08-02 14:13:46.727 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 14:13:46.727 [Information] VocomService: Using new enhanced device detection service
2025-08-02 14:13:46.730 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 14:13:46.732 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 14:13:47.474 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 14:13:47.476 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 14:13:47.477 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 14:13:47.479 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 14:13:47.479 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 14:13:47.481 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 14:13:47.483 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 14:13:47.486 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 14:13:47.739 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 14:13:47.742 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 14:13:47.745 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 14:13:47.745 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 14:13:47.747 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 14:13:47.757 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 14:13:47.758 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 14:13:47.759 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 14:13:47.760 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 14:13:47.760 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:47.761 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:47.763 [Debug] VocomService: Checking if WiFi is available
2025-08-02 14:13:47.765 [Debug] VocomService: WiFi is available
2025-08-02 14:13:47.766 [Information] VocomService: Found 3 Vocom devices
2025-08-02 14:13:47.766 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-08-02 14:13:47.768 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 54a5235d-f31d-430c-9713-c9390d6b8e19, Type: USB)
2025-08-02 14:13:47.768 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 84c07dd3-e20e-47cc-934e-084b20cd55b7, Type: Bluetooth)
2025-08-02 14:13:47.769 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: fb9bd3a6-efd1-4532-96e7-fbb358f71fe1, Type: WiFi)
2025-08-02 14:13:47.770 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-08-02 14:13:47.770 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-08-02 14:13:47.771 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-08-02 14:13:47.771 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-08-02 14:13:47.771 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-08-02 14:13:47.772 [Information] App: Architecture-aware Vocom service created successfully
2025-08-02 14:13:47.772 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 14:13:47.772 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 14:13:47.772 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 14:13:47.773 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 14:13:47.775 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 14:13:47.775 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 14:13:47.775 [Information] VocomService: Native USB communication service initialized
2025-08-02 14:13:47.776 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 14:13:47.776 [Information] VocomService: Connection recovery service initialized
2025-08-02 14:13:47.776 [Information] VocomService: Enhanced services initialization completed
2025-08-02 14:13:47.777 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:47.794 [Information] VocomService: PTT application is not running
2025-08-02 14:13:47.795 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:47.795 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:47.795 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 14:13:47.796 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-02 14:13:47.796 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-02 14:13:47.796 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-02 14:13:47.842 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 14:13:47.843 [Information] VocomService: Using new enhanced device detection service
2025-08-02 14:13:47.843 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 14:13:47.843 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 14:13:48.093 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 14:13:48.093 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 14:13:48.094 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 14:13:48.094 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 14:13:48.094 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 14:13:48.095 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 14:13:48.095 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 14:13:48.096 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 14:13:48.336 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 14:13:48.337 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 14:13:48.337 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 14:13:48.338 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 14:13:48.339 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 14:13:48.347 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 14:13:48.348 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 14:13:48.348 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 14:13:48.348 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 14:13:48.349 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:48.349 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:48.350 [Debug] VocomService: Checking if WiFi is available
2025-08-02 14:13:48.350 [Debug] VocomService: WiFi is available
2025-08-02 14:13:48.351 [Information] VocomService: Found 3 Vocom devices
2025-08-02 14:13:48.351 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-08-02 14:13:48.353 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:48.353 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:48.371 [Information] VocomService: PTT application is not running
2025-08-02 14:13:48.377 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 14:13:48.377 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 14:13:48.378 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:48.394 [Information] VocomService: PTT application is not running
2025-08-02 14:13:48.395 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 14:13:48.397 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 14:13:48.398 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:13:48.401 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:13:48.403 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:48.404 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:48.404 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:48.405 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:48.405 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:48.405 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:48.405 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:48.406 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:48.406 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:48.406 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:13:48.407 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:48.407 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:48.409 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:48.411 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:48.411 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:48.412 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:13:48.413 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:48.413 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:13:48.414 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:48.414 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:13:48.414 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:13:48.415 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:13:48.415 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 14:13:49.416 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 14:13:49.417 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:13:49.417 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:13:49.418 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:49.418 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:49.419 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:49.419 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:49.419 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:49.419 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:49.420 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:49.420 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:49.420 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:49.420 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:13:49.421 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:49.421 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:49.421 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:49.422 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:49.422 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:49.422 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:13:49.423 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:49.423 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:13:49.423 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:49.423 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:13:49.424 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:13:49.425 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:13:49.425 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 14:13:50.425 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 14:13:50.426 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:13:50.427 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:13:50.427 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:50.428 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:50.428 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:50.428 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:50.429 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:50.429 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:50.429 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:50.430 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:50.430 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:50.431 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:13:50.431 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:50.431 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:50.431 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:50.432 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:50.432 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:50.433 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:13:50.433 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:50.433 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:13:50.434 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:50.434 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:13:50.434 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:13:50.434 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:13:50.435 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 14:13:50.436 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 14:13:50.436 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 14:13:50.473 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 14:13:50.476 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 14:13:50.478 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 14:13:50.479 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 14:13:50.479 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 14:13:50.480 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 14:13:50.480 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 14:13:50.483 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 14:13:50.484 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 14:13:50.488 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 14:13:50.490 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 14:13:50.491 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 14:13:50.492 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 14:13:50.495 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 14:13:50.495 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:13:50.496 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:50.497 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-08-02 14:13:50.500 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-02 14:13:50.503 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:50.504 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-02 14:13:50.512 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 14:13:50.514 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 14:13:50.514 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 14:13:50.515 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 14:13:50.516 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 14:13:50.516 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 14:13:50.516 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 14:13:50.517 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 14:13:50.520 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 14:13:50.520 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 14:13:50.521 [Information] VocomService: Using new enhanced device detection service
2025-08-02 14:13:50.521 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 14:13:50.521 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 14:13:50.808 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 14:13:50.810 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 14:13:50.811 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 14:13:50.811 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 14:13:50.811 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 14:13:50.812 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 14:13:50.812 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 14:13:50.812 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 14:13:51.064 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 14:13:51.064 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 14:13:51.065 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 14:13:51.065 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 14:13:51.066 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 14:13:51.072 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 14:13:51.072 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 14:13:51.073 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 14:13:51.073 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 14:13:51.073 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:51.074 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:51.074 [Debug] VocomService: Checking if WiFi is available
2025-08-02 14:13:51.075 [Debug] VocomService: WiFi is available
2025-08-02 14:13:51.077 [Information] VocomService: Found 3 Vocom devices
2025-08-02 14:13:51.078 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:51.078 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:51.079 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:51.095 [Information] VocomService: PTT application is not running
2025-08-02 14:13:51.096 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 14:13:51.096 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 14:13:51.096 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:51.115 [Information] VocomService: PTT application is not running
2025-08-02 14:13:51.115 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 14:13:51.115 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 14:13:51.116 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:13:51.116 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:13:51.117 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:51.117 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:51.117 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:51.118 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:51.118 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:51.118 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:51.119 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:51.119 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:51.119 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:51.119 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:13:51.120 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:51.120 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:51.120 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:51.121 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:51.121 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:51.121 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:13:51.122 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:51.122 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:13:51.122 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:51.122 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:13:51.123 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:13:51.123 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:13:51.123 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 14:13:52.123 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 14:13:52.123 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:13:52.124 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:13:52.125 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:52.127 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:52.127 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:52.127 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:52.128 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:52.128 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:52.128 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:52.129 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:52.129 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:52.129 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:13:52.129 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:52.130 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:52.130 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:52.130 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:52.131 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:52.131 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:13:52.132 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:52.132 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:13:52.132 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:52.134 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:13:52.135 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:13:52.135 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:13:52.135 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 14:13:53.136 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 14:13:53.137 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:13:53.137 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:13:53.138 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:53.138 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:53.139 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:53.139 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:53.140 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:53.140 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:53.140 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:53.141 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:53.141 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:53.141 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:13:53.142 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:53.142 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:53.144 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:13:53.144 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:13:53.144 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:13:53.145 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:13:53.145 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:53.145 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:13:53.146 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:13:53.146 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:13:53.146 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:13:53.146 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:13:53.147 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 14:13:53.147 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 14:13:53.151 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 14:13:53.151 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 14:13:53.152 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 14:13:53.152 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 14:13:53.152 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 14:13:53.153 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 14:13:53.153 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 14:13:53.154 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 14:13:53.154 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 14:13:53.154 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 14:13:53.154 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 14:13:53.155 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 14:13:53.155 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 14:13:53.156 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 14:13:53.156 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 14:13:53.156 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:13:53.157 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:13:53.157 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:53.157 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:53.158 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:13:53.159 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 14:13:53.159 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 14:13:53.161 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:53.180 [Information] VocomService: PTT application is not running
2025-08-02 14:13:53.183 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 14:13:53.183 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:53.184 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:53.185 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-08-02 14:13:53.988 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 14:13:53.989 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 14:13:53.989 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-08-02 14:13:53.990 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 14:13:53.997 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 14:13:53.999 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 14:13:54.004 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 14:13:54.006 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 14:13:54.010 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 14:13:54.019 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 14:13:54.024 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 14:13:54.036 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 14:13:54.037 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 14:13:54.038 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 14:13:54.038 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 14:13:54.039 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 14:13:54.040 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 14:13:54.041 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 14:13:54.041 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 14:13:54.042 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 14:13:54.042 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 14:13:54.043 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 14:13:54.044 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 14:13:54.044 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 14:13:54.045 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 14:13:54.045 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 14:13:54.045 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 14:13:54.046 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 14:13:54.049 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 14:13:54.056 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 14:13:54.057 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 14:13:54.061 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 14:13:54.063 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:54.069 [Information] CANRegisterAccess: Read value 0x74 from register 0x0141 (simulated)
2025-08-02 14:13:54.076 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:54.082 [Information] CANRegisterAccess: Read value 0x77 from register 0x0141 (simulated)
2025-08-02 14:13:54.083 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 14:13:54.083 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 14:13:54.084 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 14:13:54.090 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 14:13:54.090 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 14:13:54.096 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 14:13:54.097 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 14:13:54.097 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 14:13:54.103 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 14:13:54.103 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 14:13:54.104 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 14:13:54.110 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 14:13:54.110 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 14:13:54.116 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 14:13:54.117 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 14:13:54.123 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 14:13:54.123 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 14:13:54.129 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 14:13:54.129 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 14:13:54.135 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 14:13:54.136 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 14:13:54.141 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 14:13:54.142 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 14:13:54.148 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 14:13:54.149 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 14:13:54.154 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 14:13:54.155 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 14:13:54.159 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 14:13:54.160 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 14:13:54.166 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 14:13:54.167 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 14:13:54.172 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 14:13:54.173 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 14:13:54.178 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 14:13:54.178 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 14:13:54.184 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 14:13:54.185 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 14:13:54.190 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 14:13:54.191 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 14:13:54.196 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 14:13:54.197 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 14:13:54.202 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 14:13:54.202 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 14:13:54.208 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 14:13:54.209 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 14:13:54.210 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 14:13:54.217 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 14:13:54.218 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 14:13:54.219 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 14:13:54.219 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:54.226 [Information] CANRegisterAccess: Read value 0x9A from register 0x0141 (simulated)
2025-08-02 14:13:54.228 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 14:13:54.228 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 14:13:54.229 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 14:13:54.229 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 14:13:54.235 [Information] CANRegisterAccess: Read value 0x33 from register 0x0140 (simulated)
2025-08-02 14:13:54.235 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 14:13:54.236 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 14:13:54.238 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 14:13:54.239 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 14:13:54.250 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 14:13:54.251 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 14:13:54.251 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 14:13:54.257 [Information] VocomService: Sending data and waiting for response
2025-08-02 14:13:54.258 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 14:13:54.308 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 14:13:54.311 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 14:13:54.312 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 14:13:54.314 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 14:13:54.314 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 14:13:54.326 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 14:13:54.328 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 14:13:54.329 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 14:13:54.340 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 14:13:54.351 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 14:13:54.362 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 14:13:54.373 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 14:13:54.384 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 14:13:54.386 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 14:13:54.386 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 14:13:54.397 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 14:13:54.398 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 14:13:54.398 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 14:13:54.409 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 14:13:54.419 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 14:13:54.430 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 14:13:54.441 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 14:13:54.452 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 14:13:54.463 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 14:13:54.466 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 14:13:54.466 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 14:13:54.478 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 14:13:54.479 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 14:13:54.480 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 14:13:54.480 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 14:13:54.480 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 14:13:54.481 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 14:13:54.481 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 14:13:54.481 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 14:13:54.481 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 14:13:54.482 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 14:13:54.482 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 14:13:54.482 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 14:13:54.482 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 14:13:54.483 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 14:13:54.483 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 14:13:54.483 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 14:13:54.483 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 14:13:54.584 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 14:13:54.584 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 14:13:54.588 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 14:13:54.589 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:54.590 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 14:13:54.590 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 14:13:54.591 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:54.591 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 14:13:54.591 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 14:13:54.592 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:54.594 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 14:13:54.594 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 14:13:54.595 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:54.595 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 14:13:54.596 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 14:13:54.597 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-08-02 14:13:54.598 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-02 14:13:54.598 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-02 14:13:54.602 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-02 14:13:54.604 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-02 14:13:54.609 [Information] BackupService: Initializing backup service
2025-08-02 14:13:54.609 [Information] BackupService: Backup service initialized successfully
2025-08-02 14:13:54.609 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-02 14:13:54.610 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-02 14:13:54.613 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-02 14:13:54.662 [Information] BackupService: Compressing backup data
2025-08-02 14:13:54.674 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-08-02 14:13:54.676 [Information] BackupServiceFactory: Created template for category: Production
2025-08-02 14:13:54.678 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-02 14:13:54.680 [Information] BackupService: Compressing backup data
2025-08-02 14:13:54.681 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-08-02 14:13:54.682 [Information] BackupServiceFactory: Created template for category: Development
2025-08-02 14:13:54.682 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-02 14:13:54.683 [Information] BackupService: Compressing backup data
2025-08-02 14:13:54.684 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (442 bytes)
2025-08-02 14:13:54.684 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-02 14:13:54.685 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-02 14:13:54.685 [Information] BackupService: Compressing backup data
2025-08-02 14:13:54.686 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-08-02 14:13:54.687 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-02 14:13:54.687 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-02 14:13:54.688 [Information] BackupService: Compressing backup data
2025-08-02 14:13:54.690 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-08-02 14:13:54.690 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-02 14:13:54.690 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-02 14:13:54.691 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-02 14:13:54.691 [Information] BackupService: Compressing backup data
2025-08-02 14:13:54.692 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-08-02 14:13:54.696 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-02 14:13:54.696 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-02 14:13:54.698 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-02 14:13:54.702 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 14:13:54.704 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 14:13:54.792 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 14:13:54.795 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 14:13:54.796 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-02 14:13:54.797 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-02 14:13:54.797 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-02 14:13:54.799 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-02 14:13:54.799 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-02 14:13:54.803 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-02 14:13:54.804 [Information] App: Flash operation monitor service initialized successfully
2025-08-02 14:13:54.817 [Information] LicensingService: Initializing licensing service
2025-08-02 14:13:54.872 [Information] LicensingService: License information loaded successfully
2025-08-02 14:13:54.874 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-02 14:13:54.874 [Information] App: Licensing service initialized successfully
2025-08-02 14:13:54.875 [Information] App: License status: Trial
2025-08-02 14:13:54.875 [Information] App: Trial period: 24 days remaining
2025-08-02 14:13:54.876 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-02 14:13:54.907 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-02 14:13:55.089 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 14:13:55.090 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 14:13:55.090 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 14:13:55.090 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 14:13:55.091 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 14:13:55.091 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 14:13:55.092 [Information] VocomService: Native USB communication service initialized
2025-08-02 14:13:55.092 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 14:13:55.092 [Information] VocomService: Connection recovery service initialized
2025-08-02 14:13:55.093 [Information] VocomService: Enhanced services initialization completed
2025-08-02 14:13:55.093 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:13:55.115 [Information] VocomService: PTT application is not running
2025-08-02 14:13:55.115 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:55.124 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:55.125 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 14:13:55.178 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 14:13:55.179 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 14:13:55.179 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 14:13:55.179 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 14:13:55.180 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-02 14:13:55.180 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: d7774d57-195f-4641-a358-9992158dfc6f
2025-08-02 14:13:55.182 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-02 14:13:55.182 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 14:13:55.183 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 14:13:55.183 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 14:13:55.185 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 14:13:55.186 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 14:13:55.189 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 14:13:55.190 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 14:13:55.190 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 14:13:55.221 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 14:13:55.222 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 14:13:55.222 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 14:13:55.222 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 14:13:55.223 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 14:13:55.223 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 14:13:55.223 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 14:13:55.223 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 14:13:55.224 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 14:13:55.224 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 14:13:55.224 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 14:13:55.225 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 14:13:55.225 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 14:13:55.225 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 14:13:55.225 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 14:13:55.226 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 14:13:55.226 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 14:13:55.226 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 14:13:55.233 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 14:13:55.234 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 14:13:55.234 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 14:13:55.234 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:55.240 [Information] CANRegisterAccess: Read value 0x34 from register 0x0141 (simulated)
2025-08-02 14:13:55.246 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:55.252 [Information] CANRegisterAccess: Read value 0xEE from register 0x0141 (simulated)
2025-08-02 14:13:55.257 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:55.263 [Information] CANRegisterAccess: Read value 0x32 from register 0x0141 (simulated)
2025-08-02 14:13:55.269 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:55.275 [Information] CANRegisterAccess: Read value 0x63 from register 0x0141 (simulated)
2025-08-02 14:13:55.276 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 14:13:55.276 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 14:13:55.277 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 14:13:55.283 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 14:13:55.284 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 14:13:55.289 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 14:13:55.290 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 14:13:55.290 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 14:13:55.297 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 14:13:55.297 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 14:13:55.298 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 14:13:55.304 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 14:13:55.305 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 14:13:55.311 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 14:13:55.312 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 14:13:55.318 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 14:13:55.319 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 14:13:55.324 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 14:13:55.325 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 14:13:55.331 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 14:13:55.332 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 14:13:55.338 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 14:13:55.339 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 14:13:55.345 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 14:13:55.346 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 14:13:55.351 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 14:13:55.352 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 14:13:55.358 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 14:13:55.359 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 14:13:55.365 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 14:13:55.366 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 14:13:55.372 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 14:13:55.373 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 14:13:55.379 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 14:13:55.380 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 14:13:55.386 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 14:13:55.387 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 14:13:55.394 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 14:13:55.395 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 14:13:55.401 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 14:13:55.402 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 14:13:55.407 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 14:13:55.408 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 14:13:55.414 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 14:13:55.415 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 14:13:55.415 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 14:13:55.421 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 14:13:55.422 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 14:13:55.422 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 14:13:55.422 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 14:13:55.428 [Information] CANRegisterAccess: Read value 0x2E from register 0x0141 (simulated)
2025-08-02 14:13:55.429 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 14:13:55.429 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 14:13:55.429 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 14:13:55.430 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 14:13:55.436 [Information] CANRegisterAccess: Read value 0xAA from register 0x0140 (simulated)
2025-08-02 14:13:55.441 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 14:13:55.447 [Information] CANRegisterAccess: Read value 0xEB from register 0x0140 (simulated)
2025-08-02 14:13:55.453 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 14:13:55.459 [Information] CANRegisterAccess: Read value 0xF3 from register 0x0140 (simulated)
2025-08-02 14:13:55.460 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 14:13:55.460 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 14:13:55.461 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 14:13:55.461 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 14:13:55.472 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 14:13:55.473 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 14:13:55.473 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 14:13:55.473 [Information] VocomService: Sending data and waiting for response
2025-08-02 14:13:55.474 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 14:13:55.523 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 14:13:55.524 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 14:13:55.524 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 14:13:55.525 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 14:13:55.525 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 14:13:55.536 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 14:13:55.537 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 14:13:55.537 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 14:13:55.548 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 14:13:55.562 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 14:13:55.573 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 14:13:55.584 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 14:13:55.595 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 14:13:55.596 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 14:13:55.596 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 14:13:55.607 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 14:13:55.608 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 14:13:55.608 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 14:13:55.619 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 14:13:55.630 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 14:13:55.641 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 14:13:55.652 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 14:13:55.663 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 14:13:55.674 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 14:13:55.674 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 14:13:55.675 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 14:13:55.686 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 14:13:55.686 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 14:13:55.687 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 14:13:55.687 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 14:13:55.687 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 14:13:55.688 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 14:13:55.688 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 14:13:55.688 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 14:13:55.689 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 14:13:55.689 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 14:13:55.689 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 14:13:55.689 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 14:13:55.690 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 14:13:55.690 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 14:13:55.690 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 14:13:55.691 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 14:13:55.691 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 14:13:55.792 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 14:13:55.792 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 14:13:55.793 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 14:13:55.794 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:55.794 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 14:13:55.794 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 14:13:55.795 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:55.795 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 14:13:55.795 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 14:13:55.796 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:55.796 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 14:13:55.796 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 14:13:55.797 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 14:13:55.797 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 14:13:55.797 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 14:13:55.866 [Information] BackupService: Initializing backup service
2025-08-02 14:13:55.866 [Information] BackupService: Backup service initialized successfully
2025-08-02 14:13:55.942 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 14:13:55.943 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 14:13:55.947 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 14:13:55.948 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 14:13:56.000 [Information] BackupService: Getting predefined backup categories
2025-08-02 14:13:56.051 [Information] MainViewModel: Services initialized successfully
2025-08-02 14:13:56.054 [Information] MainViewModel: Scanning for Vocom devices
2025-08-02 14:13:56.056 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 14:13:56.056 [Information] VocomService: Using new enhanced device detection service
2025-08-02 14:13:56.056 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 14:13:56.057 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 14:13:56.312 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 14:13:56.313 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 14:13:56.313 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 14:13:56.313 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 14:13:56.314 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 14:13:56.314 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 14:13:56.314 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 14:13:56.315 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 14:13:56.621 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 14:13:56.621 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 14:13:56.622 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 14:13:56.622 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 14:13:56.623 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 14:13:56.634 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 14:13:56.635 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 14:13:56.635 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 14:13:56.636 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 14:13:56.636 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 14:13:56.638 [Debug] VocomService: Bluetooth is enabled
2025-08-02 14:13:56.638 [Debug] VocomService: Checking if WiFi is available
2025-08-02 14:13:56.651 [Debug] VocomService: WiFi is available
2025-08-02 14:13:56.662 [Information] VocomService: Found 3 Vocom devices
2025-08-02 14:13:56.663 [Information] MainViewModel: Found 3 Vocom device(s)
2025-08-02 14:14:06.559 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-08-02 14:14:06.560 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:06.562 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-08-02 14:14:06.563 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-08-02 14:14:06.969 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-08-02 14:14:06.970 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-08-02 14:14:06.971 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 14:14:06.975 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 14:14:06.976 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 14:14:06.976 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-08-02 14:14:06.977 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 14:14:06.977 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 14:14:06.977 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 14:14:06.978 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:14:07.009 [Information] VocomService: PTT application is not running
2025-08-02 14:14:07.010 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 14:14:07.010 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 14:14:07.010 [Information] VocomService: Checking if PTT application is running
2025-08-02 14:14:07.030 [Information] VocomService: PTT application is not running
2025-08-02 14:14:07.031 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 14:14:07.031 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 14:14:07.032 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:14:07.032 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:14:07.033 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:07.033 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:07.033 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:07.033 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:07.034 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:07.034 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:07.034 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:07.034 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:07.035 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:07.035 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:14:07.035 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:07.035 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:07.036 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:07.036 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:07.036 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:07.036 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:14:07.037 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:14:07.037 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:14:07.037 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:14:07.037 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:14:07.038 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:14:07.038 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:14:07.038 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 14:14:08.047 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 14:14:08.047 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:14:08.048 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:14:08.049 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:08.049 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:08.049 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:08.050 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:08.050 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:08.050 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:08.051 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:08.051 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:08.051 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:08.052 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:14:08.052 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:08.052 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:08.052 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:08.053 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:08.053 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:08.053 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:14:08.054 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:14:08.054 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:14:08.054 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:14:08.054 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:14:08.055 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:14:08.055 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:14:08.055 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 14:14:09.064 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 14:14:09.064 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 14:14:09.065 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 14:14:09.066 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:09.066 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:09.067 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:09.067 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:09.067 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:09.067 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:09.068 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:09.068 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:09.068 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:09.069 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 14:14:09.069 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:09.069 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:09.069 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 14:14:09.070 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 14:14:09.070 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 14:14:09.070 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 14:14:09.071 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:14:09.071 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 14:14:09.071 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 14:14:09.072 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 14:14:09.072 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 14:14:09.072 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 14:14:09.072 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 14:14:09.073 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 14:14:09.073 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 14:14:09.074 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 14:14:09.074 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 14:14:09.074 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 14:14:09.074 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 14:14:09.075 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 14:14:09.075 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 14:14:09.075 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 14:14:09.076 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 14:14:09.076 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 14:14:09.076 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 14:14:09.077 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 14:14:09.077 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 14:14:09.077 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 14:14:09.078 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 14:14:09.078 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:14:09.078 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:14:09.079 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:14:09.079 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:14:09.080 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:14:09.080 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 14:14:09.080 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:09.081 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:09.081 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:09.081 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:09.082 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:09.082 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 14:14:09.083 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
