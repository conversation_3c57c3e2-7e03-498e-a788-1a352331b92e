@echo off
title VolvoFlashWR - Integrated VCRedist and Architecture Bridge Fix
echo ===============================================
echo VolvoFlashWR - Integrated Startup
echo ===============================================
echo.

echo Setting up VolvoFlashWR environment with integrated fixes...
echo - Visual C++ Redistributable auto-download
echo - Architecture bridge for x86/x64 compatibility
echo - Enhanced library loading
echo.

REM Add current directory to PATH for immediate library access
set PATH=%CD%;%CD%\Libraries;%CD%\Libraries\VCRedist;%CD%\Bridge;%PATH%

REM Set integrated environment variables
set FORCE_ARCHITECTURE_BRIDGE=true
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%CD%\Libraries
set VCREDIST_AUTO_DOWNLOAD=true
set INTEGRATED_STARTUP=true

echo Environment configured for integrated compatibility
echo.

REM Check system for msvcr140.dll first
echo Checking Visual C++ Runtime libraries...
setlocal enabledelayedexpansion
set MSVCR140_FOUND=0
if exist "C:\Windows\System32\msvcr140.dll" set MSVCR140_FOUND=1
if exist "C:\Windows\SysWOW64\msvcr140.dll" set MSVCR140_FOUND=1
if exist "msvcr140.dll" set MSVCR140_FOUND=1

if %MSVCR140_FOUND%==1 (
    echo ✓ Visual C++ Runtime libraries detected
) else (
    echo ❌ Visual C++ Runtime libraries NOT detected
    echo    This will cause the application to fail with library errors
    echo.
    echo 🔧 Running automatic Visual C++ Redistributable installer...

    REM Run the PowerShell installer
    powershell -ExecutionPolicy Bypass -File "Install_VCRedist_Simple.ps1" -Quiet

    if !ERRORLEVEL! EQU 0 (
        echo ✅ Visual C++ Redistributable installation completed successfully
        echo.

        REM Check again after installation
        set MSVCR140_FOUND=0
        if exist "C:\Windows\System32\msvcr140.dll" set MSVCR140_FOUND=1
        if exist "C:\Windows\SysWOW64\msvcr140.dll" set MSVCR140_FOUND=1

        if !MSVCR140_FOUND!==1 (
            echo ✅ Visual C++ libraries now available
        ) else (
            echo ⚠️  Visual C++ libraries still not detected - restart may be required
        )
    ) else (
        echo ❌ Visual C++ Redistributable installation failed
        echo    The application may still encounter library errors
        echo.
        echo 💡 You can try:
        echo    1. Running this script as Administrator
        echo    2. Manually downloading from: https://aka.ms/vs/17/release/vc_redist.x64.exe
        echo    3. Restarting your computer after installation
        echo.
        set /p CONTINUE="Continue anyway? (y/N): "
        if /i "!CONTINUE!" NEQ "y" (
            echo Startup cancelled by user
            pause
            exit /b 1
        )
    )
)

echo.

REM Check if bridge executable exists
if exist "Bridge\VolvoFlashWR.VocomBridge.exe" (
    echo ✓ Architecture bridge available
) else (
    echo ⚠ Architecture bridge not found - may have compatibility issues
)

REM Check for critical libraries
if exist "Libraries\apci.dll" (
    echo ✓ APCI library found
) else (
    echo ⚠ APCI library not found
)

echo.
echo 🚀 Starting VolvoFlashWR with integrated fixes...
echo The application will automatically:
echo - Handle library extraction and dependency management
echo - Use architecture bridge for x86/x64 compatibility
echo - Manage Vocom hardware communication setup
echo.

REM Try to run the application
if exist "VolvoFlashWR.Launcher.exe" (
    echo Starting VolvoFlashWR.Launcher.exe...
    "VolvoFlashWR.Launcher.exe"
) else if exist "VolvoFlashWR.UI.exe" (
    echo Starting VolvoFlashWR.UI.exe...
    "VolvoFlashWR.UI.exe"
) else (
    echo ERROR: No VolvoFlashWR executable found!
    echo.
    echo Expected files:
    echo - VolvoFlashWR.Launcher.exe
    echo - VolvoFlashWR.UI.exe
    echo.
    pause
    exit /b 1
)

REM Check exit code and provide helpful information
if errorlevel 1 (
    echo.
    echo ===============================================
    echo Application exited with error code: %errorlevel%
    echo ===============================================
    echo.
    echo Check the Logs folder for detailed error information.
    echo.
    echo If you still experience issues:
    echo 1. Check the latest log file in the Logs folder
    echo 2. Ensure you have internet connection for library downloads
    echo 3. Run as Administrator if permission issues occur
    echo 4. Check Windows Update for system updates
    echo.
    echo The integrated system should handle most compatibility issues automatically.
    echo.
    pause
) else (
    echo.
    echo Application started successfully!
)
