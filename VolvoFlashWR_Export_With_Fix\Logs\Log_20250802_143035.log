Log started at 8/2/2025 2:30:35 PM
2025-08-02 14:30:35.865 [Information] LoggingService: Logging service initialized
2025-08-02 14:30:35.881 [Information] App: Starting integrated application initialization
2025-08-02 14:30:35.883 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-02 14:30:35.885 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-02 14:30:35.887 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-02 14:30:35.889 [Information] IntegratedStartupService: Setting up application environment
2025-08-02 14:30:35.890 [Information] IntegratedStartupService: Application environment setup completed
2025-08-02 14:30:35.892 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-02 14:30:35.895 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-02 14:30:35.900 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-02 14:30:35.909 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-02 14:32:22.852 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-02 14:32:24.866 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 14:32:24.869 [Warning] X64LibraryResolver: ⚠ Library architecture mismatch: msvcp140.dll
2025-08-02 14:32:24.886 [Information] X64LibraryResolver: ✓ Copied from system: msvcp140.dll
2025-08-02 14:32:24.889 [Warning] X64LibraryResolver: ⚠ Library architecture mismatch: vcruntime140.dll
2025-08-02 14:32:24.892 [Information] X64LibraryResolver: ✓ Copied from system: vcruntime140.dll
2025-08-02 14:32:24.896 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-02 14:32:24.899 [Information] X64LibraryResolver: Found APCI library: apci.dll (x86)
2025-08-02 14:32:24.900 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apci.dll is x86, process is x64
2025-08-02 14:32:24.901 [Information] X64LibraryResolver: Found APCI library: apcidb.dll (x86)
2025-08-02 14:32:24.902 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apcidb.dll is x86, process is x64
2025-08-02 14:32:24.905 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlus.dll (x86)
2025-08-02 14:32:24.906 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 14:32:24.908 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlusData.dll (x86)
2025-08-02 14:32:24.908 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 14:32:24.910 [Information] X64LibraryResolver: Setting up architecture bridge for x86 library compatibility
2025-08-02 14:32:24.912 [Information] X64LibraryResolver: Found architecture bridge: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe (x86)
2025-08-02 14:32:24.913 [Information] X64LibraryResolver: ✓ Architecture bridge is properly configured
2025-08-02 14:32:24.914 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-02 14:32:24.922 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 14:32:24.923 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-02 14:32:24.923 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-02 14:32:24.924 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:32:24.925 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 14:32:24.925 [Information] X64LibraryResolver: Library resolution completed. Success: True
2025-08-02 14:32:24.927 [Information] IntegratedStartupService: x64 library resolution completed successfully
2025-08-02 14:32:24.928 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-02 14:32:24.929 [Information] IntegratedStartupService: Missing libraries: 1
2025-08-02 14:32:24.929 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-02 14:32:24.930 [Information] IntegratedStartupService: Incompatible libraries: 4
2025-08-02 14:32:24.930 [Information] IntegratedStartupService: Architecture bridge required: True
2025-08-02 14:32:24.930 [Information] IntegratedStartupService: Bridge path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 14:32:24.931 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 14:32:24.932 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-02 14:32:24.933 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-02 14:32:24.933 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 14:32:24.934 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 14:32:24.937 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-02 14:32:24.942 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-02 14:32:24.945 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-02 14:32:24.952 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-02 14:32:24.967 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:24.976 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:24.979 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:24.980 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-02 14:32:24.986 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:24.990 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:24.992 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:24.993 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:32:24.997 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:25.000 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.004 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.005 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:32:25.010 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:25.012 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.015 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.016 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:32:25.022 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:25.025 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.028 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.028 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:32:25.032 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:25.037 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.040 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.041 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:32:25.048 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 14:32:25.056 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.059 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 14:32:25.062 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:32:25.065 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-02 14:32:25.066 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-02 14:32:25.070 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-02 14:32:25.072 [Information] VCRedistBundler: Found 7 missing VC++ libraries: msvcr140.dll, api-ms-win-crt-runtime-l1-1-0.dll, api-ms-win-crt-heap-l1-1-0.dll, api-ms-win-crt-string-l1-1-0.dll, api-ms-win-crt-stdio-l1-1-0.dll, api-ms-win-crt-math-l1-1-0.dll, api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:32:25.074 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-02 14:32:25.078 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 14:33:50.938 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-02 14:33:50.945 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_e2b5d3f5
2025-08-02 14:34:03.892 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-02 14:34:03.894 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-02 14:34:03.896 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-02 14:34:03.900 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-02 14:34:03.900 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-02 14:34:03.902 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-02 14:34:03.902 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-02 14:34:03.904 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-02 14:34:03.915 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-02 14:34:03.915 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-02 14:34:03.917 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-02 14:34:03.918 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 14:34:03.919 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 14:34:03.920 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 14:34:03.921 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 14:34:03.921 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 14:34:03.921 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 14:34:03.929 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-02 14:34:03.929 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-02 14:34:03.931 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-02 14:34:03.934 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-02 14:34:03.936 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-02 14:34:03.938 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-02 14:34:03.939 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-02 14:34:03.940 [Warning] IntegratedStartupService: Architecture mismatch detected for apci.dll - x86 library in x64 process
2025-08-02 14:34:03.942 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlus.dll - x86 library in x64 process
2025-08-02 14:34:03.945 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlusData.dll - x86 library in x64 process
2025-08-02 14:34:03.946 [Information] IntegratedStartupService: Architecture mismatch detected - setting up bridge environment
2025-08-02 14:34:03.947 [Information] IntegratedStartupService: Architecture bridge executable found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 14:34:03.950 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-02 14:34:03.953 [Information] LibraryExtractor: Starting library extraction process
2025-08-02 14:34:03.958 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-02 14:34:03.963 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-02 14:34:03.965 [Information] LibraryExtractor: Copying system libraries
2025-08-02 14:34:03.974 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-02 14:34:03.980 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 14:36:13.833 [Warning] LibraryExtractor: Extraction process timed out for msvcr140.dll
2025-08-02 14:36:13.847 [Debug] LibraryExtractor: Cleanup warning for msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_faf83cca-15af-4f3b-b84b-e0b77032fa24.exe' is denied.
2025-08-02 14:36:14.848 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 14:38:14.873 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-02 14:38:15.875 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 14:40:15.882 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-02 14:40:15.883 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 14:41:12.383 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 14:41:13.388 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 14:41:59.753 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 14:42:00.756 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 14:42:51.102 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 14:42:51.106 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-02 14:42:51.106 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 14:44:27.505 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 14:44:28.512 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 14:45:56.298 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 14:45:57.304 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 14:47:42.103 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 14:47:42.112 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 14:48:34.250 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 14:48:35.253 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 14:49:29.614 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 14:49:30.619 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 14:50:25.448 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 14:50:25.453 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-02 14:50:25.453 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 14:52:04.711 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 14:52:05.719 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 14:53:56.271 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 14:53:57.278 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 14:55:38.752 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 14:55:38.759 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 14:56:41.772 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 14:56:42.776 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 14:57:31.419 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 14:57:32.424 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 14:58:18.092 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 14:58:18.098 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-02 14:58:18.099 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 14:59:51.439 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 14:59:52.445 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:01:45.273 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 15:01:46.281 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:03:46.284 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-string-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-02 15:03:46.286 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 15:04:36.667 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 15:04:37.677 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:05:24.332 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 15:05:25.337 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:06:11.421 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 15:06:11.428 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-02 15:06:11.429 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:07:54.547 [Warning] LibraryExtractor: Target library api-ms-win-crt-stdio-l1-1-0.dll not found in extracted files
2025-08-02 15:07:55.556 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-stdio-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:09:38.213 [Warning] LibraryExtractor: Target library api-ms-win-crt-stdio-l1-1-0.dll not found in extracted files
2025-08-02 15:09:39.218 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-stdio-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:11:31.931 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 15:11:31.933 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-stdio-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_862b8fd9-bc21-4a50-aac1-90d7923072b5.exe' is denied.
2025-08-02 15:11:31.933 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 15:12:19.732 [Warning] LibraryExtractor: Target library api-ms-win-crt-stdio-l1-1-0.dll not found in extracted files
2025-08-02 15:12:20.737 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-stdio-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:13:08.677 [Warning] LibraryExtractor: Target library api-ms-win-crt-stdio-l1-1-0.dll not found in extracted files
2025-08-02 15:13:09.683 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-stdio-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:13:57.313 [Warning] LibraryExtractor: Target library api-ms-win-crt-stdio-l1-1-0.dll not found in extracted files
2025-08-02 15:13:57.320 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-stdio-l1-1-0.dll from all available URLs
2025-08-02 15:13:57.320 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-02 15:13:57.321 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-02 15:13:57.327 [Information] LibraryExtractor: Verifying library extraction
2025-08-02 15:13:57.328 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-02 15:13:57.328 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-02 15:13:57.329 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-02 15:13:57.329 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-02 15:13:57.329 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-02 15:13:57.335 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-02 15:13:57.337 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-02 15:13:57.340 [Information] DependencyManager: Initializing dependency manager
2025-08-02 15:13:57.341 [Information] DependencyManager: Setting up library search paths
2025-08-02 15:13:57.346 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:13:57.346 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 15:13:57.347 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 15:13:57.347 [Information] DependencyManager: Updated PATH environment variable
2025-08-02 15:13:57.349 [Information] DependencyManager: Verifying required directories
2025-08-02 15:13:57.350 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:13:57.350 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 15:13:57.350 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-02 15:13:57.351 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 15:13:57.353 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-02 15:13:57.414 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-08-02 15:13:57.463 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 15:13:57.463 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 15:13:57.567 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 15:13:57.572 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-08-02 15:13:57.574 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 15:13:57.574 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 15:13:57.581 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 15:13:57.584 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 15:13:57.585 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 15:13:57.806 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 15:13:57.824 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 15:13:57.825 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:13:57.825 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:13:57.826 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 15:13:57.827 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 15:13:57.827 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 15:13:57.828 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 15:13:57.829 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 15:13:57.829 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 15:13:57.830 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 15:13:57.830 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 15:13:57.831 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 15:13:57.831 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 15:13:57.832 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 15:13:57.832 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 15:13:57.833 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 15:13:57.833 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 15:13:57.834 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 15:13:57.835 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 15:13:57.835 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-02 15:13:57.835 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-02 15:13:57.838 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-02 15:13:57.840 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-02 15:13:57.841 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 15:13:57.842 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-02 15:13:57.842 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 15:13:57.843 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 15:13:57.861 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-02 15:13:57.862 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 15:13:57.862 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-02 15:13:57.863 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 15:13:57.864 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-02 15:13:57.864 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-02 15:13:57.865 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 15:13:57.866 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-02 15:13:57.867 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 15:13:57.868 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-02 15:13:57.869 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-02 15:13:57.870 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 15:13:57.871 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-02 15:13:57.872 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 15:13:57.872 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-02 15:13:57.874 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-02 15:13:57.874 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-02 15:13:57.875 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 15:13:57.876 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 15:13:57.877 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-08-02 15:13:57.877 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 15:13:57.878 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 15:13:57.878 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 15:13:57.879 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-08-02 15:13:57.879 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 15:13:57.880 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 15:13:57.880 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 15:13:57.881 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-02 15:13:57.882 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 15:13:57.882 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 15:13:57.883 [Information] DependencyManager: Setting up environment variables
2025-08-02 15:13:57.883 [Information] DependencyManager: Environment variables configured
2025-08-02 15:13:57.885 [Information] DependencyManager: Verifying library loading status
2025-08-02 15:13:58.342 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-08-02 15:13:58.344 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-02 15:13:58.344 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-02 15:13:58.347 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-02 15:13:58.349 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-02 15:13:58.369 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-02 15:13:58.372 [Information] IntegratedStartupService: Verifying system readiness
2025-08-02 15:13:58.372 [Information] IntegratedStartupService: System readiness verification passed
2025-08-02 15:13:58.372 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-02 15:13:58.374 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-02 15:13:58.375 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 15:13:58.375 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:13:58.375 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 15:13:58.375 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-02 15:13:58.376 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-02 15:13:58.376 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-02 15:13:58.376 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:13:58.377 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-02 15:13:58.377 [Information] App: Integrated startup completed successfully
2025-08-02 15:13:58.380 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-02 15:13:58.420 [Information] App: Initializing application services
2025-08-02 15:13:58.423 [Information] AppConfigurationService: Initializing configuration service
2025-08-02 15:13:58.424 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 15:13:58.538 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 15:13:58.539 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-02 15:13:58.540 [Information] App: Configuration service initialized successfully
2025-08-02 15:13:58.542 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-02 15:13:58.542 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-02 15:13:58.543 [Information] App: Environment variable exists: True, not 'false': False
2025-08-02 15:13:58.543 [Information] App: Final useDummyImplementations value: False
2025-08-02 15:13:58.543 [Information] App: Updating config to NOT use dummy implementations
2025-08-02 15:13:58.545 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-02 15:13:58.577 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 15:13:58.579 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-02 15:13:58.580 [Information] App: usePatchedImplementation flag is: True
2025-08-02 15:13:58.580 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-02 15:13:58.581 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-02 15:13:58.581 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-02 15:13:58.581 [Information] App: verboseLogging flag is: True
2025-08-02 15:13:58.584 [Information] App: Verifying real hardware requirements...
2025-08-02 15:13:58.584 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-02 15:13:58.584 [Information] App: ✓ Found critical library: apci.dll
2025-08-02 15:13:58.585 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-02 15:13:58.586 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-02 15:13:58.588 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 15:13:58.588 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-02 15:13:58.589 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-02 15:13:58.589 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-02 15:13:58.602 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-02 15:13:58.607 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-02 15:13:58.607 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-02 15:13:58.621 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-02 15:13:58.626 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-02 15:16:05.134 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 15:16:05.135 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-02 15:16:05.135 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-02 15:16:05.136 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:16:05.136 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 15:16:05.137 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 15:16:05.137 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 15:16:05.138 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 15:16:05.138 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 15:16:05.138 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-02 15:16:05.142 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-02 15:16:05.142 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-02 15:16:05.142 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 15:16:05.143 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:16:05.143 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-02 15:16:05.143 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-02 15:16:05.144 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-02 15:16:05.144 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-02 15:16:05.147 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-02 15:16:05.147 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-02 15:16:05.151 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-02 15:16:05.155 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-02 15:16:05.156 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-02 15:16:05.157 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-02 15:16:05.159 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-02 15:16:05.159 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-08-02 15:16:05.162 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-08-02 15:16:05.163 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-08-02 15:16:05.166 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-08-02 15:16:05.166 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-08-02 15:16:05.209 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-08-02 15:16:05.209 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-08-02 15:16:05.216 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-08-02 15:16:05.217 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-08-02 15:16:05.218 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-08-02 15:16:05.219 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-08-02 15:16:05.224 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-08-02 15:16:05.224 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-08-02 15:16:05.225 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-08-02 15:16:05.225 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:16:05.225 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-08-02 15:16:05.226 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-08-02 15:16:05.226 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-08-02 15:16:05.232 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-08-02 15:16:05.239 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-08-02 15:16:05.267 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-08-02 15:16:05.268 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 15:16:05.269 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-08-02 15:16:05.302 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 15:16:05.366 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-08-02 15:16:05.367 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-08-02 15:16:05.368 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-08-02 15:16:05.370 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 15:16:05.370 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-08-02 15:16:05.371 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 15:16:05.371 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-08-02 15:16:05.372 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 15:16:05.373 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-08-02 15:16:05.374 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-08-02 15:16:05.374 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-08-02 15:16:05.375 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-08-02 15:16:05.377 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-08-02 15:16:05.378 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-08-02 15:16:05.378 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-08-02 15:16:05.382 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-08-02 15:16:05.383 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-08-02 15:16:05.385 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 15:16:05.385 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-08-02 15:16:05.386 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-08-02 15:16:05.389 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-08-02 15:16:05.390 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-08-02 15:16:05.391 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-08-02 15:16:05.391 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-08-02 15:16:05.392 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-08-02 15:16:05.392 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-08-02 15:16:05.392 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-08-02 15:16:05.393 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-08-02 15:16:05.394 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-08-02 15:16:05.394 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-08-02 15:16:05.397 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-08-02 15:16:05.397 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-08-02 15:16:05.398 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-08-02 15:16:05.398 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:16:05.399 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-08-02 15:16:05.401 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-08-02 15:16:05.402 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 15:16:05.402 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-08-02 15:16:05.403 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-08-02 15:16:05.404 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-08-02 15:16:05.404 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-08-02 15:16:05.405 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-08-02 15:16:05.410 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-08-02 15:16:05.412 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-08-02 15:16:05.413 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-08-02 15:16:05.424 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-08-02 15:16:05.424 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-08-02 15:16:05.425 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 15:16:05.425 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 15:16:05.428 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-08-02 15:16:05.429 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.432 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.432 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-08-02 15:16:05.433 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.438 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.439 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-08-02 15:16:05.440 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.440 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-08-02 15:16:05.442 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.442 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-08-02 15:16:05.443 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.445 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.445 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-08-02 15:16:05.447 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.448 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.448 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-08-02 15:16:05.449 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 15:16:05.451 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 15:16:05.452 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.454 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.454 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-08-02 15:16:05.455 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:16:05.456 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.456 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-08-02 15:16:05.458 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.459 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.459 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-08-02 15:16:05.460 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 15:16:05.463 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 15:16:05.464 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-08-02 15:16:05.466 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 15:16:05.470 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 15:16:05.472 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 15:16:05.476 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-08-02 15:16:05.477 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 15:16:05.477 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-08-02 15:16:05.478 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-08-02 15:16:05.479 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-08-02 15:16:05.481 [Information] VocomDriver: Initializing Vocom driver
2025-08-02 15:16:05.484 [Information] VocomNativeInterop: Initializing Vocom driver
2025-08-02 15:16:05.488 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-08-02 15:16:05.489 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 15:16:05.489 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 15:16:05.491 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 15:16:05.491 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 15:17:09.542 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-08-02 15:17:10.928 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-08-02 15:17:10.937 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-08-02 15:17:10.958 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-08-02 15:17:10.959 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-08-02 15:17:10.959 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:17:10.967 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-08-02 15:17:10.971 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-08-02 15:17:10.975 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-08-02 15:17:10.976 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-08-02 15:17:10.977 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-08-02 15:17:10.978 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-08-02 15:17:10.980 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-08-02 15:17:10.981 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-08-02 15:17:10.981 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-08-02 15:17:10.982 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-08-02 15:17:10.982 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-08-02 15:17:10.983 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-08-02 15:17:10.984 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-08-02 15:17:10.985 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-08-02 15:17:10.985 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-08-02 15:17:10.987 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-08-02 15:17:10.987 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-08-02 15:17:10.987 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-08-02 15:17:10.988 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-08-02 15:17:10.988 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-08-02 15:17:10.990 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-08-02 15:17:10.991 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-08-02 15:17:10.993 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-08-02 15:17:10.993 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-08-02 15:17:10.993 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-08-02 15:17:10.997 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-08-02 15:17:10.998 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-08-02 15:17:11.003 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-08-02 15:17:11.006 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-08-02 15:17:11.007 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-08-02 15:17:11.008 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-08-02 15:17:11.009 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-08-02 15:17:11.009 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-08-02 15:17:11.010 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-08-02 15:17:11.010 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-08-02 15:17:11.010 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-08-02 15:17:11.011 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-08-02 15:17:11.011 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-08-02 15:17:11.011 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-08-02 15:17:11.012 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-08-02 15:17:11.012 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-08-02 15:17:11.012 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-08-02 15:17:11.013 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-08-02 15:17:11.013 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-08-02 15:17:11.013 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-08-02 15:17:11.014 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-08-02 15:17:11.015 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-08-02 15:17:11.015 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-08-02 15:17:11.016 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-08-02 15:17:11.017 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-08-02 15:17:11.018 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-08-02 15:17:11.019 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-08-02 15:17:11.020 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-08-02 15:17:11.020 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-08-02 15:17:11.021 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-08-02 15:17:11.021 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-08-02 15:17:11.021 [Information] VocomDriver: Vocom driver initialized successfully
2025-08-02 15:17:11.030 [Information] VocomService: Initializing Vocom service with dependencies
2025-08-02 15:17:11.035 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-08-02 15:17:11.038 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-08-02 15:17:11.040 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-08-02 15:17:11.159 [Information] WiFiCommunicationService: WiFi is available
2025-08-02 15:17:11.160 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-08-02 15:17:11.267 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-08-02 15:17:11.269 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-08-02 15:17:11.273 [Information] BluetoothCommunicationService: Bluetooth is available
2025-08-02 15:17:11.274 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-08-02 15:17:11.276 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 15:17:11.278 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 15:17:11.280 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 15:17:11.282 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 15:17:11.290 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 15:17:11.290 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 15:17:11.290 [Information] VocomService: Native USB communication service initialized
2025-08-02 15:17:11.291 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 15:17:11.292 [Information] VocomService: Connection recovery service initialized
2025-08-02 15:17:11.292 [Information] VocomService: Enhanced services initialization completed
2025-08-02 15:17:11.296 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:11.322 [Information] VocomService: PTT application is not running
2025-08-02 15:17:11.328 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:11.331 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:11.333 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 15:17:11.333 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-08-02 15:17:11.334 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-08-02 15:17:11.341 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 15:17:11.344 [Information] VocomService: Using new enhanced device detection service
2025-08-02 15:17:11.351 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 15:17:11.353 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 15:17:12.426 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 15:17:12.427 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 15:17:12.432 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 15:17:12.435 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 15:17:12.440 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 15:17:12.443 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 15:17:12.449 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 15:17:12.456 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 15:17:12.931 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 15:17:12.936 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 15:17:12.942 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 15:17:12.943 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 15:17:12.948 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 15:17:12.984 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 15:17:12.992 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 15:17:12.997 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 15:17:12.997 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 15:17:12.998 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:13.001 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:13.011 [Debug] VocomService: Checking if WiFi is available
2025-08-02 15:17:13.018 [Debug] VocomService: WiFi is available
2025-08-02 15:17:13.018 [Information] VocomService: Found 3 Vocom devices
2025-08-02 15:17:13.019 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-08-02 15:17:13.025 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 6ffe714d-9aab-4f03-ae3c-cf8f5ea7c6fc, Type: USB)
2025-08-02 15:17:13.026 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 70bca54f-17be-4d80-9da5-e0aa969b87c8, Type: Bluetooth)
2025-08-02 15:17:13.026 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 6039e732-7851-43d4-80d2-02a9734a5256, Type: WiFi)
2025-08-02 15:17:13.028 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-08-02 15:17:13.028 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-08-02 15:17:13.029 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-08-02 15:17:13.029 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-08-02 15:17:13.029 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-08-02 15:17:13.030 [Information] App: Architecture-aware Vocom service created successfully
2025-08-02 15:17:13.030 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 15:17:13.030 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 15:17:13.031 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 15:17:13.031 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 15:17:13.032 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 15:17:13.032 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 15:17:13.032 [Information] VocomService: Native USB communication service initialized
2025-08-02 15:17:13.033 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 15:17:13.033 [Information] VocomService: Connection recovery service initialized
2025-08-02 15:17:13.033 [Information] VocomService: Enhanced services initialization completed
2025-08-02 15:17:13.034 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:13.069 [Information] VocomService: PTT application is not running
2025-08-02 15:17:13.075 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:13.077 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:13.079 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 15:17:13.079 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-02 15:17:13.081 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-02 15:17:13.082 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-02 15:17:13.169 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 15:17:13.169 [Information] VocomService: Using new enhanced device detection service
2025-08-02 15:17:13.170 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 15:17:13.170 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 15:17:13.678 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 15:17:13.679 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 15:17:13.680 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 15:17:13.680 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 15:17:13.680 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 15:17:13.681 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 15:17:13.681 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 15:17:13.682 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 15:17:14.186 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 15:17:14.187 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 15:17:14.187 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 15:17:14.188 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 15:17:14.189 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 15:17:14.202 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 15:17:14.202 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 15:17:14.203 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 15:17:14.203 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 15:17:14.203 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:14.204 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:14.204 [Debug] VocomService: Checking if WiFi is available
2025-08-02 15:17:14.208 [Debug] VocomService: WiFi is available
2025-08-02 15:17:14.209 [Information] VocomService: Found 3 Vocom devices
2025-08-02 15:17:14.209 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-08-02 15:17:14.213 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:14.214 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:14.235 [Information] VocomService: PTT application is not running
2025-08-02 15:17:14.241 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 15:17:14.242 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 15:17:14.242 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:14.262 [Information] VocomService: PTT application is not running
2025-08-02 15:17:14.263 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 15:17:14.266 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 15:17:14.268 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 15:17:14.272 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 15:17:14.274 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:14.276 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:14.276 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:14.277 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:14.277 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:14.277 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:14.278 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:14.278 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:14.278 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:14.279 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 15:17:14.279 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:14.279 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:14.280 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:14.280 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:14.280 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:14.281 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 15:17:14.282 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:14.283 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 15:17:14.283 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:14.283 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 15:17:14.284 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 15:17:14.284 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 15:17:14.284 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 15:17:15.294 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 15:17:15.294 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 15:17:15.295 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 15:17:15.295 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:15.296 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:15.296 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:15.296 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:15.297 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:15.297 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:15.297 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:15.298 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:15.298 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:15.298 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 15:17:15.299 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:15.299 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:15.300 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:15.300 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:15.300 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:15.301 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 15:17:15.301 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:15.302 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 15:17:15.302 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:15.302 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 15:17:15.303 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 15:17:15.303 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 15:17:15.304 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 15:17:16.305 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 15:17:16.306 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 15:17:16.307 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 15:17:16.307 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:16.309 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:16.309 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:16.310 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:16.310 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:16.311 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:16.311 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:16.311 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:16.312 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:16.312 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 15:17:16.312 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:16.313 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:16.313 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:16.314 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:16.314 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:16.314 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 15:17:16.315 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:16.315 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 15:17:16.315 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:16.316 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 15:17:16.317 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 15:17:16.317 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 15:17:16.318 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 15:17:16.319 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 15:17:16.319 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 15:17:16.369 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 15:17:16.371 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 15:17:16.374 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 15:17:16.375 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 15:17:16.375 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 15:17:16.376 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 15:17:16.376 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 15:17:16.379 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 15:17:16.381 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 15:17:16.387 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 15:17:16.390 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 15:17:16.392 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 15:17:16.392 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 15:17:16.393 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 15:17:16.393 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 15:17:16.394 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:16.395 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-08-02 15:17:16.401 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-02 15:17:16.406 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:16.407 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-02 15:17:16.415 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 15:17:16.418 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 15:17:16.419 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 15:17:16.420 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 15:17:16.421 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 15:17:16.421 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 15:17:16.421 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 15:17:16.422 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 15:17:16.427 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 15:17:16.427 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 15:17:16.428 [Information] VocomService: Using new enhanced device detection service
2025-08-02 15:17:16.428 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 15:17:16.428 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 15:17:16.889 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 15:17:16.890 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 15:17:16.891 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 15:17:16.891 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 15:17:16.892 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 15:17:16.893 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 15:17:16.894 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 15:17:16.894 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 15:17:17.263 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 15:17:17.263 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 15:17:17.264 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 15:17:17.265 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 15:17:17.266 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 15:17:17.274 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 15:17:17.274 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 15:17:17.275 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 15:17:17.275 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 15:17:17.275 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:17.277 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:17.278 [Debug] VocomService: Checking if WiFi is available
2025-08-02 15:17:17.279 [Debug] VocomService: WiFi is available
2025-08-02 15:17:17.279 [Information] VocomService: Found 3 Vocom devices
2025-08-02 15:17:17.287 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:17.287 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:17.288 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:17.310 [Information] VocomService: PTT application is not running
2025-08-02 15:17:17.311 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 15:17:17.311 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 15:17:17.312 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:17.336 [Information] VocomService: PTT application is not running
2025-08-02 15:17:17.337 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 15:17:17.337 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 15:17:17.338 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 15:17:17.338 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 15:17:17.340 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:17.340 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:17.341 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:17.341 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:17.341 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:17.342 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:17.343 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:17.344 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:17.344 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:17.345 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 15:17:17.345 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:17.345 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:17.346 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:17.346 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:17.347 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:17.347 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 15:17:17.348 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:17.348 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 15:17:17.348 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:17.349 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 15:17:17.349 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 15:17:17.349 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 15:17:17.349 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 15:17:18.350 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 15:17:18.350 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 15:17:18.351 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 15:17:18.351 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:18.352 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:18.352 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:18.353 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:18.353 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:18.354 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:18.354 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:18.354 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:18.355 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:18.356 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 15:17:18.357 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:18.357 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:18.357 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:18.358 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:18.358 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:18.359 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 15:17:18.359 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:18.359 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 15:17:18.360 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:18.360 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 15:17:18.360 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 15:17:18.360 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 15:17:18.361 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 15:17:19.361 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 15:17:19.362 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 15:17:19.362 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 15:17:19.363 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:19.363 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:19.364 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:19.364 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:19.364 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:19.365 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:19.365 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:19.365 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:19.366 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:19.368 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 15:17:19.368 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:19.369 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:19.369 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 15:17:19.369 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 15:17:19.370 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 15:17:19.370 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 15:17:19.371 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:19.371 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 15:17:19.371 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 15:17:19.371 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 15:17:19.372 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 15:17:19.372 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 15:17:19.372 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 15:17:19.373 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 15:17:19.373 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 15:17:19.373 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 15:17:19.374 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 15:17:19.374 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 15:17:19.374 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 15:17:19.375 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 15:17:19.375 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 15:17:19.375 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 15:17:19.375 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 15:17:19.376 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 15:17:19.376 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 15:17:19.376 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 15:17:19.377 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 15:17:19.377 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 15:17:19.378 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 15:17:19.378 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 15:17:19.379 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 15:17:19.379 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:19.380 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:19.380 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 15:17:19.380 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 15:17:19.380 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 15:17:19.381 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:19.406 [Information] VocomService: PTT application is not running
2025-08-02 15:17:19.409 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 15:17:19.410 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:19.411 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:19.412 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-08-02 15:17:20.371 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 15:17:20.427 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 15:17:20.428 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-08-02 15:17:20.428 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 15:17:20.433 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 15:17:20.436 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 15:17:20.500 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 15:17:20.503 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 15:17:20.508 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 15:17:20.518 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 15:17:20.522 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 15:17:20.535 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 15:17:20.538 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 15:17:20.548 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 15:17:20.562 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 15:17:20.563 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 15:17:20.563 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 15:17:20.563 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 15:17:20.564 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 15:17:20.564 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 15:17:20.564 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 15:17:20.565 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 15:17:20.565 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 15:17:20.565 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 15:17:20.566 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 15:17:20.567 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 15:17:20.568 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 15:17:20.568 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 15:17:20.578 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 15:17:20.586 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 15:17:20.587 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 15:17:20.594 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 15:17:20.597 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:20.620 [Information] CANRegisterAccess: Read value 0x5A from register 0x0141 (simulated)
2025-08-02 15:17:20.630 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:20.636 [Information] CANRegisterAccess: Read value 0x9E from register 0x0141 (simulated)
2025-08-02 15:17:20.642 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:20.648 [Information] CANRegisterAccess: Read value 0xC2 from register 0x0141 (simulated)
2025-08-02 15:17:20.654 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:20.661 [Information] CANRegisterAccess: Read value 0x94 from register 0x0141 (simulated)
2025-08-02 15:17:20.668 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:20.674 [Information] CANRegisterAccess: Read value 0x7F from register 0x0141 (simulated)
2025-08-02 15:17:20.674 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 15:17:20.676 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 15:17:20.678 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 15:17:20.684 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 15:17:20.685 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 15:17:20.692 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 15:17:20.692 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 15:17:20.693 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 15:17:20.699 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 15:17:20.699 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 15:17:20.700 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 15:17:20.708 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 15:17:20.708 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 15:17:20.714 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 15:17:20.714 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 15:17:20.720 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 15:17:20.720 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 15:17:20.726 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 15:17:20.726 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 15:17:20.732 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 15:17:20.732 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 15:17:20.738 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 15:17:20.738 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 15:17:20.745 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 15:17:20.745 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 15:17:20.751 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 15:17:20.751 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 15:17:20.757 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 15:17:20.758 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 15:17:20.764 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 15:17:20.765 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 15:17:20.771 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 15:17:20.771 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 15:17:20.777 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 15:17:20.777 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 15:17:20.783 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 15:17:20.783 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 15:17:20.789 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 15:17:20.790 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 15:17:20.796 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 15:17:20.797 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 15:17:20.803 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 15:17:20.804 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 15:17:20.811 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 15:17:20.811 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 15:17:20.812 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 15:17:20.818 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 15:17:20.818 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 15:17:20.819 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 15:17:20.819 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:20.825 [Information] CANRegisterAccess: Read value 0xCE from register 0x0141 (simulated)
2025-08-02 15:17:20.825 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 15:17:20.826 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 15:17:20.826 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 15:17:20.827 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 15:17:20.834 [Information] CANRegisterAccess: Read value 0xAE from register 0x0140 (simulated)
2025-08-02 15:17:20.840 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 15:17:20.848 [Information] CANRegisterAccess: Read value 0x6D from register 0x0140 (simulated)
2025-08-02 15:17:20.854 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 15:17:20.861 [Information] CANRegisterAccess: Read value 0x25 from register 0x0140 (simulated)
2025-08-02 15:17:20.867 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 15:17:20.873 [Information] CANRegisterAccess: Read value 0x78 from register 0x0140 (simulated)
2025-08-02 15:17:20.873 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 15:17:20.874 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 15:17:20.878 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 15:17:20.879 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 15:17:20.892 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 15:17:20.893 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 15:17:20.894 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 15:17:20.905 [Information] VocomService: Sending data and waiting for response
2025-08-02 15:17:20.905 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 15:17:20.957 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 15:17:20.959 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 15:17:20.961 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 15:17:20.964 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 15:17:20.964 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 15:17:20.976 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 15:17:20.977 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 15:17:20.977 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 15:17:20.989 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 15:17:21.000 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 15:17:21.012 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 15:17:21.023 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 15:17:21.035 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 15:17:21.038 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 15:17:21.038 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 15:17:21.049 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 15:17:21.050 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 15:17:21.050 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 15:17:21.062 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 15:17:21.073 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 15:17:21.084 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 15:17:21.095 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 15:17:21.106 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 15:17:21.119 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 15:17:21.122 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 15:17:21.123 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 15:17:21.134 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 15:17:21.136 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 15:17:21.136 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 15:17:21.137 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 15:17:21.137 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 15:17:21.137 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 15:17:21.139 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 15:17:21.140 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 15:17:21.140 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 15:17:21.140 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 15:17:21.141 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 15:17:21.141 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 15:17:21.141 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 15:17:21.141 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 15:17:21.142 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 15:17:21.142 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 15:17:21.142 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 15:17:21.243 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 15:17:21.244 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 15:17:21.248 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 15:17:21.250 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:21.250 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 15:17:21.250 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 15:17:21.251 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:21.251 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 15:17:21.251 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 15:17:21.252 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:21.252 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 15:17:21.253 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 15:17:21.253 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:21.253 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 15:17:21.254 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 15:17:21.255 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-08-02 15:17:21.256 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-02 15:17:21.256 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-02 15:17:21.261 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-02 15:17:21.263 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-02 15:17:21.268 [Information] BackupService: Initializing backup service
2025-08-02 15:17:21.268 [Information] BackupService: Backup service initialized successfully
2025-08-02 15:17:21.268 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-02 15:17:21.269 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-02 15:17:21.271 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-02 15:17:21.331 [Information] BackupService: Compressing backup data
2025-08-02 15:17:21.348 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (447 bytes)
2025-08-02 15:17:21.349 [Information] BackupServiceFactory: Created template for category: Production
2025-08-02 15:17:21.350 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-02 15:17:21.352 [Information] BackupService: Compressing backup data
2025-08-02 15:17:21.354 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-08-02 15:17:21.354 [Information] BackupServiceFactory: Created template for category: Development
2025-08-02 15:17:21.355 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-02 15:17:21.355 [Information] BackupService: Compressing backup data
2025-08-02 15:17:21.357 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (443 bytes)
2025-08-02 15:17:21.357 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-02 15:17:21.357 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-02 15:17:21.358 [Information] BackupService: Compressing backup data
2025-08-02 15:17:21.359 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-08-02 15:17:21.360 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-02 15:17:21.360 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-02 15:17:21.361 [Information] BackupService: Compressing backup data
2025-08-02 15:17:21.362 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-08-02 15:17:21.363 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-02 15:17:21.363 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-02 15:17:21.364 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-02 15:17:21.365 [Information] BackupService: Compressing backup data
2025-08-02 15:17:21.366 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-08-02 15:17:21.367 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-02 15:17:21.367 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-02 15:17:21.369 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-02 15:17:21.373 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 15:17:21.376 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 15:17:21.491 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 15:17:21.492 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 15:17:21.494 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-02 15:17:21.495 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-02 15:17:21.495 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-02 15:17:21.497 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-02 15:17:21.497 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-02 15:17:21.503 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-02 15:17:21.503 [Information] App: Flash operation monitor service initialized successfully
2025-08-02 15:17:21.517 [Information] LicensingService: Initializing licensing service
2025-08-02 15:17:21.588 [Information] LicensingService: License information loaded successfully
2025-08-02 15:17:21.591 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-02 15:17:21.592 [Information] App: Licensing service initialized successfully
2025-08-02 15:17:21.592 [Information] App: License status: Trial
2025-08-02 15:17:21.593 [Information] App: Trial period: 24 days remaining
2025-08-02 15:17:21.594 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-02 15:17:21.636 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-02 15:17:21.818 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 15:17:21.819 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 15:17:21.820 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 15:17:21.820 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 15:17:21.822 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 15:17:21.823 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 15:17:21.824 [Information] VocomService: Native USB communication service initialized
2025-08-02 15:17:21.824 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 15:17:21.825 [Information] VocomService: Connection recovery service initialized
2025-08-02 15:17:21.825 [Information] VocomService: Enhanced services initialization completed
2025-08-02 15:17:21.826 [Information] VocomService: Checking if PTT application is running
2025-08-02 15:17:21.855 [Information] VocomService: PTT application is not running
2025-08-02 15:17:21.856 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:21.861 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:21.862 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 15:17:21.914 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 15:17:21.914 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 15:17:21.915 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 15:17:21.915 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 15:17:21.916 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-02 15:17:21.916 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: a9aa7d1c-8c71-4bff-bed8-00c8e02e5412
2025-08-02 15:17:21.919 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-02 15:17:21.919 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 15:17:21.920 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 15:17:21.920 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 15:17:21.924 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 15:17:21.925 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 15:17:21.927 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 15:17:21.928 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 15:17:21.928 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 15:17:21.939 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 15:17:21.940 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 15:17:21.940 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 15:17:21.941 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 15:17:21.941 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 15:17:21.941 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 15:17:21.942 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 15:17:21.942 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 15:17:21.943 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 15:17:21.943 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 15:17:21.944 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 15:17:21.944 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 15:17:21.944 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 15:17:21.945 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 15:17:21.945 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 15:17:21.946 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 15:17:21.946 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 15:17:21.947 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 15:17:21.953 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 15:17:21.954 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 15:17:21.954 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 15:17:21.954 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:21.961 [Information] CANRegisterAccess: Read value 0x9D from register 0x0141 (simulated)
2025-08-02 15:17:21.961 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 15:17:21.962 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 15:17:21.962 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 15:17:21.969 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 15:17:21.970 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 15:17:21.976 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 15:17:21.976 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 15:17:21.977 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 15:17:21.983 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 15:17:21.984 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 15:17:21.984 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 15:17:21.990 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 15:17:21.990 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 15:17:21.997 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 15:17:21.998 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 15:17:22.004 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 15:17:22.005 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 15:17:22.011 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 15:17:22.011 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 15:17:22.018 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 15:17:22.018 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 15:17:22.025 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 15:17:22.026 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 15:17:22.032 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 15:17:22.032 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 15:17:22.038 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 15:17:22.039 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 15:17:22.045 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 15:17:22.045 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 15:17:22.051 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 15:17:22.051 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 15:17:22.058 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 15:17:22.058 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 15:17:22.065 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 15:17:22.065 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 15:17:22.072 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 15:17:22.072 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 15:17:22.079 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 15:17:22.079 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 15:17:22.086 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 15:17:22.086 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 15:17:22.093 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 15:17:22.093 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 15:17:22.100 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 15:17:22.101 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 15:17:22.101 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 15:17:22.107 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 15:17:22.107 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 15:17:22.108 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 15:17:22.108 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 15:17:22.114 [Information] CANRegisterAccess: Read value 0x36 from register 0x0141 (simulated)
2025-08-02 15:17:22.114 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 15:17:22.115 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 15:17:22.115 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 15:17:22.116 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 15:17:22.122 [Information] CANRegisterAccess: Read value 0x56 from register 0x0140 (simulated)
2025-08-02 15:17:22.123 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 15:17:22.123 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 15:17:22.124 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 15:17:22.124 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 15:17:22.135 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 15:17:22.135 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 15:17:22.136 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 15:17:22.136 [Information] VocomService: Sending data and waiting for response
2025-08-02 15:17:22.136 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 15:17:22.186 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 15:17:22.187 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 15:17:22.187 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 15:17:22.188 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 15:17:22.188 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 15:17:22.200 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 15:17:22.201 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 15:17:22.201 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 15:17:22.212 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 15:17:22.223 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 15:17:22.234 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 15:17:22.245 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 15:17:22.256 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 15:17:22.257 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 15:17:22.257 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 15:17:22.268 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 15:17:22.269 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 15:17:22.269 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 15:17:22.280 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 15:17:22.291 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 15:17:22.303 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 15:17:22.314 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 15:17:22.325 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 15:17:22.336 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 15:17:22.337 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 15:17:22.337 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 15:17:22.348 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 15:17:22.349 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 15:17:22.349 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 15:17:22.349 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 15:17:22.350 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 15:17:22.350 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 15:17:22.350 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 15:17:22.351 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 15:17:22.351 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 15:17:22.351 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 15:17:22.352 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 15:17:22.352 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 15:17:22.352 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 15:17:22.353 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 15:17:22.353 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 15:17:22.353 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 15:17:22.353 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 15:17:22.453 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 15:17:22.454 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 15:17:22.454 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 15:17:22.455 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:22.455 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 15:17:22.456 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 15:17:22.456 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:22.456 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 15:17:22.457 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 15:17:22.457 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:22.457 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 15:17:22.458 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 15:17:22.458 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 15:17:22.458 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 15:17:22.459 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 15:17:22.510 [Information] BackupService: Initializing backup service
2025-08-02 15:17:22.510 [Information] BackupService: Backup service initialized successfully
2025-08-02 15:17:22.562 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 15:17:22.562 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 15:17:22.564 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 15:17:22.564 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 15:17:22.616 [Information] BackupService: Getting predefined backup categories
2025-08-02 15:17:22.669 [Information] MainViewModel: Services initialized successfully
2025-08-02 15:17:22.672 [Information] MainViewModel: Scanning for Vocom devices
2025-08-02 15:17:22.674 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 15:17:22.674 [Information] VocomService: Using new enhanced device detection service
2025-08-02 15:17:22.674 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 15:17:22.675 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 15:17:23.013 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 15:17:23.013 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 15:17:23.014 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 15:17:23.014 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 15:17:23.015 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 15:17:23.015 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 15:17:23.015 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 15:17:23.016 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 15:17:23.413 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 15:17:23.414 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 15:17:23.415 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 15:17:23.416 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 15:17:23.418 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 15:17:23.434 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 15:17:23.435 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 15:17:23.435 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 15:17:23.436 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 15:17:23.436 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 15:17:23.439 [Debug] VocomService: Bluetooth is enabled
2025-08-02 15:17:23.439 [Debug] VocomService: Checking if WiFi is available
2025-08-02 15:17:23.460 [Debug] VocomService: WiFi is available
2025-08-02 15:17:23.461 [Information] VocomService: Found 3 Vocom devices
2025-08-02 15:17:23.463 [Information] MainViewModel: Found 3 Vocom device(s)
