Log started at 8/2/2025 1:44:52 PM
2025-08-02 13:44:52.613 [Information] LoggingService: Logging service initialized
2025-08-02 13:44:52.631 [Information] App: Starting integrated application initialization
2025-08-02 13:44:52.632 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-02 13:44:52.635 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-02 13:44:52.638 [Information] IntegratedStartupService: Setting up application environment
2025-08-02 13:44:52.638 [Information] IntegratedStartupService: Application environment setup completed
2025-08-02 13:44:52.640 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-02 13:44:52.643 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-08-02 13:44:52.646 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-02 13:44:52.651 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-02 13:44:52.670 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.677 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.680 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.681 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-02 13:44:52.685 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.688 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.691 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.692 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:44:52.696 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.699 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.702 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.703 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 13:44:52.706 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.709 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.712 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.713 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 13:44:52.717 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.719 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.722 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.722 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 13:44:52.726 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.730 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.733 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.734 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 13:44:52.737 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 13:44:52.740 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.743 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 13:44:52.744 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 13:44:52.746 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-02 13:44:52.749 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-02 13:44:52.750 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-02 13:44:52.768 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-02 13:44:52.768 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-02 13:44:52.779 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-02 13:44:52.779 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-02 13:44:52.780 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-02 13:44:52.902 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-02 13:44:52.903 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-02 13:44:52.948 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-02 13:44:52.948 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:44:52.949 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 13:44:52.949 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 13:44:52.950 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 13:44:52.950 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 13:44:52.950 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 13:44:52.957 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-02 13:44:52.958 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-02 13:44:52.958 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-02 13:44:52.964 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-02 13:44:52.965 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-02 13:44:52.966 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-02 13:44:52.968 [Information] LibraryExtractor: Starting library extraction process
2025-08-02 13:44:52.971 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-02 13:44:52.974 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-02 13:44:52.976 [Information] LibraryExtractor: Copying system libraries
2025-08-02 13:44:52.990 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-02 13:44:53.018 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-08-02 13:45:23.034 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 13:45:24.037 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-08-02 13:45:54.041 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 13:45:54.042 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:46:24.044 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 13:46:25.045 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-08-02 13:46:55.048 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 13:46:55.049 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 13:47:25.052 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 13:47:26.052 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-08-02 13:47:56.056 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-08-02 13:47:56.057 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-08-02 13:47:56.057 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-08-02 13:47:56.061 [Information] LibraryExtractor: Verifying library extraction
2025-08-02 13:47:56.061 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-02 13:47:56.062 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-02 13:47:56.062 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-02 13:47:56.063 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-02 13:47:56.063 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-02 13:47:56.069 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-02 13:47:56.071 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-02 13:47:56.072 [Information] DependencyManager: Initializing dependency manager
2025-08-02 13:47:56.073 [Information] DependencyManager: Setting up library search paths
2025-08-02 13:47:56.075 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 13:47:56.076 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 13:47:56.076 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 13:47:56.077 [Information] DependencyManager: Updated PATH environment variable
2025-08-02 13:47:56.078 [Information] DependencyManager: Verifying required directories
2025-08-02 13:47:56.079 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 13:47:56.080 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 13:47:56.080 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-02 13:47:56.080 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 13:47:56.082 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-02 13:47:56.106 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-08-02 13:47:56.117 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 13:47:56.118 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 13:47:56.126 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 13:47:56.141 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-08-02 13:47:56.148 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 13:47:56.149 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 13:47:56.155 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 13:47:56.158 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 13:47:56.158 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 13:47:56.173 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-08-02 13:47:56.242 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-08-02 13:47:56.257 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-08-02 13:47:56.258 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-08-02 13:47:56.263 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:47:56.264 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:47:56.265 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 13:47:56.265 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 13:47:56.266 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 13:47:56.267 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 13:47:56.268 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 13:47:56.268 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 13:47:56.269 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 13:47:56.269 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 13:47:56.270 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 13:47:56.271 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 13:47:56.271 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 13:47:56.272 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 13:47:56.273 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 13:47:56.273 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 13:47:56.274 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 13:47:56.274 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 13:47:56.275 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-02 13:47:56.275 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-02 13:47:56.277 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-02 13:47:56.324 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-02 13:47:56.420 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 13:47:56.420 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-02 13:47:56.515 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 13:47:56.516 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 13:47:56.517 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-02 13:47:56.597 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 13:47:56.598 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-02 13:47:56.659 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 13:47:56.659 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-02 13:47:56.660 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-02 13:47:56.936 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 13:47:56.937 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-02 13:47:57.198 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 13:47:57.198 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-02 13:47:57.199 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-02 13:47:57.315 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 13:47:57.316 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-02 13:47:57.429 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 13:47:57.430 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-02 13:47:57.431 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-02 13:47:57.498 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-02 13:47:57.499 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 13:47:57.500 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 13:47:57.500 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-08-02 13:47:57.501 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 13:47:57.501 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 13:47:57.502 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 13:47:57.502 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-08-02 13:47:57.502 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 13:47:57.503 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 13:47:57.503 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 13:47:57.504 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-02 13:47:57.505 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-08-02 13:47:57.505 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-08-02 13:47:57.506 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-08-02 13:47:57.506 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-08-02 13:47:57.507 [Information] DependencyManager: Setting up environment variables
2025-08-02 13:47:57.508 [Information] DependencyManager: Environment variables configured
2025-08-02 13:47:57.510 [Information] DependencyManager: Verifying library loading status
2025-08-02 13:47:57.882 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-08-02 13:47:57.883 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-02 13:47:57.883 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-02 13:47:57.886 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-02 13:47:57.887 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-02 13:47:57.893 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-02 13:47:57.896 [Information] IntegratedStartupService: Verifying system readiness
2025-08-02 13:47:57.896 [Information] IntegratedStartupService: System readiness verification passed
2025-08-02 13:47:57.897 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-02 13:47:57.899 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-02 13:47:57.899 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 13:47:57.899 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 13:47:57.900 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 13:47:57.900 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-02 13:47:57.900 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-02 13:47:57.901 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-02 13:47:57.901 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 13:47:57.901 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-02 13:47:57.902 [Information] App: Integrated startup completed successfully
2025-08-02 13:47:57.906 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-02 13:47:58.175 [Information] App: Initializing application services
2025-08-02 13:47:58.178 [Information] AppConfigurationService: Initializing configuration service
2025-08-02 13:47:58.178 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 13:47:58.264 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 13:47:58.265 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-02 13:47:58.266 [Information] App: Configuration service initialized successfully
2025-08-02 13:47:58.268 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-02 13:47:58.268 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-02 13:47:58.275 [Information] App: Environment variable exists: True, not 'false': False
2025-08-02 13:47:58.275 [Information] App: Final useDummyImplementations value: False
2025-08-02 13:47:58.276 [Information] App: Updating config to NOT use dummy implementations
2025-08-02 13:47:58.278 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-02 13:47:58.296 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 13:47:58.296 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-02 13:47:58.296 [Information] App: usePatchedImplementation flag is: True
2025-08-02 13:47:58.297 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-02 13:47:58.297 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-02 13:47:58.297 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-02 13:47:58.297 [Information] App: verboseLogging flag is: True
2025-08-02 13:47:58.300 [Information] App: Verifying real hardware requirements...
2025-08-02 13:47:58.300 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-02 13:47:58.301 [Information] App: ✓ Found critical library: apci.dll
2025-08-02 13:47:58.301 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-02 13:47:58.301 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-02 13:47:58.302 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 13:47:58.302 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-02 13:47:58.302 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-02 13:47:58.303 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-02 13:47:58.314 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-02 13:47:58.318 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-02 13:47:58.319 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-02 13:47:58.322 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-02 13:47:58.329 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-02 13:50:29.979 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 13:50:29.979 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-02 13:50:29.980 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-02 13:50:29.980 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:50:29.981 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 13:50:29.981 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 13:50:29.982 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 13:50:29.982 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 13:50:29.983 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 13:50:29.983 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-02 13:50:29.984 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-02 13:50:29.985 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-02 13:50:29.985 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 13:50:29.985 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 13:50:29.986 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-02 13:50:29.986 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-02 13:50:29.986 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-02 13:50:29.987 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-02 13:50:29.990 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-02 13:50:29.990 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-02 13:50:29.993 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-02 13:50:29.993 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-02 13:50:29.994 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-02 13:50:29.994 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-02 13:50:29.996 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-02 13:50:29.996 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-08-02 13:50:30.000 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-08-02 13:50:30.002 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-08-02 13:50:30.004 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-08-02 13:50:30.004 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-08-02 13:50:30.048 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-08-02 13:50:30.049 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-08-02 13:50:30.056 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-08-02 13:50:30.056 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-08-02 13:50:30.057 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-08-02 13:50:30.057 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-08-02 13:50:30.060 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-08-02 13:50:30.061 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-08-02 13:50:30.061 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-08-02 13:50:30.062 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:50:30.062 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-08-02 13:50:30.062 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-08-02 13:50:30.063 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-08-02 13:50:30.066 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-08-02 13:50:30.070 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-08-02 13:50:30.105 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-08-02 13:50:30.107 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 13:50:30.107 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-08-02 13:50:30.134 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 13:50:30.182 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-08-02 13:50:30.184 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-08-02 13:50:30.184 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-08-02 13:50:30.186 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 13:50:30.186 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-08-02 13:50:30.186 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 13:50:30.187 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-08-02 13:50:30.340 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 13:50:30.340 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-08-02 13:50:30.341 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-08-02 13:50:30.342 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-08-02 13:50:30.343 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-08-02 13:50:30.344 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-08-02 13:50:30.345 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-08-02 13:50:30.345 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-08-02 13:50:30.349 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-08-02 13:50:30.350 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-08-02 13:50:30.351 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 13:50:30.351 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-08-02 13:50:30.352 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-08-02 13:50:30.355 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-08-02 13:50:30.356 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-08-02 13:50:30.357 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-08-02 13:50:30.357 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-08-02 13:50:30.358 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-08-02 13:50:30.358 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-08-02 13:50:30.358 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-08-02 13:50:30.359 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-08-02 13:50:30.360 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-08-02 13:50:30.360 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-08-02 13:50:30.362 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-08-02 13:50:30.363 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-08-02 13:50:30.363 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-08-02 13:50:30.363 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:50:30.364 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-08-02 13:50:30.367 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-08-02 13:50:30.368 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 13:50:30.368 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-08-02 13:50:30.368 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-08-02 13:50:30.370 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-08-02 13:50:30.370 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-08-02 13:50:30.370 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-08-02 13:50:30.374 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-08-02 13:50:30.377 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-08-02 13:50:30.377 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-08-02 13:50:30.816 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-08-02 13:50:30.818 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-08-02 13:50:30.823 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 13:50:30.823 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 13:50:30.829 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-08-02 13:50:30.831 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:30.832 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:30.832 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-08-02 13:50:30.833 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:30.833 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:30.834 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-08-02 13:50:31.272 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:31.272 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-08-02 13:50:31.589 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:31.590 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-08-02 13:50:31.591 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:31.592 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:31.592 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-08-02 13:50:31.790 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:32.083 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:32.083 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-08-02 13:50:32.271 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 13:50:32.709 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 13:50:33.070 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:33.427 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:33.436 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-08-02 13:50:33.437 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 13:50:34.056 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:34.057 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-08-02 13:50:34.228 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:34.395 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:34.396 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-08-02 13:50:34.661 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 13:50:34.890 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 13:50:34.890 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-08-02 13:50:35.096 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 13:50:35.359 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 13:50:35.453 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 13:50:35.457 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-08-02 13:50:35.458 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 13:50:35.458 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-08-02 13:50:35.459 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-08-02 13:50:35.460 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-08-02 13:50:35.462 [Information] VocomDriver: Initializing Vocom driver
2025-08-02 13:50:35.465 [Information] VocomNativeInterop: Initializing Vocom driver
2025-08-02 13:50:35.469 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-08-02 13:50:35.470 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 13:50:35.470 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 13:50:35.472 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 13:50:35.472 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 13:50:52.262 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-08-02 13:50:52.878 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-08-02 13:50:52.881 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-08-02 13:50:52.882 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-08-02 13:50:52.882 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-08-02 13:50:52.885 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 13:50:52.964 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-08-02 13:50:53.151 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-08-02 13:50:53.327 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-08-02 13:50:53.370 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-08-02 13:50:53.370 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-08-02 13:50:53.371 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-08-02 13:50:53.373 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-08-02 13:50:53.374 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-08-02 13:50:53.375 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-08-02 13:50:53.375 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-08-02 13:50:53.375 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-08-02 13:50:53.376 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-08-02 13:50:53.376 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-08-02 13:50:53.376 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-08-02 13:50:53.376 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-08-02 13:50:53.377 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-08-02 13:50:53.377 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-08-02 13:50:53.377 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-08-02 13:50:53.378 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-08-02 13:50:53.378 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-08-02 13:50:53.379 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-08-02 13:50:53.379 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-08-02 13:50:53.379 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-08-02 13:50:53.380 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-08-02 13:50:53.380 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-08-02 13:50:53.380 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-08-02 13:50:53.380 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-08-02 13:50:53.381 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-08-02 13:50:53.381 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-08-02 13:50:53.381 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-08-02 13:50:53.382 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-08-02 13:50:53.382 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-08-02 13:50:53.382 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-08-02 13:50:53.383 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-08-02 13:50:53.383 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-08-02 13:50:53.383 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-08-02 13:50:53.384 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-08-02 13:50:53.384 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-08-02 13:50:53.384 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-08-02 13:50:53.384 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-08-02 13:50:53.385 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-08-02 13:50:53.385 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-08-02 13:50:53.385 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-08-02 13:50:53.385 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-08-02 13:50:53.386 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-08-02 13:50:53.386 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-08-02 13:50:53.387 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-08-02 13:50:53.387 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-08-02 13:50:53.387 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-08-02 13:50:53.389 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-08-02 13:50:53.390 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-08-02 13:50:53.391 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-08-02 13:50:53.392 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-08-02 13:50:53.392 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-08-02 13:50:53.392 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-08-02 13:50:53.393 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-08-02 13:50:53.393 [Information] VocomDriver: Vocom driver initialized successfully
2025-08-02 13:50:53.400 [Information] VocomService: Initializing Vocom service with dependencies
2025-08-02 13:50:53.402 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-08-02 13:50:53.404 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-08-02 13:50:53.406 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-08-02 13:50:53.495 [Information] WiFiCommunicationService: WiFi is available
2025-08-02 13:50:53.495 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-08-02 13:50:53.498 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-08-02 13:50:53.500 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-08-02 13:50:53.502 [Information] BluetoothCommunicationService: Bluetooth is available
2025-08-02 13:50:53.503 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-08-02 13:50:53.505 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 13:50:53.509 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 13:50:53.510 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 13:50:53.512 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 13:50:53.518 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 13:50:53.518 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 13:50:53.519 [Information] VocomService: Native USB communication service initialized
2025-08-02 13:50:53.519 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 13:50:53.520 [Information] VocomService: Connection recovery service initialized
2025-08-02 13:50:53.520 [Information] VocomService: Enhanced services initialization completed
2025-08-02 13:50:53.524 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:50:53.550 [Information] VocomService: PTT application is not running
2025-08-02 13:50:53.552 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:50:53.555 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:50:53.556 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 13:50:53.557 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-08-02 13:50:53.558 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-08-02 13:50:53.562 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 13:50:53.563 [Information] VocomService: Using new enhanced device detection service
2025-08-02 13:50:53.565 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 13:50:53.567 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 13:50:54.093 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 13:50:54.095 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 13:50:54.096 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 13:50:54.097 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 13:50:54.098 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 13:50:54.099 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 13:50:54.102 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 13:50:54.105 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 13:50:54.725 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 13:50:54.727 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 13:50:54.729 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 13:50:54.730 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 13:50:54.732 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 13:50:54.743 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 13:50:54.744 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 13:50:54.745 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 13:50:54.745 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 13:50:54.745 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:50:54.746 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:50:54.748 [Debug] VocomService: Checking if WiFi is available
2025-08-02 13:50:54.751 [Debug] VocomService: WiFi is available
2025-08-02 13:50:54.751 [Information] VocomService: Found 3 Vocom devices
2025-08-02 13:50:54.752 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-08-02 13:50:54.754 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: c1f4ad38-0775-439f-a052-45ffdd1895c5, Type: USB)
2025-08-02 13:50:54.754 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 8770289a-7241-4c15-a619-b8d404d73620, Type: Bluetooth)
2025-08-02 13:50:54.754 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 5441d730-dac7-45d9-9c48-0807534246ed, Type: WiFi)
2025-08-02 13:50:54.755 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-08-02 13:50:54.756 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-08-02 13:50:54.756 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-08-02 13:50:54.756 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-08-02 13:50:54.757 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-08-02 13:50:54.757 [Information] App: Architecture-aware Vocom service created successfully
2025-08-02 13:50:54.758 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 13:50:54.758 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 13:50:54.758 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 13:50:54.759 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 13:50:54.759 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 13:50:54.760 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 13:50:54.760 [Information] VocomService: Native USB communication service initialized
2025-08-02 13:50:54.760 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 13:50:54.761 [Information] VocomService: Connection recovery service initialized
2025-08-02 13:50:54.761 [Information] VocomService: Enhanced services initialization completed
2025-08-02 13:50:54.761 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:50:54.780 [Information] VocomService: PTT application is not running
2025-08-02 13:50:54.781 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:50:54.782 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:50:54.782 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 13:50:54.782 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-02 13:50:54.783 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-02 13:50:54.783 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-02 13:50:54.834 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 13:50:54.834 [Information] VocomService: Using new enhanced device detection service
2025-08-02 13:50:54.834 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 13:50:54.835 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 13:50:55.119 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 13:50:55.119 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 13:50:55.119 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 13:50:55.120 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 13:50:55.120 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 13:50:55.120 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 13:50:55.121 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 13:50:55.122 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 13:50:55.420 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 13:50:55.420 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 13:50:55.421 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 13:50:55.421 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 13:50:55.423 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 13:50:55.434 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 13:50:55.435 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 13:50:55.435 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 13:50:55.436 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 13:50:55.436 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:50:55.436 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:50:55.437 [Debug] VocomService: Checking if WiFi is available
2025-08-02 13:50:55.437 [Debug] VocomService: WiFi is available
2025-08-02 13:50:55.438 [Information] VocomService: Found 3 Vocom devices
2025-08-02 13:50:55.438 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-08-02 13:50:55.442 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 13:50:55.442 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:50:55.459 [Information] VocomService: PTT application is not running
2025-08-02 13:50:55.463 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 13:50:55.464 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 13:50:55.464 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:50:55.485 [Information] VocomService: PTT application is not running
2025-08-02 13:50:55.485 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 13:50:55.487 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 13:50:55.489 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:50:55.492 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:50:55.494 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:55.495 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:55.496 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:55.496 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:55.496 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:55.497 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:55.497 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:55.497 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:55.497 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:55.498 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:50:55.498 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:55.498 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:55.499 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:55.499 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:55.499 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:55.499 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:50:55.500 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:55.501 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:50:55.501 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:55.501 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:50:55.502 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:50:55.502 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:50:55.502 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 13:50:56.504 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 13:50:56.504 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:50:56.504 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:50:56.505 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:56.505 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:56.506 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:56.506 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:56.506 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:56.507 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:56.507 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:56.507 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:56.508 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:56.508 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:50:56.508 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:56.509 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:56.509 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:56.509 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:56.510 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:56.510 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:50:56.510 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:56.511 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:50:56.511 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:56.511 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:50:56.511 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:50:56.512 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:50:56.512 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 13:50:57.513 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 13:50:57.513 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:50:57.514 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:50:57.514 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:57.515 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:57.515 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:57.516 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:57.516 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:57.516 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:57.516 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:57.517 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:57.517 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:57.517 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:50:57.518 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:57.518 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:57.518 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:57.518 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:57.519 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:57.519 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:50:57.520 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:57.520 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:50:57.520 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:57.520 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:50:57.521 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:50:57.521 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:50:57.524 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 13:50:57.525 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 13:50:57.526 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 13:50:57.710 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 13:50:57.712 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 13:50:57.713 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 13:50:57.714 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 13:50:57.715 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 13:50:57.715 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 13:50:57.715 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 13:50:57.718 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 13:50:57.719 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 13:50:57.724 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 13:50:57.726 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 13:50:57.727 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 13:50:57.728 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 13:50:57.728 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 13:50:57.728 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:50:57.729 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:50:57.730 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-08-02 13:50:57.735 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-02 13:50:57.739 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:50:57.741 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-02 13:50:57.747 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 13:50:57.749 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 13:50:57.750 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 13:50:57.751 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 13:50:57.751 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 13:50:57.752 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 13:50:57.752 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 13:50:57.753 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 13:50:57.756 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 13:50:57.756 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 13:50:57.757 [Information] VocomService: Using new enhanced device detection service
2025-08-02 13:50:57.757 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 13:50:57.757 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 13:50:58.061 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 13:50:58.062 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 13:50:58.062 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 13:50:58.062 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 13:50:58.063 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 13:50:58.063 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 13:50:58.063 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 13:50:58.064 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 13:50:58.392 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 13:50:58.392 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 13:50:58.393 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 13:50:58.394 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 13:50:58.395 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 13:50:58.400 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 13:50:58.401 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 13:50:58.401 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 13:50:58.401 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 13:50:58.402 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:50:58.402 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:50:58.403 [Debug] VocomService: Checking if WiFi is available
2025-08-02 13:50:58.403 [Debug] VocomService: WiFi is available
2025-08-02 13:50:58.403 [Information] VocomService: Found 3 Vocom devices
2025-08-02 13:50:58.404 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:50:58.405 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 13:50:58.405 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:50:58.424 [Information] VocomService: PTT application is not running
2025-08-02 13:50:58.425 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 13:50:58.425 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 13:50:58.426 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:50:58.442 [Information] VocomService: PTT application is not running
2025-08-02 13:50:58.443 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 13:50:58.443 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 13:50:58.444 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:50:58.444 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:50:58.445 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:58.445 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:58.445 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:58.446 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:58.446 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:58.446 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:58.446 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:58.447 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:58.447 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:58.447 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:50:58.448 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:58.448 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:58.448 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:58.448 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:58.449 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:58.449 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:50:58.449 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:58.450 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:50:58.450 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:58.450 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:50:58.451 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:50:58.451 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:50:58.451 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 13:50:59.451 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 13:50:59.451 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:50:59.452 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:50:59.453 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:59.453 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:59.453 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:59.454 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:59.454 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:59.454 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:59.455 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:59.455 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:59.455 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:59.456 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:50:59.456 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:59.457 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:59.457 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:50:59.458 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:50:59.458 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:50:59.458 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:50:59.459 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:59.459 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:50:59.459 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:50:59.460 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:50:59.460 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:50:59.460 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:50:59.460 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 13:51:00.460 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 13:51:00.461 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:51:00.461 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:51:00.462 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:00.462 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:00.463 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:00.463 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:00.463 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:00.464 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:00.464 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:00.464 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:00.465 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:00.465 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:51:00.465 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:00.466 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:00.466 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:00.466 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:00.468 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:00.468 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:51:00.468 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:00.469 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:51:00.469 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:00.469 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:51:00.470 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:51:00.470 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:51:00.470 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 13:51:00.471 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 13:51:00.471 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 13:51:00.471 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 13:51:00.472 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 13:51:00.472 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 13:51:00.473 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 13:51:00.473 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 13:51:00.473 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 13:51:00.474 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 13:51:00.474 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 13:51:00.474 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 13:51:00.474 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 13:51:00.475 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 13:51:00.475 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 13:51:00.475 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 13:51:00.476 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 13:51:00.476 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:00.476 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:00.477 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:00.477 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:00.478 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:00.478 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:00.478 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:00.478 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:51:00.496 [Information] VocomService: PTT application is not running
2025-08-02 13:51:00.499 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:00.499 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:51:00.500 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:51:00.501 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-08-02 13:51:01.307 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:01.308 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:01.310 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-08-02 13:51:01.310 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:01.313 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 13:51:01.316 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 13:51:01.320 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 13:51:01.323 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 13:51:01.328 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 13:51:01.335 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 13:51:01.338 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 13:51:01.350 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 13:51:01.351 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 13:51:01.352 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 13:51:01.352 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 13:51:01.353 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 13:51:01.353 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 13:51:01.353 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 13:51:01.354 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 13:51:01.354 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 13:51:01.354 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 13:51:01.355 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 13:51:01.355 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 13:51:01.355 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 13:51:01.356 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 13:51:01.356 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 13:51:01.356 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 13:51:01.357 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 13:51:01.361 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 13:51:01.367 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 13:51:01.368 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 13:51:01.372 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 13:51:01.375 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:01.381 [Information] CANRegisterAccess: Read value 0xBF from register 0x0141 (simulated)
2025-08-02 13:51:01.382 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 13:51:01.383 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 13:51:01.384 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 13:51:01.390 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 13:51:01.390 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 13:51:01.395 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 13:51:01.396 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 13:51:01.396 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 13:51:01.402 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 13:51:01.404 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 13:51:01.404 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 13:51:01.410 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 13:51:01.411 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 13:51:01.417 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 13:51:01.417 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 13:51:01.424 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 13:51:01.424 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 13:51:01.430 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 13:51:01.431 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 13:51:01.436 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 13:51:01.437 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 13:51:01.442 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 13:51:01.443 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 13:51:01.448 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 13:51:01.449 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 13:51:01.454 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 13:51:01.455 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 13:51:01.460 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 13:51:01.461 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 13:51:01.466 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 13:51:01.467 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 13:51:01.472 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 13:51:01.473 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 13:51:01.478 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 13:51:01.479 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 13:51:01.483 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 13:51:01.484 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 13:51:01.489 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 13:51:01.490 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 13:51:01.495 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 13:51:01.496 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 13:51:01.501 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 13:51:01.502 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 13:51:01.508 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 13:51:01.508 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 13:51:01.509 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 13:51:01.514 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 13:51:01.515 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 13:51:01.515 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 13:51:01.516 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:01.520 [Information] CANRegisterAccess: Read value 0xF1 from register 0x0141 (simulated)
2025-08-02 13:51:01.527 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:01.533 [Information] CANRegisterAccess: Read value 0x34 from register 0x0141 (simulated)
2025-08-02 13:51:01.534 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 13:51:01.534 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 13:51:01.535 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 13:51:01.535 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 13:51:01.541 [Information] CANRegisterAccess: Read value 0x3F from register 0x0140 (simulated)
2025-08-02 13:51:01.541 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 13:51:01.542 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 13:51:01.545 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 13:51:01.545 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 13:51:01.556 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 13:51:01.558 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 13:51:01.558 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 13:51:01.563 [Information] VocomService: Sending data and waiting for response
2025-08-02 13:51:01.563 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 13:51:01.615 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 13:51:01.616 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 13:51:01.618 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 13:51:01.620 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 13:51:01.621 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 13:51:01.632 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 13:51:01.633 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 13:51:01.633 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 13:51:01.644 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 13:51:01.655 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 13:51:01.666 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 13:51:01.677 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 13:51:01.688 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 13:51:01.690 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 13:51:01.690 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 13:51:01.702 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 13:51:01.703 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 13:51:01.703 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 13:51:01.714 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 13:51:01.725 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 13:51:01.736 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 13:51:01.747 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 13:51:01.758 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 13:51:01.769 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 13:51:01.771 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 13:51:01.771 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 13:51:01.783 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 13:51:01.784 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 13:51:01.784 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 13:51:01.785 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 13:51:01.785 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 13:51:01.785 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 13:51:01.786 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 13:51:01.786 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 13:51:01.786 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 13:51:01.786 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 13:51:01.787 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 13:51:01.787 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 13:51:01.787 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 13:51:01.788 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 13:51:01.788 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 13:51:01.788 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 13:51:01.789 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 13:51:01.889 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 13:51:01.889 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 13:51:01.893 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 13:51:01.894 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:01.894 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 13:51:01.895 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 13:51:01.895 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:01.896 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 13:51:01.896 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 13:51:01.897 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:01.897 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 13:51:01.897 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 13:51:01.898 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:01.898 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 13:51:01.898 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 13:51:01.899 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-08-02 13:51:01.901 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-02 13:51:01.902 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-02 13:51:01.905 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-02 13:51:01.907 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-02 13:51:01.911 [Information] BackupService: Initializing backup service
2025-08-02 13:51:01.912 [Information] BackupService: Backup service initialized successfully
2025-08-02 13:51:01.912 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-02 13:51:01.912 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-02 13:51:01.915 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-02 13:51:01.956 [Information] BackupService: Compressing backup data
2025-08-02 13:51:01.966 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-08-02 13:51:01.967 [Information] BackupServiceFactory: Created template for category: Production
2025-08-02 13:51:01.968 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-02 13:51:01.968 [Information] BackupService: Compressing backup data
2025-08-02 13:51:01.970 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-08-02 13:51:01.970 [Information] BackupServiceFactory: Created template for category: Development
2025-08-02 13:51:01.971 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-02 13:51:01.971 [Information] BackupService: Compressing backup data
2025-08-02 13:51:01.973 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (444 bytes)
2025-08-02 13:51:01.975 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-02 13:51:01.976 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-02 13:51:01.977 [Information] BackupService: Compressing backup data
2025-08-02 13:51:01.978 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (448 bytes)
2025-08-02 13:51:01.979 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-02 13:51:01.979 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-02 13:51:01.979 [Information] BackupService: Compressing backup data
2025-08-02 13:51:01.982 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-08-02 13:51:01.982 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-02 13:51:01.982 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-02 13:51:01.983 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-02 13:51:01.983 [Information] BackupService: Compressing backup data
2025-08-02 13:51:01.984 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-08-02 13:51:01.985 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-02 13:51:01.985 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-02 13:51:01.987 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-02 13:51:01.991 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 13:51:01.995 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 13:51:02.088 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 13:51:02.089 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 13:51:02.090 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-02 13:51:02.091 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-02 13:51:02.091 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-02 13:51:02.093 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-02 13:51:02.094 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-02 13:51:02.098 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-02 13:51:02.099 [Information] App: Flash operation monitor service initialized successfully
2025-08-02 13:51:02.111 [Information] LicensingService: Initializing licensing service
2025-08-02 13:51:02.176 [Information] LicensingService: License information loaded successfully
2025-08-02 13:51:02.178 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-02 13:51:02.178 [Information] App: Licensing service initialized successfully
2025-08-02 13:51:02.179 [Information] App: License status: Trial
2025-08-02 13:51:02.179 [Information] App: Trial period: 24 days remaining
2025-08-02 13:51:02.180 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-02 13:51:02.212 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-02 13:51:02.383 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 13:51:02.383 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 13:51:02.384 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 13:51:02.384 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 13:51:02.385 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 13:51:02.385 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 13:51:02.385 [Information] VocomService: Native USB communication service initialized
2025-08-02 13:51:02.386 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 13:51:02.386 [Information] VocomService: Connection recovery service initialized
2025-08-02 13:51:02.386 [Information] VocomService: Enhanced services initialization completed
2025-08-02 13:51:02.387 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:51:02.403 [Information] VocomService: PTT application is not running
2025-08-02 13:51:02.403 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:51:02.404 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:51:02.405 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 13:51:02.455 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 13:51:02.456 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 13:51:02.456 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 13:51:02.456 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 13:51:02.457 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-02 13:51:02.457 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 99204919-a23e-4c76-a080-616c407ccd2a
2025-08-02 13:51:02.459 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-02 13:51:02.459 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 13:51:02.459 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 13:51:02.460 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 13:51:02.462 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 13:51:02.462 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 13:51:02.463 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 13:51:02.464 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 13:51:02.464 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 13:51:02.476 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 13:51:02.476 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 13:51:02.477 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 13:51:02.477 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 13:51:02.478 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 13:51:02.478 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 13:51:02.478 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 13:51:02.479 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 13:51:02.479 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 13:51:02.479 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 13:51:02.480 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 13:51:02.480 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 13:51:02.480 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 13:51:02.480 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 13:51:02.481 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 13:51:02.481 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 13:51:02.481 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 13:51:02.481 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 13:51:02.487 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 13:51:02.488 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 13:51:02.488 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 13:51:02.489 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:02.494 [Information] CANRegisterAccess: Read value 0x7E from register 0x0141 (simulated)
2025-08-02 13:51:02.500 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:02.508 [Information] CANRegisterAccess: Read value 0xFC from register 0x0141 (simulated)
2025-08-02 13:51:02.514 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:02.520 [Information] CANRegisterAccess: Read value 0xFF from register 0x0141 (simulated)
2025-08-02 13:51:02.521 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 13:51:02.521 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 13:51:02.522 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 13:51:02.527 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 13:51:02.528 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 13:51:02.534 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 13:51:02.535 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 13:51:02.535 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 13:51:02.542 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 13:51:02.542 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 13:51:02.542 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 13:51:02.548 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 13:51:02.549 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 13:51:02.555 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 13:51:02.556 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 13:51:02.561 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 13:51:02.562 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 13:51:02.568 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 13:51:02.569 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 13:51:02.574 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 13:51:02.575 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 13:51:02.581 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 13:51:02.582 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 13:51:02.588 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 13:51:02.589 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 13:51:02.595 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 13:51:02.596 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 13:51:02.602 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 13:51:02.603 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 13:51:02.609 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 13:51:02.609 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 13:51:02.615 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 13:51:02.616 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 13:51:02.622 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 13:51:02.623 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 13:51:02.629 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 13:51:02.630 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 13:51:02.635 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 13:51:02.636 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 13:51:02.643 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 13:51:02.643 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 13:51:02.649 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 13:51:02.650 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 13:51:02.656 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 13:51:02.657 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 13:51:02.658 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 13:51:02.663 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 13:51:02.664 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 13:51:02.664 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 13:51:02.664 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:02.670 [Information] CANRegisterAccess: Read value 0x5F from register 0x0141 (simulated)
2025-08-02 13:51:02.676 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 13:51:02.682 [Information] CANRegisterAccess: Read value 0xC6 from register 0x0141 (simulated)
2025-08-02 13:51:02.683 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 13:51:02.683 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 13:51:02.683 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 13:51:02.684 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 13:51:02.689 [Information] CANRegisterAccess: Read value 0x66 from register 0x0140 (simulated)
2025-08-02 13:51:02.695 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 13:51:02.701 [Information] CANRegisterAccess: Read value 0x81 from register 0x0140 (simulated)
2025-08-02 13:51:02.707 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 13:51:02.713 [Information] CANRegisterAccess: Read value 0x9B from register 0x0140 (simulated)
2025-08-02 13:51:02.714 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 13:51:02.714 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 13:51:02.714 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 13:51:02.715 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 13:51:02.726 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 13:51:02.727 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 13:51:02.727 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 13:51:02.727 [Information] VocomService: Sending data and waiting for response
2025-08-02 13:51:02.728 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 13:51:02.778 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 13:51:02.779 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 13:51:02.779 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 13:51:02.780 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 13:51:02.780 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 13:51:02.791 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 13:51:02.792 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 13:51:02.792 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 13:51:02.803 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 13:51:02.814 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 13:51:02.825 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 13:51:02.835 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 13:51:02.846 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 13:51:02.847 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 13:51:02.847 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 13:51:02.858 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 13:51:02.859 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 13:51:02.859 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 13:51:02.870 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 13:51:02.881 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 13:51:02.893 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 13:51:02.903 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 13:51:02.914 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 13:51:02.925 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 13:51:02.926 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 13:51:02.926 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 13:51:02.937 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 13:51:02.938 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 13:51:02.938 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 13:51:02.938 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 13:51:02.939 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 13:51:02.939 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 13:51:02.939 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 13:51:02.939 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 13:51:02.940 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 13:51:02.940 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 13:51:02.941 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 13:51:02.941 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 13:51:02.941 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 13:51:02.942 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 13:51:02.942 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 13:51:02.942 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 13:51:02.942 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 13:51:03.043 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 13:51:03.043 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 13:51:03.044 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 13:51:03.044 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:03.044 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 13:51:03.045 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 13:51:03.045 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:03.045 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 13:51:03.046 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 13:51:03.046 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:03.046 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 13:51:03.047 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 13:51:03.047 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 13:51:03.047 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 13:51:03.048 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 13:51:03.099 [Information] BackupService: Initializing backup service
2025-08-02 13:51:03.099 [Information] BackupService: Backup service initialized successfully
2025-08-02 13:51:03.151 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 13:51:03.151 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 13:51:03.153 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 13:51:03.153 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 13:51:03.205 [Information] BackupService: Getting predefined backup categories
2025-08-02 13:51:03.257 [Information] MainViewModel: Services initialized successfully
2025-08-02 13:51:03.260 [Information] MainViewModel: Scanning for Vocom devices
2025-08-02 13:51:03.262 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 13:51:03.262 [Information] VocomService: Using new enhanced device detection service
2025-08-02 13:51:03.263 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 13:51:03.263 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 13:51:03.508 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 13:51:03.508 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 13:51:03.509 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 13:51:03.509 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 13:51:03.510 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 13:51:03.510 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 13:51:03.510 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 13:51:03.511 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 13:51:03.796 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 13:51:03.796 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 13:51:03.797 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 13:51:03.797 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 13:51:03.798 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 13:51:03.809 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 13:51:03.809 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 13:51:03.810 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 13:51:03.810 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 13:51:03.810 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 13:51:03.812 [Debug] VocomService: Bluetooth is enabled
2025-08-02 13:51:03.813 [Debug] VocomService: Checking if WiFi is available
2025-08-02 13:51:03.828 [Debug] VocomService: WiFi is available
2025-08-02 13:51:03.843 [Information] VocomService: Found 3 Vocom devices
2025-08-02 13:51:03.845 [Information] MainViewModel: Found 3 Vocom device(s)
2025-08-02 13:51:50.437 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-08-02 13:51:50.438 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:50.440 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-08-02 13:51:50.442 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:50.853 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-08-02 13:51:50.854 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-08-02 13:51:50.855 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 13:51:50.859 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 13:51:50.860 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 13:51:50.860 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-08-02 13:51:50.861 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 13:51:50.861 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 13:51:50.861 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 13:51:50.862 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:51:50.877 [Information] VocomService: PTT application is not running
2025-08-02 13:51:50.877 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 13:51:50.877 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 13:51:50.878 [Information] VocomService: Checking if PTT application is running
2025-08-02 13:51:50.895 [Information] VocomService: PTT application is not running
2025-08-02 13:51:50.896 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 13:51:50.896 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 13:51:50.896 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:51:50.897 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:51:50.897 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:50.898 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:50.898 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:50.898 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:50.899 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:50.899 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:50.899 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:50.899 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:50.900 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:50.900 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:51:50.900 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:50.901 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:50.901 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:50.901 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:50.901 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:50.902 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:51:50.902 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:50.903 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:51:50.903 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:50.903 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:51:50.903 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:51:50.904 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:51:50.904 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 13:51:51.919 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 13:51:51.920 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:51:51.920 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:51:51.921 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:51.921 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:51.921 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:51.922 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:51.922 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:51.922 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:51.923 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:51.923 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:51.923 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:51.924 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:51:51.924 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:51.924 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:51.925 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:51.925 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:51.925 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:51.926 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:51:51.926 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:51.926 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:51:51.927 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:51.927 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:51:51.927 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:51:51.927 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:51:51.928 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 13:51:52.930 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 13:51:52.931 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 13:51:52.939 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 13:51:52.940 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:52.940 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:52.941 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:52.941 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:52.941 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:52.942 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:52.942 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:52.942 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:52.943 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:52.943 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 13:51:52.943 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:52.943 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:52.944 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 13:51:52.944 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 13:51:52.944 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 13:51:52.945 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 13:51:52.945 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:52.945 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 13:51:52.945 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 13:51:52.946 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 13:51:52.946 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 13:51:52.946 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 13:51:52.947 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 13:51:52.947 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 13:51:52.947 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 13:51:52.948 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 13:51:52.948 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 13:51:52.948 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 13:51:52.948 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 13:51:52.949 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 13:51:52.949 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 13:51:52.949 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 13:51:52.950 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 13:51:52.950 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 13:51:52.950 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 13:51:52.951 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 13:51:52.951 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 13:51:52.951 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 13:51:52.952 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 13:51:52.952 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:52.952 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:52.953 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:52.953 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:52.954 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:52.954 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 13:51:52.954 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:52.955 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:52.955 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:52.955 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:52.956 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:52.956 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 13:51:52.957 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
