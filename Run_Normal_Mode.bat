@echo off
title VolvoFlashWR - Normal Mode
echo === VolvoFlashWR Normal Mode ===
echo.
echo Setting up environment for real hardware support...

REM Set environment variables for patched implementation
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%CD%\Libraries
set PATH=%CD%\Libraries;%CD%\Drivers\Vocom;%PATH%

echo Environment configured:
echo - USE_PATCHED_IMPLEMENTATION=true
echo - PHOENIX_VOCOM_ENABLED=true
echo - VERBOSE_LOGGING=true
echo - APCI_LIBRARY_PATH=%CD%\Libraries
echo.

REM Try to find the application executable
set APP_EXE=
if exist "VolvoFlashWR.UI\bin\Release\net8.0-windows\VolvoFlashWR.UI.exe" (
    set APP_EXE=VolvoFlashWR.UI\bin\Release\net8.0-windows\VolvoFlashWR.UI.exe
    echo Found Release build: %APP_EXE%
) else if exist "VolvoFlashWR.UI\bin\Debug\net8.0-windows\VolvoFlashWR.UI.exe" (
    set APP_EXE=VolvoFlashWR.UI\bin\Debug\net8.0-windows\VolvoFlashWR.UI.exe
    echo Found Debug build: %APP_EXE%
) else if exist "VolvoFlashWR.Launcher\bin\Release\net8.0\VolvoFlashWR.Launcher.exe" (
    set APP_EXE=VolvoFlashWR.Launcher\bin\Release\net8.0\VolvoFlashWR.Launcher.exe
    echo Found Launcher Release build: %APP_EXE%
) else if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0\VolvoFlashWR.Launcher.exe" (
    set APP_EXE=VolvoFlashWR.Launcher\bin\Debug\net8.0\VolvoFlashWR.Launcher.exe
    echo Found Launcher Debug build: %APP_EXE%
) else (
    echo ERROR: Could not find VolvoFlashWR executable!
    echo Please build the project first using:
    echo   dotnet build --configuration Release
    echo.
    pause
    exit /b 1
)

echo.
echo Starting VolvoFlashWR with architecture-aware Vocom service...
echo Application will automatically detect and use real Vocom hardware.
echo.
echo Check the Logs folder for detailed information about device detection.
echo.

REM Start the application
"%APP_EXE%"

if errorlevel 1 (
    echo.
    echo Application exited with error code %errorlevel%
    echo Check the logs in the Logs folder for more information.
    pause
) else (
    echo.
    echo Application completed successfully.
)
