Log started at 8/2/2025 3:27:17 PM
2025-08-02 15:27:17.533 [Information] LoggingService: Logging service initialized
2025-08-02 15:27:17.581 [Information] App: Starting integrated application initialization
2025-08-02 15:27:17.584 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-02 15:27:17.596 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-02 15:27:17.599 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-02 15:27:17.605 [Information] IntegratedStartupService: Setting up application environment
2025-08-02 15:27:17.629 [Information] IntegratedStartupService: Application environment setup completed
2025-08-02 15:27:17.633 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-02 15:27:17.636 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-02 15:27:17.639 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-02 15:27:17.653 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-02 15:29:10.447 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-02 15:29:12.450 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 15:29:12.453 [Information] X64LibraryResolver: ✓ Found compatible library: msvcp140.dll
2025-08-02 15:29:12.455 [Information] X64LibraryResolver: ✓ Found compatible library: vcruntime140.dll
2025-08-02 15:29:12.458 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-02 15:29:12.461 [Information] X64LibraryResolver: Found APCI library: apci.dll (x86)
2025-08-02 15:29:12.462 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apci.dll is x86, process is x64
2025-08-02 15:29:12.464 [Information] X64LibraryResolver: Found APCI library: apcidb.dll (x86)
2025-08-02 15:29:12.464 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apcidb.dll is x86, process is x64
2025-08-02 15:29:12.466 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlus.dll (x86)
2025-08-02 15:29:12.466 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 15:29:12.468 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlusData.dll (x86)
2025-08-02 15:29:12.468 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 15:29:12.470 [Information] X64LibraryResolver: Setting up architecture bridge for x86 library compatibility
2025-08-02 15:29:12.472 [Information] X64LibraryResolver: Found architecture bridge: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe (x86)
2025-08-02 15:29:12.472 [Information] X64LibraryResolver: ✓ Architecture bridge is properly configured
2025-08-02 15:29:12.474 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-02 15:29:12.481 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 15:29:12.482 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-02 15:29:12.482 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-02 15:29:12.483 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:29:12.483 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 15:29:12.484 [Information] X64LibraryResolver: Library resolution completed. Success: True
2025-08-02 15:29:12.485 [Information] IntegratedStartupService: x64 library resolution completed successfully
2025-08-02 15:29:12.485 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-02 15:29:12.486 [Information] IntegratedStartupService: Missing libraries: 1
2025-08-02 15:29:12.486 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-02 15:29:12.487 [Information] IntegratedStartupService: Incompatible libraries: 4
2025-08-02 15:29:12.487 [Information] IntegratedStartupService: Architecture bridge required: True
2025-08-02 15:29:12.487 [Information] IntegratedStartupService: Bridge path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 15:29:12.488 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 15:29:12.488 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-02 15:29:12.489 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-02 15:29:12.489 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 15:29:12.489 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 15:29:12.492 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-02 15:29:12.495 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-02 15:29:12.498 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-02 15:29:12.503 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-02 15:29:12.514 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.519 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.521 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.522 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-02 15:29:12.526 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.528 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.530 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.531 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:29:12.533 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.535 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.537 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.539 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 15:29:12.543 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.546 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.549 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.550 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 15:29:12.553 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.555 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.557 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.558 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 15:29:12.562 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.565 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.567 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.568 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 15:29:12.571 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 15:29:12.573 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.576 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 15:29:12.576 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 15:29:12.578 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-02 15:29:12.579 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-02 15:29:12.581 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-02 15:29:12.581 [Information] VCRedistBundler: Found 7 missing VC++ libraries: msvcr140.dll, api-ms-win-crt-runtime-l1-1-0.dll, api-ms-win-crt-heap-l1-1-0.dll, api-ms-win-crt-string-l1-1-0.dll, api-ms-win-crt-stdio-l1-1-0.dll, api-ms-win-crt-math-l1-1-0.dll, api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 15:29:12.583 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-02 15:29:12.585 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:30:45.918 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-02 15:30:45.926 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_09101cb3
2025-08-02 15:30:53.056 [Information] VCRedistBundler: Copied msvcp120.dll to application directory for immediate access
2025-08-02 15:30:53.061 [Information] VCRedistBundler: Copied msvcr120.dll to application directory for immediate access
2025-08-02 15:30:53.063 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-02 15:30:53.065 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-02 15:30:53.066 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-02 15:30:53.068 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-02 15:30:53.069 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-02 15:30:53.070 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-02 15:30:53.070 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-02 15:30:53.071 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-02 15:30:53.082 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-02 15:30:53.083 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-02 15:30:53.085 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-02 15:30:53.085 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 15:30:53.086 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 15:30:53.086 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 15:30:53.087 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 15:30:53.087 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 15:30:53.088 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 15:30:53.094 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-02 15:30:53.094 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-02 15:30:53.096 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-02 15:30:53.099 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-02 15:30:53.100 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-02 15:30:53.102 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-02 15:30:53.102 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-02 15:30:53.104 [Warning] IntegratedStartupService: Architecture mismatch detected for apci.dll - x86 library in x64 process
2025-08-02 15:30:53.106 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlus.dll - x86 library in x64 process
2025-08-02 15:30:53.107 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlusData.dll - x86 library in x64 process
2025-08-02 15:30:53.108 [Information] IntegratedStartupService: Architecture mismatch detected - setting up bridge environment
2025-08-02 15:30:53.108 [Information] IntegratedStartupService: Architecture bridge executable found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 15:30:53.114 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-02 15:30:53.116 [Information] LibraryExtractor: Starting library extraction process
2025-08-02 15:30:53.119 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-02 15:30:53.122 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-02 15:30:53.125 [Information] LibraryExtractor: Copying system libraries
2025-08-02 15:30:53.135 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-02 15:30:53.136 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe
2025-08-02 15:31:32.641 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 15:31:33.645 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 2/3)
2025-08-02 15:32:02.811 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 15:32:03.815 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 3/3)
2025-08-02 15:32:29.935 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 15:32:29.939 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe
2025-08-02 15:33:29.472 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 15:33:30.479 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 2/3)
2025-08-02 15:34:19.388 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 15:34:20.391 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 3/3)
2025-08-02 15:35:11.270 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 15:35:11.310 [Warning] LibraryExtractor: Failed to download library: msvcr120.dll from all available URLs
2025-08-02 15:35:11.311 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:36:44.742 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 15:36:45.749 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:38:13.067 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 15:38:14.073 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:39:42.586 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 15:39:42.591 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 15:40:31.212 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 15:40:32.219 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:41:19.721 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 15:41:20.725 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:42:06.261 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 15:42:06.265 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-02 15:42:06.266 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:43:43.206 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 15:43:44.211 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:45:44.252 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-02 15:45:45.253 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:47:13.618 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 15:47:13.626 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 15:48:00.958 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 15:48:01.963 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:48:47.607 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 15:48:48.612 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:49:29.483 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 15:49:29.488 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-02 15:49:29.488 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:51:20.695 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 15:51:20.699 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-heap-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_9d05b0cc-0652-4907-91e0-988eb0f99d51.exe' is denied.
2025-08-02 15:51:21.699 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:52:47.317 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 15:52:48.325 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:54:25.692 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 15:54:25.701 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 15:55:17.269 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 15:55:18.274 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:56:02.992 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 15:56:03.999 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 15:56:49.945 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 15:56:49.950 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-02 15:56:49.950 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 15:58:16.899 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 15:58:17.904 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 15:59:37.247 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 15:59:38.256 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 16:00:58.398 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 16:00:58.406 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 16:01:45.825 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 16:01:46.829 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 16:02:45.343 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 16:02:46.347 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 16:03:33.627 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 16:03:33.631 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-02 16:03:33.632 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-02 16:03:33.632 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-02 16:03:33.635 [Information] LibraryExtractor: Verifying library extraction
2025-08-02 16:03:33.636 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-02 16:03:33.636 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-02 16:03:33.636 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-02 16:03:33.637 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-02 16:03:33.637 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-02 16:03:33.642 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-02 16:03:33.645 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-02 16:03:33.647 [Information] DependencyManager: Initializing dependency manager
2025-08-02 16:03:33.648 [Information] DependencyManager: Setting up library search paths
2025-08-02 16:03:33.650 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:03:33.651 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 16:03:33.651 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 16:03:33.651 [Information] DependencyManager: Updated PATH environment variable
2025-08-02 16:03:33.653 [Information] DependencyManager: Verifying required directories
2025-08-02 16:03:33.654 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:03:33.654 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 16:03:33.654 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-02 16:03:33.655 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 16:03:33.657 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-02 16:03:33.679 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 16:03:33.679 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 16:03:33.687 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 16:03:33.688 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 16:03:33.689 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 16:03:33.695 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 16:03:33.698 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 16:03:33.699 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 16:03:33.707 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 16:03:33.710 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 16:03:33.711 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:03:33.711 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:03:33.712 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 16:03:33.713 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 16:03:33.713 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 16:03:33.714 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 16:03:33.714 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 16:03:33.715 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 16:03:33.716 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 16:03:33.716 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 16:03:33.717 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 16:03:33.717 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 16:03:33.718 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 16:03:33.718 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 16:03:33.719 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 16:03:33.720 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 16:03:33.721 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 16:03:33.721 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 16:03:33.722 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-02 16:03:33.722 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-02 16:03:33.724 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-02 16:03:33.726 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-02 16:03:33.727 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 16:03:33.728 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-02 16:03:33.729 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 16:03:33.729 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 16:03:33.732 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-02 16:03:33.733 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 16:03:33.734 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-02 16:03:33.735 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 16:03:33.735 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-02 16:03:33.736 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-02 16:03:33.737 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 16:03:33.738 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-02 16:03:33.739 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 16:03:33.740 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-02 16:03:33.742 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-02 16:03:33.743 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 16:03:33.743 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-02 16:03:33.745 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 16:03:33.746 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-02 16:03:33.747 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-02 16:03:33.749 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-02 16:03:33.750 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 16:03:33.752 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 16:03:33.753 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 16:03:33.753 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 16:03:33.754 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 16:03:33.754 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 16:03:33.755 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 16:03:33.755 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 16:03:33.756 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-02 16:03:33.757 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 16:03:33.758 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 16:03:33.759 [Information] DependencyManager: Setting up environment variables
2025-08-02 16:03:33.759 [Information] DependencyManager: Environment variables configured
2025-08-02 16:03:33.761 [Information] DependencyManager: Verifying library loading status
2025-08-02 16:03:34.196 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-08-02 16:03:34.197 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-02 16:03:34.197 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-02 16:03:34.199 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-02 16:03:34.202 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-02 16:03:34.214 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-02 16:03:34.217 [Information] IntegratedStartupService: Verifying system readiness
2025-08-02 16:03:34.218 [Information] IntegratedStartupService: System readiness verification passed
2025-08-02 16:03:34.218 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-02 16:03:34.220 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-02 16:03:34.220 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 16:03:34.221 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:03:34.223 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 16:03:34.224 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-02 16:03:34.225 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-02 16:03:34.225 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-02 16:03:34.226 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:03:34.226 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-02 16:03:34.226 [Information] App: Integrated startup completed successfully
2025-08-02 16:03:34.229 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-02 16:03:34.269 [Information] App: Initializing application services
2025-08-02 16:03:34.274 [Information] AppConfigurationService: Initializing configuration service
2025-08-02 16:03:34.275 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 16:03:34.370 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 16:03:34.371 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-02 16:03:34.372 [Information] App: Configuration service initialized successfully
2025-08-02 16:03:34.374 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-02 16:03:34.375 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-02 16:03:34.375 [Information] App: Environment variable exists: True, not 'false': False
2025-08-02 16:03:34.376 [Information] App: Final useDummyImplementations value: False
2025-08-02 16:03:34.376 [Information] App: Updating config to NOT use dummy implementations
2025-08-02 16:03:34.401 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-02 16:03:34.429 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 16:03:34.432 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-02 16:03:34.432 [Information] App: usePatchedImplementation flag is: True
2025-08-02 16:03:34.433 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-02 16:03:34.434 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-02 16:03:34.435 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-02 16:03:34.435 [Information] App: verboseLogging flag is: True
2025-08-02 16:03:34.438 [Information] App: Verifying real hardware requirements...
2025-08-02 16:03:34.439 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-02 16:03:34.439 [Information] App: ✓ Found critical library: apci.dll
2025-08-02 16:03:34.439 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-02 16:03:34.440 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-02 16:03:34.441 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 16:03:34.443 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-02 16:03:34.443 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-02 16:03:34.443 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-02 16:03:34.455 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-02 16:03:34.458 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-02 16:03:34.459 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-02 16:03:34.464 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-02 16:03:34.470 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-02 16:04:54.582 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 16:04:54.583 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-02 16:04:54.584 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-02 16:04:54.585 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:04:54.586 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 16:04:54.586 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 16:04:54.587 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 16:04:54.587 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 16:04:54.588 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 16:04:54.588 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-02 16:04:54.590 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-02 16:04:54.591 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-02 16:04:54.591 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 16:04:54.591 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 16:04:54.592 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-02 16:04:54.592 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-02 16:04:54.592 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-02 16:04:54.593 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-02 16:04:54.599 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-02 16:04:54.600 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-02 16:04:54.604 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-02 16:04:54.606 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-02 16:04:54.609 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-02 16:04:54.609 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-02 16:04:54.612 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-02 16:04:54.612 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-08-02 16:04:54.616 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-08-02 16:04:54.617 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-08-02 16:04:54.619 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-08-02 16:04:54.619 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-08-02 16:04:54.674 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-08-02 16:04:54.676 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-08-02 16:04:54.685 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-08-02 16:04:54.685 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-08-02 16:04:54.693 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-08-02 16:04:54.694 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-08-02 16:04:54.707 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-08-02 16:04:54.707 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-08-02 16:04:54.708 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-08-02 16:04:54.709 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:04:54.709 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-08-02 16:04:54.710 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-08-02 16:04:54.711 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-08-02 16:04:54.714 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-08-02 16:04:54.719 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-08-02 16:04:54.775 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-08-02 16:04:54.777 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 16:04:54.778 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-08-02 16:04:54.808 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 16:04:54.875 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-08-02 16:04:54.876 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-08-02 16:04:54.877 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-08-02 16:04:54.879 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 16:04:54.879 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-08-02 16:04:54.880 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 16:04:54.881 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-08-02 16:04:54.883 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 16:04:54.884 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-08-02 16:04:54.885 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-08-02 16:04:54.886 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-08-02 16:04:54.886 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-08-02 16:04:54.888 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-08-02 16:04:54.889 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-08-02 16:04:54.890 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-08-02 16:04:54.893 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-08-02 16:04:54.898 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-08-02 16:04:54.902 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 16:04:54.903 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-08-02 16:04:54.903 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-08-02 16:04:54.906 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-08-02 16:04:54.908 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-08-02 16:04:54.908 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-08-02 16:04:54.911 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-08-02 16:04:54.911 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-08-02 16:04:54.911 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-08-02 16:04:54.915 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-08-02 16:04:54.916 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-08-02 16:04:54.916 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-08-02 16:04:54.917 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-08-02 16:04:54.921 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-08-02 16:04:54.921 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-08-02 16:04:54.921 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-08-02 16:04:54.922 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:04:54.923 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-08-02 16:04:54.925 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-08-02 16:04:54.926 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 16:04:54.926 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-08-02 16:04:54.926 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-08-02 16:04:54.927 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-08-02 16:04:54.928 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-08-02 16:04:54.928 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-08-02 16:04:54.931 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-08-02 16:04:54.934 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-08-02 16:04:54.934 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-08-02 16:04:54.944 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-08-02 16:04:54.945 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-08-02 16:04:54.946 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 16:04:54.947 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 16:04:54.954 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-08-02 16:04:54.956 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.958 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.959 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-08-02 16:04:54.960 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.960 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.961 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-08-02 16:04:54.962 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.963 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-08-02 16:04:54.964 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.964 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-08-02 16:04:54.966 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.968 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.968 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-08-02 16:04:54.970 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.972 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.972 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-08-02 16:04:54.973 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 16:04:54.976 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 16:04:54.977 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.979 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.980 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-08-02 16:04:54.981 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:04:54.982 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.983 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-08-02 16:04:54.985 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.986 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.987 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-08-02 16:04:54.988 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 16:04:54.989 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 16:04:54.990 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-08-02 16:04:54.993 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 16:04:54.995 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 16:04:54.997 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 16:04:55.002 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-08-02 16:04:55.003 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 16:04:55.003 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-08-02 16:04:55.004 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-08-02 16:04:55.005 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-08-02 16:04:55.007 [Information] VocomDriver: Initializing Vocom driver
2025-08-02 16:04:55.010 [Information] VocomNativeInterop: Initializing Vocom driver
2025-08-02 16:04:55.014 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-08-02 16:04:55.014 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 16:04:55.015 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 16:04:55.017 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 16:04:55.017 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 16:04:55.024 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-08-02 16:04:55.027 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-08-02 16:04:55.030 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-08-02 16:04:55.039 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-08-02 16:04:55.039 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-08-02 16:04:55.040 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:04:55.045 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-08-02 16:04:55.050 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-08-02 16:04:55.055 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-08-02 16:04:55.057 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-08-02 16:04:55.058 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-08-02 16:04:55.059 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-08-02 16:04:55.061 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-08-02 16:04:55.062 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-08-02 16:04:55.062 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-08-02 16:04:55.063 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-08-02 16:04:55.063 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-08-02 16:04:55.063 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-08-02 16:04:55.064 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-08-02 16:04:55.064 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-08-02 16:04:55.065 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-08-02 16:04:55.066 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-08-02 16:04:55.066 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-08-02 16:04:55.067 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-08-02 16:04:55.067 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-08-02 16:04:55.067 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-08-02 16:04:55.068 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-08-02 16:04:55.069 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-08-02 16:04:55.069 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-08-02 16:04:55.070 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-08-02 16:04:55.070 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-08-02 16:04:55.070 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-08-02 16:04:55.071 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-08-02 16:04:55.071 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-08-02 16:04:55.072 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-08-02 16:04:55.072 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-08-02 16:04:55.072 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-08-02 16:04:55.073 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-08-02 16:04:55.073 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-08-02 16:04:55.073 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-08-02 16:04:55.074 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-08-02 16:04:55.074 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-08-02 16:04:55.074 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-08-02 16:04:55.075 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-08-02 16:04:55.075 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-08-02 16:04:55.076 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-08-02 16:04:55.076 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-08-02 16:04:55.077 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-08-02 16:04:55.078 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-08-02 16:04:55.078 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-08-02 16:04:55.079 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-08-02 16:04:55.080 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-08-02 16:04:55.081 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-08-02 16:04:55.081 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-08-02 16:04:55.082 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-08-02 16:04:55.084 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-08-02 16:04:55.086 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-08-02 16:04:55.087 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-08-02 16:04:55.088 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-08-02 16:04:55.090 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-08-02 16:04:55.091 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-08-02 16:04:55.092 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-08-02 16:04:55.092 [Information] VocomDriver: Vocom driver initialized successfully
2025-08-02 16:04:55.101 [Information] VocomService: Initializing Vocom service with dependencies
2025-08-02 16:04:55.102 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-08-02 16:04:55.105 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-08-02 16:04:55.106 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-08-02 16:04:55.249 [Information] WiFiCommunicationService: WiFi is available
2025-08-02 16:04:55.254 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-08-02 16:04:55.257 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-08-02 16:04:55.260 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-08-02 16:04:55.262 [Information] BluetoothCommunicationService: Bluetooth is available
2025-08-02 16:04:55.264 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-08-02 16:04:55.267 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 16:04:55.270 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 16:04:55.271 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 16:04:55.274 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 16:04:55.286 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 16:04:55.286 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 16:04:55.286 [Information] VocomService: Native USB communication service initialized
2025-08-02 16:04:55.287 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 16:04:55.288 [Information] VocomService: Connection recovery service initialized
2025-08-02 16:04:55.288 [Information] VocomService: Enhanced services initialization completed
2025-08-02 16:04:55.291 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:04:55.336 [Information] VocomService: PTT application is not running
2025-08-02 16:04:55.338 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:04:55.344 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:04:55.347 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 16:04:55.347 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-08-02 16:04:55.348 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-08-02 16:04:55.352 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 16:04:55.353 [Information] VocomService: Using new enhanced device detection service
2025-08-02 16:04:55.358 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 16:04:55.360 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 16:04:56.343 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 16:04:56.345 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 16:04:56.346 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 16:04:56.348 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 16:04:56.348 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 16:04:56.350 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 16:04:56.353 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 16:04:56.357 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 16:04:56.779 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 16:04:56.782 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 16:04:56.789 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 16:04:56.790 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 16:04:56.792 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 16:04:56.806 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 16:04:56.807 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 16:04:56.808 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 16:04:56.809 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 16:04:56.809 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:04:56.810 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:04:56.813 [Debug] VocomService: Checking if WiFi is available
2025-08-02 16:04:56.816 [Debug] VocomService: WiFi is available
2025-08-02 16:04:56.816 [Information] VocomService: Found 3 Vocom devices
2025-08-02 16:04:56.817 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-08-02 16:04:56.820 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 59a87939-9538-41a4-acdd-35ce11e415ac, Type: USB)
2025-08-02 16:04:56.820 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: a993086c-c45c-45b4-bd75-296a9f042db8, Type: Bluetooth)
2025-08-02 16:04:56.821 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 5309babd-7a01-4847-96df-c1c6e1c3be8d, Type: WiFi)
2025-08-02 16:04:56.823 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-08-02 16:04:56.823 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-08-02 16:04:56.824 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-08-02 16:04:56.824 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-08-02 16:04:56.825 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-08-02 16:04:56.825 [Information] App: Architecture-aware Vocom service created successfully
2025-08-02 16:04:56.826 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 16:04:56.826 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 16:04:56.827 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 16:04:56.827 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 16:04:56.828 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 16:04:56.829 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 16:04:56.829 [Information] VocomService: Native USB communication service initialized
2025-08-02 16:04:56.829 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 16:04:56.830 [Information] VocomService: Connection recovery service initialized
2025-08-02 16:04:56.830 [Information] VocomService: Enhanced services initialization completed
2025-08-02 16:04:56.830 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:04:56.856 [Information] VocomService: PTT application is not running
2025-08-02 16:04:56.857 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:04:56.858 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:04:56.858 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 16:04:56.858 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-02 16:04:56.859 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-02 16:04:56.859 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-02 16:04:56.925 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 16:04:56.926 [Information] VocomService: Using new enhanced device detection service
2025-08-02 16:04:56.926 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 16:04:56.927 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 16:04:57.249 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 16:04:57.249 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 16:04:57.250 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 16:04:57.250 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 16:04:57.251 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 16:04:57.252 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 16:04:57.252 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 16:04:57.253 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 16:04:57.759 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 16:04:57.759 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 16:04:57.760 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 16:04:57.760 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 16:04:57.761 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 16:04:57.774 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 16:04:57.775 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 16:04:57.775 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 16:04:57.776 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 16:04:57.776 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:04:57.777 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:04:57.777 [Debug] VocomService: Checking if WiFi is available
2025-08-02 16:04:57.778 [Debug] VocomService: WiFi is available
2025-08-02 16:04:57.778 [Information] VocomService: Found 3 Vocom devices
2025-08-02 16:04:57.779 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-08-02 16:04:57.782 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 16:04:57.783 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:04:57.805 [Information] VocomService: PTT application is not running
2025-08-02 16:04:57.812 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 16:04:57.814 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 16:04:57.817 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:04:57.840 [Information] VocomService: PTT application is not running
2025-08-02 16:04:57.841 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 16:04:57.844 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 16:04:57.847 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:04:57.850 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:04:57.852 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:57.854 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:57.854 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:57.855 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:57.856 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:57.856 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:57.856 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:57.857 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:57.857 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:57.857 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:04:57.858 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:57.859 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:57.859 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:57.860 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:57.860 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:57.860 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:04:57.862 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:04:57.862 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:04:57.863 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:04:57.863 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:04:57.863 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:04:57.864 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:04:57.864 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 16:04:58.950 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 16:04:58.950 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:04:58.951 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:04:58.954 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:58.955 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:58.955 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:58.956 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:58.956 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:58.957 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:58.957 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:58.957 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:58.959 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:58.959 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:04:58.961 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:58.962 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:58.962 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:04:58.963 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:04:58.963 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:04:58.964 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:04:58.964 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:04:58.965 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:04:58.965 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:04:58.965 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:04:58.966 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:04:58.966 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:04:58.966 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 16:04:59.987 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 16:04:59.991 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:04:59.995 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:05:00.004 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:00.014 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:00.055 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:00.075 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:00.108 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:00.123 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:00.125 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:00.126 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:00.127 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:00.127 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:05:00.128 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:00.128 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:00.129 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:00.129 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:00.129 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:00.130 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:05:00.131 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:00.131 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:05:00.132 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:00.132 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:05:00.132 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:05:00.133 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:05:00.133 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 16:05:00.135 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 16:05:00.135 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 16:05:00.440 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 16:05:00.442 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 16:05:00.443 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 16:05:00.445 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 16:05:00.446 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 16:05:00.446 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 16:05:00.449 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 16:05:00.457 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 16:05:00.460 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 16:05:00.466 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 16:05:00.474 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 16:05:00.475 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 16:05:00.476 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 16:05:00.477 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:05:00.479 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:05:00.479 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-08-02 16:05:00.502 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-02 16:05:00.513 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:00.521 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-02 16:05:00.537 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 16:05:00.545 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 16:05:00.546 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 16:05:00.549 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 16:05:00.551 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 16:05:00.553 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 16:05:00.554 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 16:05:00.555 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 16:05:00.563 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 16:05:00.564 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 16:05:00.564 [Information] VocomService: Using new enhanced device detection service
2025-08-02 16:05:00.566 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 16:05:00.566 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 16:05:01.338 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 16:05:01.338 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 16:05:01.339 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 16:05:01.339 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 16:05:01.339 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 16:05:01.340 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 16:05:01.340 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 16:05:01.341 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 16:05:01.917 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 16:05:01.918 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 16:05:01.921 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 16:05:01.924 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 16:05:01.925 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 16:05:01.932 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 16:05:01.933 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 16:05:01.933 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 16:05:01.934 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 16:05:01.934 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:05:01.939 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:05:01.940 [Debug] VocomService: Checking if WiFi is available
2025-08-02 16:05:01.940 [Debug] VocomService: WiFi is available
2025-08-02 16:05:01.941 [Information] VocomService: Found 3 Vocom devices
2025-08-02 16:05:01.942 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:05:01.942 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 16:05:01.942 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:05:01.980 [Information] VocomService: PTT application is not running
2025-08-02 16:05:01.980 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 16:05:01.981 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 16:05:01.982 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:05:02.013 [Information] VocomService: PTT application is not running
2025-08-02 16:05:02.013 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 16:05:02.014 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 16:05:02.014 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:05:02.015 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:05:02.015 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:02.016 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:02.016 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:02.017 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:02.017 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:02.018 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:02.019 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:02.022 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:02.024 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:02.024 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:05:02.025 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:02.025 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:02.025 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:02.026 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:02.026 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:02.027 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:05:02.027 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:02.028 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:05:02.028 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:02.028 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:05:02.029 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:05:02.029 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:05:02.029 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 16:05:03.029 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 16:05:03.030 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:05:03.031 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:05:03.033 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:03.033 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:03.033 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:03.034 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:03.034 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:03.035 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:03.035 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:03.035 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:03.036 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:03.039 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:05:03.044 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:03.045 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:03.046 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:03.047 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:03.048 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:03.049 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:05:03.049 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:03.049 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:05:03.050 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:03.050 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:05:03.050 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:05:03.051 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:05:03.051 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 16:05:04.052 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 16:05:04.054 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:05:04.055 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:05:04.056 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:04.056 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:04.057 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:04.057 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:04.057 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:04.058 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:04.058 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:04.058 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:04.059 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:04.059 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:05:04.059 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:04.060 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:04.060 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:05:04.060 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:05:04.060 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:05:04.061 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:05:04.061 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:04.062 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:05:04.062 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:05:04.062 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:05:04.063 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:05:04.063 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:05:04.063 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 16:05:04.064 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 16:05:04.064 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 16:05:04.064 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 16:05:04.064 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 16:05:04.065 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 16:05:04.065 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 16:05:04.065 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 16:05:04.066 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 16:05:04.066 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 16:05:04.066 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 16:05:04.066 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 16:05:04.067 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 16:05:04.067 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 16:05:04.067 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 16:05:04.068 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 16:05:04.068 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:05:04.069 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:05:04.069 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:05:04.069 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:05:04.070 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:05:04.070 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 16:05:04.071 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 16:05:04.071 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:05:04.096 [Information] VocomService: PTT application is not running
2025-08-02 16:05:04.100 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 16:05:04.102 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:05:04.106 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:05:04.108 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-08-02 16:05:04.913 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 16:05:04.914 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 16:05:04.915 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-08-02 16:05:04.915 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 16:05:04.919 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 16:05:04.921 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 16:05:04.930 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 16:05:04.933 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 16:05:04.937 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 16:05:04.946 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 16:05:04.950 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 16:05:04.963 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 16:05:04.964 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 16:05:04.965 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 16:05:04.965 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 16:05:04.966 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 16:05:04.967 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 16:05:04.967 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 16:05:04.968 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 16:05:04.968 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 16:05:04.969 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 16:05:04.969 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 16:05:04.969 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 16:05:04.970 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 16:05:04.970 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 16:05:04.971 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 16:05:04.972 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 16:05:04.972 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 16:05:04.977 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 16:05:04.989 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 16:05:04.990 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 16:05:04.995 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 16:05:04.998 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:05.006 [Information] CANRegisterAccess: Read value 0xA3 from register 0x0141 (simulated)
2025-08-02 16:05:05.008 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 16:05:05.009 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 16:05:05.010 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 16:05:05.018 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 16:05:05.018 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 16:05:05.029 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 16:05:05.030 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 16:05:05.030 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 16:05:05.047 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 16:05:05.059 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 16:05:05.060 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 16:05:05.068 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 16:05:05.069 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 16:05:05.075 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 16:05:05.081 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 16:05:05.088 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 16:05:05.088 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 16:05:05.094 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 16:05:05.096 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 16:05:05.102 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 16:05:05.103 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 16:05:05.111 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 16:05:05.112 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 16:05:05.118 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 16:05:05.119 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 16:05:05.126 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 16:05:05.126 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 16:05:05.132 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 16:05:05.132 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 16:05:05.141 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 16:05:05.142 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 16:05:05.148 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 16:05:05.152 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 16:05:05.158 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 16:05:05.158 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 16:05:05.164 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 16:05:05.164 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 16:05:05.171 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 16:05:05.172 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 16:05:05.178 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 16:05:05.178 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 16:05:05.183 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 16:05:05.183 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 16:05:05.190 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 16:05:05.191 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 16:05:05.192 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 16:05:05.198 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 16:05:05.199 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 16:05:05.199 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 16:05:05.200 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:05.207 [Information] CANRegisterAccess: Read value 0xCB from register 0x0141 (simulated)
2025-08-02 16:05:05.213 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:05.219 [Information] CANRegisterAccess: Read value 0xA4 from register 0x0141 (simulated)
2025-08-02 16:05:05.219 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 16:05:05.220 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 16:05:05.222 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 16:05:05.222 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 16:05:05.228 [Information] CANRegisterAccess: Read value 0x6F from register 0x0140 (simulated)
2025-08-02 16:05:05.234 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 16:05:05.240 [Information] CANRegisterAccess: Read value 0xED from register 0x0140 (simulated)
2025-08-02 16:05:05.246 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 16:05:05.252 [Information] CANRegisterAccess: Read value 0xC1 from register 0x0140 (simulated)
2025-08-02 16:05:05.258 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 16:05:05.264 [Information] CANRegisterAccess: Read value 0x51 from register 0x0140 (simulated)
2025-08-02 16:05:05.264 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 16:05:05.264 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 16:05:05.268 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 16:05:05.269 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 16:05:05.280 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 16:05:05.281 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 16:05:05.281 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 16:05:05.286 [Information] VocomService: Sending data and waiting for response
2025-08-02 16:05:05.287 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 16:05:05.337 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 16:05:05.339 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 16:05:05.339 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 16:05:05.341 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 16:05:05.342 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 16:05:05.352 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 16:05:05.353 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 16:05:05.354 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 16:05:05.365 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 16:05:05.376 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 16:05:05.387 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 16:05:05.398 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 16:05:05.409 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 16:05:05.412 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 16:05:05.412 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 16:05:05.423 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 16:05:05.424 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 16:05:05.425 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 16:05:05.436 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 16:05:05.447 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 16:05:05.458 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 16:05:05.469 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 16:05:05.480 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 16:05:05.491 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 16:05:05.493 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 16:05:05.494 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 16:05:05.505 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 16:05:05.506 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 16:05:05.506 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 16:05:05.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 16:05:05.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 16:05:05.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 16:05:05.508 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 16:05:05.508 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 16:05:05.508 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 16:05:05.509 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 16:05:05.509 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 16:05:05.509 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 16:05:05.510 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 16:05:05.510 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 16:05:05.510 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 16:05:05.510 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 16:05:05.511 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 16:05:05.612 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 16:05:05.612 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 16:05:05.615 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 16:05:05.617 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:05.617 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 16:05:05.618 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 16:05:05.618 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:05.619 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 16:05:05.619 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 16:05:05.620 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:05.620 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 16:05:05.620 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 16:05:05.622 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:05.622 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 16:05:05.623 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 16:05:05.624 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-08-02 16:05:05.625 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-02 16:05:05.626 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-02 16:05:05.630 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-02 16:05:05.632 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-02 16:05:05.641 [Information] BackupService: Initializing backup service
2025-08-02 16:05:05.642 [Information] BackupService: Backup service initialized successfully
2025-08-02 16:05:05.642 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-02 16:05:05.642 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-02 16:05:05.645 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-02 16:05:05.702 [Information] BackupService: Compressing backup data
2025-08-02 16:05:05.718 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-08-02 16:05:05.720 [Information] BackupServiceFactory: Created template for category: Production
2025-08-02 16:05:05.720 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-02 16:05:05.721 [Information] BackupService: Compressing backup data
2025-08-02 16:05:05.723 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-08-02 16:05:05.723 [Information] BackupServiceFactory: Created template for category: Development
2025-08-02 16:05:05.723 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-02 16:05:05.724 [Information] BackupService: Compressing backup data
2025-08-02 16:05:05.725 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (450 bytes)
2025-08-02 16:05:05.726 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-02 16:05:05.726 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-02 16:05:05.727 [Information] BackupService: Compressing backup data
2025-08-02 16:05:05.728 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-08-02 16:05:05.728 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-02 16:05:05.729 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-02 16:05:05.729 [Information] BackupService: Compressing backup data
2025-08-02 16:05:05.730 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-08-02 16:05:05.731 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-02 16:05:05.731 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-02 16:05:05.732 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-02 16:05:05.733 [Information] BackupService: Compressing backup data
2025-08-02 16:05:05.739 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-08-02 16:05:05.739 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-02 16:05:05.740 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-02 16:05:05.742 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-02 16:05:05.745 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 16:05:05.748 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 16:05:05.838 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 16:05:05.839 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 16:05:05.841 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-02 16:05:05.841 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-02 16:05:05.842 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-02 16:05:05.846 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-02 16:05:05.847 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-02 16:05:05.851 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-02 16:05:05.851 [Information] App: Flash operation monitor service initialized successfully
2025-08-02 16:05:05.865 [Information] LicensingService: Initializing licensing service
2025-08-02 16:05:05.916 [Information] LicensingService: License information loaded successfully
2025-08-02 16:05:05.918 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-02 16:05:05.919 [Information] App: Licensing service initialized successfully
2025-08-02 16:05:05.919 [Information] App: License status: Trial
2025-08-02 16:05:05.920 [Information] App: Trial period: 24 days remaining
2025-08-02 16:05:05.920 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-02 16:05:05.953 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-02 16:05:06.212 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 16:05:06.212 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 16:05:06.213 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 16:05:06.213 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 16:05:06.214 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 16:05:06.214 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 16:05:06.215 [Information] VocomService: Native USB communication service initialized
2025-08-02 16:05:06.215 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 16:05:06.215 [Information] VocomService: Connection recovery service initialized
2025-08-02 16:05:06.215 [Information] VocomService: Enhanced services initialization completed
2025-08-02 16:05:06.216 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:05:06.238 [Information] VocomService: PTT application is not running
2025-08-02 16:05:06.238 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:05:06.241 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:05:06.242 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 16:05:06.294 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 16:05:06.294 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 16:05:06.294 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 16:05:06.295 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 16:05:06.295 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-02 16:05:06.296 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 12881f57-d057-40f1-93df-779c0906a7dc
2025-08-02 16:05:06.298 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-02 16:05:06.299 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 16:05:06.299 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 16:05:06.299 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 16:05:06.302 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 16:05:06.302 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 16:05:06.307 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 16:05:06.308 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 16:05:06.308 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 16:05:06.320 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 16:05:06.321 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 16:05:06.321 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 16:05:06.322 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 16:05:06.322 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 16:05:06.323 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 16:05:06.323 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 16:05:06.323 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 16:05:06.323 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 16:05:06.324 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 16:05:06.324 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 16:05:06.324 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 16:05:06.325 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 16:05:06.325 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 16:05:06.325 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 16:05:06.326 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 16:05:06.326 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 16:05:06.326 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 16:05:06.333 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 16:05:06.333 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 16:05:06.333 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 16:05:06.334 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:06.340 [Information] CANRegisterAccess: Read value 0x7E from register 0x0141 (simulated)
2025-08-02 16:05:06.346 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:06.352 [Information] CANRegisterAccess: Read value 0x89 from register 0x0141 (simulated)
2025-08-02 16:05:06.352 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 16:05:06.353 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 16:05:06.353 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 16:05:06.360 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 16:05:06.361 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 16:05:06.366 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 16:05:06.367 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 16:05:06.369 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 16:05:06.375 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 16:05:06.375 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 16:05:06.375 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 16:05:06.382 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 16:05:06.382 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 16:05:06.388 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 16:05:06.389 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 16:05:06.396 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 16:05:06.396 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 16:05:06.403 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 16:05:06.403 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 16:05:06.410 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 16:05:06.410 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 16:05:06.443 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 16:05:06.444 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 16:05:06.470 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 16:05:06.471 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 16:05:06.478 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 16:05:06.478 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 16:05:06.484 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 16:05:06.485 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 16:05:06.492 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 16:05:06.492 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 16:05:06.499 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 16:05:06.500 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 16:05:06.506 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 16:05:06.507 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 16:05:06.513 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 16:05:06.514 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 16:05:06.520 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 16:05:06.521 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 16:05:06.527 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 16:05:06.528 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 16:05:06.534 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 16:05:06.535 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 16:05:06.541 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 16:05:06.542 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 16:05:06.542 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 16:05:06.548 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 16:05:06.549 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 16:05:06.549 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 16:05:06.550 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:06.555 [Information] CANRegisterAccess: Read value 0xB1 from register 0x0141 (simulated)
2025-08-02 16:05:06.561 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 16:05:06.567 [Information] CANRegisterAccess: Read value 0xBE from register 0x0141 (simulated)
2025-08-02 16:05:06.568 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 16:05:06.568 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 16:05:06.569 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 16:05:06.569 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 16:05:06.575 [Information] CANRegisterAccess: Read value 0x44 from register 0x0140 (simulated)
2025-08-02 16:05:06.581 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 16:05:06.587 [Information] CANRegisterAccess: Read value 0xBD from register 0x0140 (simulated)
2025-08-02 16:05:06.588 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 16:05:06.589 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 16:05:06.595 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 16:05:06.596 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 16:05:06.606 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 16:05:06.607 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 16:05:06.607 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 16:05:06.607 [Information] VocomService: Sending data and waiting for response
2025-08-02 16:05:06.608 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 16:05:06.658 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 16:05:06.659 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 16:05:06.659 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 16:05:06.660 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 16:05:06.660 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 16:05:06.672 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 16:05:06.673 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 16:05:06.673 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 16:05:06.684 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 16:05:06.695 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 16:05:06.706 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 16:05:06.717 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 16:05:06.729 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 16:05:06.729 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 16:05:06.730 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 16:05:06.740 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 16:05:06.741 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 16:05:06.741 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 16:05:06.753 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 16:05:06.764 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 16:05:06.775 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 16:05:06.787 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 16:05:06.798 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 16:05:06.809 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 16:05:06.810 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 16:05:06.810 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 16:05:06.821 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 16:05:06.822 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 16:05:06.822 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 16:05:06.823 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 16:05:06.823 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 16:05:06.823 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 16:05:06.823 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 16:05:06.824 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 16:05:06.825 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 16:05:06.825 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 16:05:06.826 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 16:05:06.826 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 16:05:06.826 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 16:05:06.827 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 16:05:06.827 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 16:05:06.827 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 16:05:06.827 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 16:05:06.928 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 16:05:06.929 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 16:05:06.930 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 16:05:06.930 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:06.931 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 16:05:06.931 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 16:05:06.931 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:06.932 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 16:05:06.932 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 16:05:06.933 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:06.933 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 16:05:06.933 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 16:05:06.934 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 16:05:06.934 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 16:05:06.935 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 16:05:06.987 [Information] BackupService: Initializing backup service
2025-08-02 16:05:06.988 [Information] BackupService: Backup service initialized successfully
2025-08-02 16:05:07.040 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 16:05:07.041 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 16:05:07.042 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 16:05:07.043 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 16:05:07.095 [Information] BackupService: Getting predefined backup categories
2025-08-02 16:05:07.149 [Information] MainViewModel: Services initialized successfully
2025-08-02 16:05:07.153 [Information] MainViewModel: Scanning for Vocom devices
2025-08-02 16:05:07.155 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 16:05:07.155 [Information] VocomService: Using new enhanced device detection service
2025-08-02 16:05:07.156 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 16:05:07.156 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 16:05:07.432 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 16:05:07.433 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 16:05:07.433 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 16:05:07.433 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 16:05:07.433 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 16:05:07.434 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 16:05:07.435 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 16:05:07.436 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 16:05:07.845 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 16:05:07.845 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 16:05:07.846 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 16:05:07.846 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 16:05:07.847 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 16:05:07.857 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 16:05:07.858 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 16:05:07.858 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 16:05:07.859 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 16:05:07.859 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 16:05:07.861 [Debug] VocomService: Bluetooth is enabled
2025-08-02 16:05:07.861 [Debug] VocomService: Checking if WiFi is available
2025-08-02 16:05:07.862 [Debug] VocomService: WiFi is available
2025-08-02 16:05:07.863 [Information] VocomService: Found 3 Vocom devices
2025-08-02 16:05:07.864 [Information] MainViewModel: Found 3 Vocom device(s)
2025-08-02 16:09:38.876 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-08-02 16:09:38.877 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:38.880 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-08-02 16:09:38.881 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-08-02 16:09:39.295 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-08-02 16:09:39.296 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-08-02 16:09:39.297 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 16:09:39.301 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 16:09:39.303 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 16:09:39.303 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-08-02 16:09:39.304 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 16:09:39.304 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 16:09:39.304 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 16:09:39.305 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:09:39.330 [Information] VocomService: PTT application is not running
2025-08-02 16:09:39.331 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 16:09:39.331 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 16:09:39.331 [Information] VocomService: Checking if PTT application is running
2025-08-02 16:09:39.356 [Information] VocomService: PTT application is not running
2025-08-02 16:09:39.357 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 16:09:39.357 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 16:09:39.358 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:09:39.358 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:09:39.359 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:39.359 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:39.359 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:39.360 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:39.360 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:39.360 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:39.361 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:39.361 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:39.361 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:39.362 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:09:39.362 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:39.363 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:39.363 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:39.364 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:39.364 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:39.364 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:09:39.365 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:09:39.365 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:09:39.365 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:09:39.366 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:09:39.366 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:09:39.366 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:09:39.367 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 16:09:40.376 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 16:09:40.376 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:09:40.377 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:09:40.378 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:40.378 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:40.378 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:40.379 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:40.379 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:40.379 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:40.380 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:40.380 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:40.380 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:40.381 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:09:40.381 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:40.381 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:40.381 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:40.382 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:40.382 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:40.383 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:09:40.383 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:09:40.383 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:09:40.384 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:09:40.384 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:09:40.384 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:09:40.385 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:09:40.385 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 16:09:41.391 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 16:09:41.391 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 16:09:41.392 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 16:09:41.392 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:41.393 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:41.393 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:41.393 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:41.394 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:41.394 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:41.394 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:41.395 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:41.395 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:41.395 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 16:09:41.396 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:41.396 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:41.396 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 16:09:41.396 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 16:09:41.397 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 16:09:41.397 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 16:09:41.398 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:09:41.398 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 16:09:41.398 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 16:09:41.399 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 16:09:41.399 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 16:09:41.399 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 16:09:41.399 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 16:09:41.400 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 16:09:41.400 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 16:09:41.400 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 16:09:41.401 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 16:09:41.401 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 16:09:41.401 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 16:09:41.402 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 16:09:41.402 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 16:09:41.403 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 16:09:41.403 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 16:09:41.403 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 16:09:41.404 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 16:09:41.404 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 16:09:41.404 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 16:09:41.405 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 16:09:41.405 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:09:41.405 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:09:41.406 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:09:41.407 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:09:41.407 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:09:41.407 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 16:09:41.408 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:41.408 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:41.409 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:41.409 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:41.409 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:41.410 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 16:09:41.411 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
