Log started at 8/4/2025 11:07:28 AM
2025-08-04 11:07:28.594 [Information] LoggingService: Logging service initialized
2025-08-04 11:07:28.613 [Information] App: Starting integrated application initialization
2025-08-04 11:07:28.615 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-04 11:07:28.617 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-04 11:07:28.620 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-04 11:07:28.623 [Information] IntegratedStartupService: Setting up application environment
2025-08-04 11:07:28.624 [Information] IntegratedStartupService: Application environment setup completed
2025-08-04 11:07:28.627 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-04 11:07:28.630 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-04 11:07:28.633 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-04 11:07:28.654 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-04 11:09:10.627 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-04 11:09:12.630 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-04 11:09:12.694 [Information] X64LibraryResolver: ✓ Found compatible library: msvcp140.dll
2025-08-04 11:09:12.696 [Information] X64LibraryResolver: ✓ Found compatible library: vcruntime140.dll
2025-08-04 11:09:12.699 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-04 11:09:12.822 [Information] X64LibraryResolver: Found APCI library: apci.dll (x86)
2025-08-04 11:09:12.823 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apci.dll is x86, process is x64
2025-08-04 11:09:12.824 [Information] X64LibraryResolver: Found APCI library: apcidb.dll (x86)
2025-08-04 11:09:12.825 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apcidb.dll is x86, process is x64
2025-08-04 11:09:13.195 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlus.dll (x86)
2025-08-04 11:09:13.195 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlus.dll is x86, process is x64
2025-08-04 11:09:13.324 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlusData.dll (x86)
2025-08-04 11:09:13.325 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlusData.dll is x86, process is x64
2025-08-04 11:09:13.327 [Information] X64LibraryResolver: Setting up architecture bridge for x86 library compatibility
2025-08-04 11:09:13.328 [Information] X64LibraryResolver: Found architecture bridge: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe (x86)
2025-08-04 11:09:13.328 [Information] X64LibraryResolver: ✓ Architecture bridge is properly configured
2025-08-04 11:09:13.330 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-04 11:09:13.335 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-04 11:09:13.335 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-04 11:09:13.336 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-04 11:09:13.337 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-04 11:09:13.337 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-04 11:09:13.339 [Information] X64LibraryResolver: Library resolution completed. Success: True
2025-08-04 11:09:13.341 [Information] IntegratedStartupService: x64 library resolution completed successfully
2025-08-04 11:09:13.342 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-04 11:09:13.342 [Information] IntegratedStartupService: Missing libraries: 1
2025-08-04 11:09:13.343 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-04 11:09:13.344 [Information] IntegratedStartupService: Incompatible libraries: 4
2025-08-04 11:09:13.346 [Information] IntegratedStartupService: Architecture bridge required: True
2025-08-04 11:09:13.347 [Information] IntegratedStartupService: Bridge path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 11:09:13.348 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-04 11:09:13.348 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-04 11:09:13.349 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-04 11:09:13.349 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-04 11:09:13.350 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-04 11:09:13.352 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-04 11:09:13.355 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-04 11:09:13.357 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-04 11:09:13.361 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-04 11:09:13.376 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.384 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.387 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.388 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-04 11:09:13.393 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.395 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.397 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.398 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 11:09:13.402 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.405 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.408 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.409 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 11:09:13.412 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.414 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.417 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.417 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 11:09:13.422 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.425 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.427 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.428 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 11:09:13.431 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.434 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.437 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.439 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 11:09:13.443 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-04 11:09:13.446 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.448 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-04 11:09:13.449 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 11:09:13.451 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-04 11:09:13.451 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-04 11:09:13.453 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-04 11:09:13.454 [Information] VCRedistBundler: Found 7 missing VC++ libraries: msvcr140.dll, api-ms-win-crt-runtime-l1-1-0.dll, api-ms-win-crt-heap-l1-1-0.dll, api-ms-win-crt-string-l1-1-0.dll, api-ms-win-crt-stdio-l1-1-0.dll, api-ms-win-crt-math-l1-1-0.dll, api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 11:09:13.458 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-04 11:09:13.462 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:10:52.745 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-04 11:10:52.752 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_5a0bdf71
2025-08-04 11:11:03.282 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-04 11:11:03.284 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-04 11:11:03.284 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-04 11:11:03.336 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-04 11:11:03.348 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-04 11:11:03.350 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-04 11:11:03.351 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-04 11:11:03.351 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-04 11:11:03.453 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-04 11:11:03.454 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-04 11:11:03.456 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-04 11:11:03.457 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 11:11:03.457 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 11:11:03.458 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 11:11:03.460 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 11:11:03.461 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 11:11:03.462 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 11:11:03.495 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-04 11:11:03.496 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-04 11:11:03.497 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-04 11:11:03.500 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-04 11:11:03.501 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-04 11:11:03.504 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-04 11:11:03.504 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-04 11:11:03.632 [Warning] IntegratedStartupService: Architecture mismatch detected for apci.dll - x86 library in x64 process
2025-08-04 11:11:03.966 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlus.dll - x86 library in x64 process
2025-08-04 11:11:04.086 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlusData.dll - x86 library in x64 process
2025-08-04 11:11:04.087 [Information] IntegratedStartupService: Architecture mismatch detected - setting up bridge environment
2025-08-04 11:11:04.088 [Information] IntegratedStartupService: Architecture bridge executable found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 11:11:04.092 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-04 11:11:04.096 [Information] LibraryExtractor: Starting library extraction process
2025-08-04 11:11:04.098 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-04 11:11:04.102 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-04 11:11:04.104 [Information] LibraryExtractor: Copying system libraries
2025-08-04 11:11:04.113 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-04 11:11:04.114 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe
2025-08-04 11:11:31.906 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 11:11:32.911 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 2/3)
2025-08-04 11:12:00.209 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 11:12:01.212 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 3/3)
2025-08-04 11:12:44.519 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 11:12:44.524 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe
2025-08-04 11:13:36.325 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 11:13:37.330 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 2/3)
2025-08-04 11:14:32.507 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 11:14:33.511 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 3/3)
2025-08-04 11:16:33.519 [Warning] LibraryExtractor: Download timeout for redistributable msvcr120.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-04 11:16:33.519 [Warning] LibraryExtractor: Failed to download library: msvcr120.dll from all available URLs
2025-08-04 11:16:33.520 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:18:33.523 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-04 11:18:34.524 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:20:34.528 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-04 11:20:35.530 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:22:35.534 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-04 11:22:35.535 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 11:24:01.998 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 11:24:03.003 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:24:49.152 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 11:24:50.156 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:25:35.089 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 11:25:35.095 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-04 11:25:35.096 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:27:35.099 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-04 11:27:36.099 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:29:13.174 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 11:29:14.180 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:30:44.739 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 11:30:44.746 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 11:31:27.499 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 11:31:28.504 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:32:08.307 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 11:32:09.312 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:33:11.880 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 11:33:11.886 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-04 11:33:11.886 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:35:38.365 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 11:35:38.367 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-heap-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_d5b86d97-522a-4a2e-a216-7ddb0dfa4972.exe' is denied.
2025-08-04 11:35:39.368 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:37:39.371 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-04 11:37:40.371 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:39:45.167 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 11:39:45.177 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 11:40:49.486 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 11:40:49.495 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-heap-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_89705d91-8348-45ac-9a64-40922c043b1c.exe' is denied.
2025-08-04 11:40:50.496 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:41:40.958 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 11:41:41.967 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:42:26.738 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 11:42:26.744 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-04 11:42:26.745 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:44:09.926 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-string-l1-1-0.dll
2025-08-04 11:44:09.927 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_cffe403d-e691-4df9-9204-9a1b89d028c2.exe' is denied.
2025-08-04 11:44:10.930 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:45:32.468 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 11:45:33.477 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:46:52.063 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 11:46:52.074 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 11:47:35.338 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 11:47:36.446 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 11:48:31.860 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 11:48:32.865 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 11:49:43.823 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 11:49:43.827 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-04 11:49:43.828 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-04 11:49:43.828 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-04 11:49:43.831 [Information] LibraryExtractor: Verifying library extraction
2025-08-04 11:49:43.832 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-04 11:49:43.832 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-04 11:49:43.832 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-04 11:49:43.833 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-04 11:49:43.834 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-04 11:49:43.839 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-04 11:49:43.840 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-04 11:49:43.842 [Information] DependencyManager: Initializing dependency manager
2025-08-04 11:49:43.843 [Information] DependencyManager: Setting up library search paths
2025-08-04 11:49:43.845 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-04 11:49:43.846 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-04 11:49:43.846 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-04 11:49:43.846 [Information] DependencyManager: Updated PATH environment variable
2025-08-04 11:49:43.848 [Information] DependencyManager: Verifying required directories
2025-08-04 11:49:43.848 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-04 11:49:43.849 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-04 11:49:43.849 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-04 11:49:43.849 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-04 11:49:43.851 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-04 11:49:43.866 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-04 11:49:43.867 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-04 11:49:43.874 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-04 11:49:43.875 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-04 11:49:43.876 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-04 11:49:43.882 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-04 11:49:43.885 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-04 11:49:43.886 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-04 11:49:43.892 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-04 11:49:43.893 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-04 11:49:43.894 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 11:49:43.894 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 11:49:43.895 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 11:49:43.896 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 11:49:43.896 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 11:49:43.897 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 11:49:43.898 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 11:49:43.898 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 11:49:43.899 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 11:49:43.899 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 11:49:43.900 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 11:49:43.901 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 11:49:43.903 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-04 11:49:43.903 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-04 11:49:43.905 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-04 11:49:43.905 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-04 11:49:43.906 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-04 11:49:43.907 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-04 11:49:43.907 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-04 11:49:43.907 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-04 11:49:43.909 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-04 11:49:43.913 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-04 11:49:43.913 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-04 11:49:43.914 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-04 11:49:43.914 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-04 11:49:43.915 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-04 11:49:43.917 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-04 11:49:43.918 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-04 11:49:43.919 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-04 11:49:43.919 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-04 11:49:43.920 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-04 11:49:43.921 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-04 11:49:43.921 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-04 11:49:43.922 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-04 11:49:43.922 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-04 11:49:43.922 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-04 11:49:43.924 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-04 11:49:43.925 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-04 11:49:43.925 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-04 11:49:43.926 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-04 11:49:43.926 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-04 11:49:43.928 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-04 11:49:43.929 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-04 11:49:43.929 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-04 11:49:43.931 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-04 11:49:43.932 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-04 11:49:43.932 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-04 11:49:43.932 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-04 11:49:43.933 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-04 11:49:43.933 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-04 11:49:43.934 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-04 11:49:43.935 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-04 11:49:43.936 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-04 11:49:43.938 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-04 11:49:43.939 [Information] DependencyManager: Setting up environment variables
2025-08-04 11:49:43.939 [Information] DependencyManager: Environment variables configured
2025-08-04 11:49:43.941 [Information] DependencyManager: Verifying library loading status
2025-08-04 11:49:44.295 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-08-04 11:49:44.295 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-04 11:49:44.295 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-04 11:49:44.298 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-04 11:49:44.300 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-04 11:49:44.310 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-04 11:49:44.312 [Information] IntegratedStartupService: Verifying system readiness
2025-08-04 11:49:44.313 [Information] IntegratedStartupService: System readiness verification passed
2025-08-04 11:49:44.313 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-04 11:49:44.315 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-04 11:49:44.315 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-04 11:49:44.316 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-04 11:49:44.316 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-04 11:49:44.316 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-04 11:49:44.317 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-04 11:49:44.317 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-04 11:49:44.317 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-04 11:49:44.318 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-04 11:49:44.318 [Information] App: Integrated startup completed successfully
2025-08-04 11:49:44.322 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-04 11:49:44.542 [Information] App: Initializing application services
2025-08-04 11:49:44.544 [Information] AppConfigurationService: Initializing configuration service
2025-08-04 11:49:44.544 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-04 11:49:44.619 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-04 11:49:44.620 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-04 11:49:44.621 [Information] App: Configuration service initialized successfully
2025-08-04 11:49:44.622 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-04 11:49:44.622 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-04 11:49:44.623 [Information] App: Environment variable exists: True, not 'false': False
2025-08-04 11:49:44.623 [Information] App: Final useDummyImplementations value: False
2025-08-04 11:49:44.623 [Information] App: Updating config to NOT use dummy implementations
2025-08-04 11:49:44.625 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-04 11:49:44.644 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-04 11:49:44.646 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-04 11:49:44.646 [Information] App: usePatchedImplementation flag is: True
2025-08-04 11:49:44.646 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-04 11:49:44.647 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-04 11:49:44.647 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-04 11:49:44.647 [Information] App: verboseLogging flag is: True
2025-08-04 11:49:44.651 [Information] App: Verifying real hardware requirements...
2025-08-04 11:49:44.652 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-04 11:49:44.652 [Information] App: ✓ Found critical library: apci.dll
2025-08-04 11:49:44.652 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-04 11:49:44.653 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-04 11:49:44.655 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-04 11:49:44.656 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-04 11:49:44.656 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-04 11:49:44.657 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-04 11:49:44.668 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-04 11:49:44.670 [Information] VCRuntimeInstaller: Starting Visual C++ runtime dependency installation
2025-08-04 11:49:44.670 [Information] VCRuntimeInstaller: Process architecture: x64
2025-08-04 11:49:44.677 [Information] VCRuntimeInstaller: Found 1 missing Visual C++ runtime libraries
2025-08-04 11:49:44.679 [Information] VCRuntimeInstaller: Installing Visual C++ redistributable: vc_redist.x64.exe
2025-08-04 11:49:44.683 [Information] VCRuntimeInstaller: Attempting to download from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:52:11.314 [Information] VCRuntimeInstaller: Downloaded redistributable to: C:\Users\<USER>\AppData\Local\Temp\vcredist_767d4578-fec5-415d-8779-02e351a32810.exe
2025-08-04 11:52:11.317 [Information] VCRuntimeInstaller: Running Visual C++ redistributable installer: C:\Users\<USER>\AppData\Local\Temp\vcredist_767d4578-fec5-415d-8779-02e351a32810.exe
2025-08-04 11:52:17.390 [Information] VCRuntimeInstaller: Visual C++ redistributable installer exit code: 0
2025-08-04 11:52:17.395 [Information] VCRuntimeInstaller: Successfully installed Visual C++ redistributable from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:52:17.400 [Warning] VCRuntimeInstaller: Visual C++ runtime installation partially successful. 1 libraries still missing
2025-08-04 11:52:17.400 [Warning] App: VCRuntimeInstaller had issues: 
2025-08-04 11:52:17.404 [Information] EnhancedRuntimeInstaller: Starting enhanced runtime dependency installation
2025-08-04 11:52:17.404 [Information] EnhancedRuntimeInstaller: Process architecture: x64
2025-08-04 11:52:17.410 [Warning] EnhancedRuntimeInstaller: Missing runtime library: Microsoft Visual C++ 2015-2022 Runtime (msvcr140.dll)
2025-08-04 11:52:17.411 [Information] EnhancedRuntimeInstaller: Found 1 missing runtime libraries
2025-08-04 11:52:17.415 [Information] EnhancedRuntimeInstaller: Downloading Visual C++ Redistributable (x64)
2025-08-04 11:53:27.496 [Information] EnhancedRuntimeInstaller: Installing Visual C++ Redistributable (this may take a few minutes)
2025-08-04 11:53:38.730 [Information] EnhancedRuntimeInstaller: Visual C++ Redistributable installed successfully
2025-08-04 11:53:38.737 [Information] EnhancedRuntimeInstaller: Checking Universal CRT availability
2025-08-04 11:53:38.737 [Information] EnhancedRuntimeInstaller: Windows 10+ detected - Universal CRT should be available via Windows Update
2025-08-04 11:53:38.739 [Warning] EnhancedRuntimeInstaller: Missing runtime library: Microsoft Visual C++ 2015-2022 Runtime (msvcr140.dll)
2025-08-04 11:53:38.740 [Information] EnhancedRuntimeInstaller: Runtime installation completed: 0/1 libraries resolved
2025-08-04 11:53:38.740 [Warning] App: Enhanced runtime installer also had issues, trying final fallback resolver
2025-08-04 11:53:38.743 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-04 11:53:38.743 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-04 11:53:38.746 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-04 11:53:38.751 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-04 11:54:55.770 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-04 11:54:55.771 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-04 11:54:55.771 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-04 11:54:55.772 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 11:54:55.773 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 11:54:55.774 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 11:54:55.775 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 11:54:55.776 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 11:54:55.776 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 11:54:55.778 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-04 11:54:55.779 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-04 11:54:55.780 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-04 11:54:55.780 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-04 11:54:55.781 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 11:54:55.781 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-04 11:54:55.781 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-04 11:54:55.782 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-04 11:54:55.782 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-04 11:54:55.785 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-04 11:54:55.786 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-04 11:54:55.788 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-04 11:54:55.789 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-04 11:54:55.790 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-04 11:54:55.790 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-04 11:54:55.792 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-04 11:54:55.793 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridge service immediately
2025-08-04 11:54:55.794 [Information] ArchitectureAwareVocomServiceFactory: Architecture bridge is available and compatible
2025-08-04 11:54:55.794 [Information] ArchitectureAwareVocomServiceFactory: Architecture bridge available - creating bridged service for x86 library compatibility
2025-08-04 11:54:55.795 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-08-04 11:54:55.799 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-08-04 11:54:55.802 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge (attempt 1/3)
2025-08-04 11:54:55.820 [Information] VocomArchitectureBridge: Cleaning up existing bridge processes
2025-08-04 11:54:55.823 [Information] VocomArchitectureBridge: No existing bridge processes found
2025-08-04 11:54:55.839 [Information] VocomArchitectureBridge: Created named pipe server: VocomBridge_13440_638899052957972291_ab7e2e156cc54d8689b151fb40c4cd1f
2025-08-04 11:54:55.841 [Information] VocomArchitectureBridge: Looking for bridge executable at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 11:54:55.931 [Information] VocomArchitectureBridge: Started bridge process with PID 15916
2025-08-04 11:54:56.934 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-08-04 11:54:57.049 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-08-04 11:54:57.066 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: Initialize
2025-08-04 11:54:57.066 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:54:57.070 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"Initialize","Data":"{\u0022LibrariesPath\u0022:\u0022D:\\\\001-Software Engineering\\\\S.A.H\\\\Development\\\\S.A.H.VolvoFlashWR\\\\VolvoFlashWR_Export_With_Fix\\\\Libraries\u0022}"} ===
2025-08-04 11:54:57.070 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 192 ===
2025-08-04 11:54:57.071 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:54:57.090 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:54:57.090 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:54:57.091 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:54:57.189 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 80 bytes from pipe ===
2025-08-04 11:54:57.189 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":null,"Message":"Bridge service initialized successfully"} ===
2025-08-04 11:54:57.189 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:54:57.192 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:54:57.193 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Bridge service initialized successfully, Data length= ===
2025-08-04 11:54:57.194 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-08-04 11:54:57.195 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-08-04 11:54:57.196 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-08-04 11:54:57.197 [Information] App: Architecture-aware Vocom service created successfully
2025-08-04 11:54:57.197 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-08-04 11:54:57.197 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge (attempt 1/3)
2025-08-04 11:54:57.198 [Information] VocomArchitectureBridge: Bridge already initialized
2025-08-04 11:54:57.198 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-08-04 11:54:57.198 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-04 11:54:57.199 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-04 11:54:57.199 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-04 11:54:57.246 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-08-04 11:54:57.247 [Information] BridgedVocomService: === About to call _bridge.DetectDevicesAsync() ===
2025-08-04 11:54:57.257 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-08-04 11:54:57.257 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-08-04 11:54:57.257 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-08-04 11:54:57.258 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-08-04 11:54:57.258 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-08-04 11:54:57.258 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:54:57.259 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-08-04 11:54:57.259 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-08-04 11:54:57.260 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:54:57.260 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:54:57.261 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:54:57.261 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:54:58.327 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-08-04 11:54:58.327 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-08-04 11:54:58.328 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:54:58.328 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:54:58.329 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-08-04 11:54:58.330 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-08-04 11:54:58.331 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:54:58.333 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:54:58.333 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-08-04 11:54:58.342 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-08-04 11:54:58.342 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-08-04 11:54:58.345 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-08-04 11:54:58.348 [Information] BridgedVocomService: === Returned from _bridge.DetectDevicesAsync() ===
2025-08-04 11:54:58.349 [Information] BridgedVocomService: Found 1 Vocom devices
2025-08-04 11:54:58.349 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-08-04 11:54:58.357 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-08-04 11:54:58.358 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-08-04 11:54:58.358 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-08-04 11:54:58.358 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-08-04 11:54:58.358 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-08-04 11:54:58.359 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:54:58.359 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-08-04 11:54:58.360 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-08-04 11:54:58.360 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:54:58.364 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:54:58.365 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:54:58.365 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:54:59.374 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-08-04 11:54:59.374 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-08-04 11:54:59.375 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:54:59.375 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:54:59.376 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-08-04 11:54:59.376 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-08-04 11:54:59.376 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:54:59.377 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:54:59.377 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-08-04 11:54:59.379 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-08-04 11:54:59.380 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-08-04 11:54:59.380 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-08-04 11:54:59.382 [Information] BridgedVocomService: Connecting to Vocom device SYSTEM_VOCOM_001 through bridge
2025-08-04 11:54:59.389 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: ConnectDevice
2025-08-04 11:54:59.389 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:54:59.390 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"ConnectDevice","Data":"{\u0022DeviceId\u0022:\u0022SYSTEM_VOCOM_001\u0022}"} ===
2025-08-04 11:54:59.390 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 85 ===
2025-08-04 11:54:59.390 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:54:59.393 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:54:59.395 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:54:59.395 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:55:00.848 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 77 bytes from pipe ===
2025-08-04 11:55:00.849 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":null,"Message":"Connected to device SYSTEM_VOCOM_001"} ===
2025-08-04 11:55:00.849 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:55:00.849 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:55:00.850 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Connected to device SYSTEM_VOCOM_001, Data length= ===
2025-08-04 11:55:00.851 [Information] VocomArchitectureBridge: Successfully connected to device SYSTEM_VOCOM_001 through bridge
2025-08-04 11:55:00.853 [Information] BridgedVocomService: === DEVICE CONNECTION DEBUG ===
2025-08-04 11:55:00.853 [Information] BridgedVocomService: Device ID: SYSTEM_VOCOM_001
2025-08-04 11:55:00.854 [Information] BridgedVocomService: Device Name: System Vocom Adapter (Real Hardware)
2025-08-04 11:55:00.856 [Information] BridgedVocomService: Device ConnectionStatus: Connected
2025-08-04 11:55:00.856 [Information] BridgedVocomService: _isConnected: True
2025-08-04 11:55:00.857 [Information] BridgedVocomService: CurrentDevice != null: True
2025-08-04 11:55:00.857 [Information] BridgedVocomService: CurrentDevice.ConnectionStatus: Connected
2025-08-04 11:55:00.857 [Information] BridgedVocomService: === END DEVICE CONNECTION DEBUG ===
2025-08-04 11:55:00.858 [Information] BridgedVocomService: Successfully connected to device SYSTEM_VOCOM_001
2025-08-04 11:55:00.859 [Information] App: Connected to Vocom device System Vocom Adapter (Real Hardware)
2025-08-04 11:55:00.864 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-04 11:55:00.871 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:00.873 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-04 11:55:00.880 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 11:55:00.882 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 11:55:00.882 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 11:55:00.884 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 11:55:00.884 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-04 11:55:00.885 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: SYSTEM_VOCOM_001
2025-08-04 11:55:00.885 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-04 11:55:00.885 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 11:55:00.889 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-04 11:55:00.891 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-04 11:55:00.915 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-04 11:55:00.918 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-04 11:55:00.946 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-04 11:55:00.962 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-04 11:55:00.967 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-04 11:55:00.978 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-04 11:55:00.979 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-04 11:55:00.980 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-04 11:55:00.981 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-04 11:55:00.981 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-04 11:55:00.982 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-04 11:55:00.983 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-04 11:55:00.983 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-04 11:55:00.983 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-04 11:55:00.984 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-04 11:55:00.984 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-04 11:55:00.986 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-04 11:55:00.987 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-04 11:55:00.987 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-04 11:55:00.989 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-04 11:55:00.989 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-04 11:55:00.989 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-04 11:55:00.996 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-04 11:55:01.002 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-04 11:55:01.004 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-04 11:55:01.010 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-04 11:55:01.013 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:01.021 [Information] CANRegisterAccess: Read value 0xC6 from register 0x0141 (simulated)
2025-08-04 11:55:01.028 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:01.035 [Information] CANRegisterAccess: Read value 0x57 from register 0x0141 (simulated)
2025-08-04 11:55:01.035 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-04 11:55:01.037 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-04 11:55:01.038 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-04 11:55:01.043 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-04 11:55:01.044 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-04 11:55:01.050 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-04 11:55:01.051 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-04 11:55:01.052 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-04 11:55:01.057 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-04 11:55:01.058 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-04 11:55:01.059 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-04 11:55:01.064 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-04 11:55:01.065 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-04 11:55:01.071 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-04 11:55:01.072 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-04 11:55:01.078 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-04 11:55:01.078 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-04 11:55:01.085 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-04 11:55:01.085 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-04 11:55:01.091 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-04 11:55:01.092 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-04 11:55:01.098 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-04 11:55:01.099 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-04 11:55:01.105 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-04 11:55:01.105 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-04 11:55:01.111 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-04 11:55:01.112 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-04 11:55:01.118 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-04 11:55:01.119 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-04 11:55:01.125 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-04 11:55:01.125 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-04 11:55:01.131 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-04 11:55:01.132 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-04 11:55:01.138 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-04 11:55:01.139 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-04 11:55:01.144 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-04 11:55:01.145 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-04 11:55:01.151 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-04 11:55:01.152 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-04 11:55:01.161 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-04 11:55:01.162 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-04 11:55:01.168 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-04 11:55:01.168 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-04 11:55:01.175 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-04 11:55:01.175 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-04 11:55:01.176 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-04 11:55:01.184 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-04 11:55:01.185 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-04 11:55:01.186 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-04 11:55:01.187 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:01.193 [Information] CANRegisterAccess: Read value 0x66 from register 0x0141 (simulated)
2025-08-04 11:55:01.194 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-04 11:55:01.194 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-04 11:55:01.195 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-04 11:55:01.195 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-04 11:55:01.200 [Information] CANRegisterAccess: Read value 0xB7 from register 0x0140 (simulated)
2025-08-04 11:55:01.201 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-04 11:55:01.201 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-04 11:55:01.206 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-04 11:55:01.207 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-04 11:55:01.218 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-04 11:55:01.219 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-04 11:55:01.220 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-04 11:55:01.226 [Debug] BridgedVocomService: Sending and receiving 4 bytes through bridge
2025-08-04 11:55:01.227 [Error] SPIProtocolHandler: No response received from Vocom device
2025-08-04 11:55:01.227 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-04 11:55:01.228 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-04 11:55:01.230 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-04 11:55:01.231 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-04 11:55:01.241 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-04 11:55:01.243 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-04 11:55:01.243 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-04 11:55:01.255 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-04 11:55:01.265 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-04 11:55:01.279 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-04 11:55:01.291 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-04 11:55:01.307 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-04 11:55:01.310 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-04 11:55:01.312 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-04 11:55:01.323 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-04 11:55:01.324 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-04 11:55:01.325 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-04 11:55:01.339 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-04 11:55:01.350 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-04 11:55:01.361 [Information] IICProtocolHandler: Enabling IIC module
2025-08-04 11:55:01.372 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-04 11:55:01.384 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-04 11:55:01.395 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-04 11:55:01.398 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-04 11:55:01.399 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-04 11:55:01.410 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-04 11:55:01.412 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-04 11:55:01.412 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-04 11:55:01.412 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-04 11:55:01.413 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-04 11:55:01.413 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-04 11:55:01.413 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-04 11:55:01.414 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-04 11:55:01.414 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-04 11:55:01.415 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-04 11:55:01.415 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-04 11:55:01.415 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-04 11:55:01.416 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-04 11:55:01.416 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-04 11:55:01.420 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-04 11:55:01.420 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-04 11:55:01.421 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-04 11:55:01.522 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-04 11:55:01.522 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-04 11:55:01.527 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-04 11:55:01.529 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:01.529 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-04 11:55:01.530 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-04 11:55:01.530 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:01.531 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-04 11:55:01.531 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-04 11:55:01.532 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:01.532 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-04 11:55:01.532 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-04 11:55:01.533 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:01.533 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-04 11:55:01.534 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-04 11:55:01.535 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-08-04 11:55:01.536 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-04 11:55:01.537 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-04 11:55:01.542 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-04 11:55:01.545 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-04 11:55:01.551 [Information] BackupService: Initializing backup service
2025-08-04 11:55:01.551 [Information] BackupService: Backup service initialized successfully
2025-08-04 11:55:01.551 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-04 11:55:01.552 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-04 11:55:01.555 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-04 11:55:01.597 [Information] BackupService: Compressing backup data
2025-08-04 11:55:01.614 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-08-04 11:55:01.615 [Information] BackupServiceFactory: Created template for category: Production
2025-08-04 11:55:01.616 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-04 11:55:01.617 [Information] BackupService: Compressing backup data
2025-08-04 11:55:01.620 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-08-04 11:55:01.620 [Information] BackupServiceFactory: Created template for category: Development
2025-08-04 11:55:01.621 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-04 11:55:01.621 [Information] BackupService: Compressing backup data
2025-08-04 11:55:01.623 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-08-04 11:55:01.623 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-04 11:55:01.624 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-04 11:55:01.625 [Information] BackupService: Compressing backup data
2025-08-04 11:55:01.626 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-08-04 11:55:01.626 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-04 11:55:01.627 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-04 11:55:01.627 [Information] BackupService: Compressing backup data
2025-08-04 11:55:01.628 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-08-04 11:55:01.629 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-04 11:55:01.629 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-04 11:55:01.629 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-04 11:55:01.630 [Information] BackupService: Compressing backup data
2025-08-04 11:55:01.632 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-08-04 11:55:01.632 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-04 11:55:01.632 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-04 11:55:01.635 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-04 11:55:01.639 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-04 11:55:01.642 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-04 11:55:01.757 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-04 11:55:01.758 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-04 11:55:01.760 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-04 11:55:01.761 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-04 11:55:01.761 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-04 11:55:01.763 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-04 11:55:01.764 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-04 11:55:01.770 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-04 11:55:01.771 [Information] App: Flash operation monitor service initialized successfully
2025-08-04 11:55:01.791 [Information] LicensingService: Initializing licensing service
2025-08-04 11:55:01.864 [Information] LicensingService: License information loaded successfully
2025-08-04 11:55:01.868 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-04 11:55:01.868 [Information] App: Licensing service initialized successfully
2025-08-04 11:55:01.869 [Information] App: License status: Trial
2025-08-04 11:55:01.869 [Information] App: Trial period: 22 days remaining
2025-08-04 11:55:01.872 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-04 11:55:01.911 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-04 11:55:02.093 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-08-04 11:55:02.094 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge (attempt 1/3)
2025-08-04 11:55:02.094 [Information] VocomArchitectureBridge: Bridge already initialized
2025-08-04 11:55:02.094 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-08-04 11:55:02.145 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 11:55:02.145 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 11:55:02.145 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 11:55:02.146 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 11:55:02.146 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-04 11:55:02.146 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: SYSTEM_VOCOM_001
2025-08-04 11:55:02.147 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-04 11:55:02.147 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 11:55:02.147 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-04 11:55:02.148 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-04 11:55:02.149 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-04 11:55:02.150 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-04 11:55:02.151 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-04 11:55:02.151 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-04 11:55:02.152 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-04 11:55:02.162 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-04 11:55:02.163 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-04 11:55:02.163 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-04 11:55:02.164 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-04 11:55:02.164 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-04 11:55:02.164 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-04 11:55:02.165 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-04 11:55:02.165 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-04 11:55:02.165 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-04 11:55:02.165 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-04 11:55:02.166 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-04 11:55:02.166 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-04 11:55:02.166 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-04 11:55:02.167 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-04 11:55:02.167 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-04 11:55:02.167 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-04 11:55:02.168 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-04 11:55:02.168 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-04 11:55:02.175 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-04 11:55:02.175 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-04 11:55:02.175 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-04 11:55:02.176 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:02.181 [Information] CANRegisterAccess: Read value 0xD9 from register 0x0141 (simulated)
2025-08-04 11:55:02.182 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-04 11:55:02.182 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-04 11:55:02.183 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-04 11:55:02.188 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-04 11:55:02.189 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-04 11:55:02.195 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-04 11:55:02.196 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-04 11:55:02.197 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-04 11:55:02.217 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-04 11:55:02.218 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-04 11:55:02.218 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-04 11:55:02.224 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-04 11:55:02.225 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-04 11:55:02.230 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-04 11:55:02.231 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-04 11:55:02.238 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-04 11:55:02.239 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-04 11:55:02.244 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-04 11:55:02.245 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-04 11:55:02.251 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-04 11:55:02.252 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-04 11:55:02.258 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-04 11:55:02.259 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-04 11:55:02.265 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-04 11:55:02.266 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-04 11:55:02.271 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-04 11:55:02.272 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-04 11:55:02.278 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-04 11:55:02.279 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-04 11:55:02.285 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-04 11:55:02.286 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-04 11:55:02.292 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-04 11:55:02.293 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-04 11:55:02.298 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-04 11:55:02.299 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-04 11:55:02.306 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-04 11:55:02.306 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-04 11:55:02.312 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-04 11:55:02.313 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-04 11:55:02.319 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-04 11:55:02.319 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-04 11:55:02.325 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-04 11:55:02.326 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-04 11:55:02.332 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-04 11:55:02.333 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-04 11:55:02.333 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-04 11:55:02.339 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-04 11:55:02.340 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-04 11:55:02.340 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-04 11:55:02.341 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:02.346 [Information] CANRegisterAccess: Read value 0x8B from register 0x0141 (simulated)
2025-08-04 11:55:02.352 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:02.358 [Information] CANRegisterAccess: Read value 0xD3 from register 0x0141 (simulated)
2025-08-04 11:55:02.364 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:02.371 [Information] CANRegisterAccess: Read value 0x27 from register 0x0141 (simulated)
2025-08-04 11:55:02.377 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-04 11:55:02.383 [Information] CANRegisterAccess: Read value 0x88 from register 0x0141 (simulated)
2025-08-04 11:55:02.384 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-04 11:55:02.384 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-04 11:55:02.385 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-04 11:55:02.385 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-04 11:55:02.391 [Information] CANRegisterAccess: Read value 0x8B from register 0x0140 (simulated)
2025-08-04 11:55:02.397 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-04 11:55:02.404 [Information] CANRegisterAccess: Read value 0x6C from register 0x0140 (simulated)
2025-08-04 11:55:02.410 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-04 11:55:02.416 [Information] CANRegisterAccess: Read value 0x69 from register 0x0140 (simulated)
2025-08-04 11:55:02.422 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-04 11:55:02.428 [Information] CANRegisterAccess: Read value 0x30 from register 0x0140 (simulated)
2025-08-04 11:55:02.429 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-04 11:55:02.429 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-04 11:55:02.432 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-04 11:55:02.433 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-04 11:55:02.445 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-04 11:55:02.446 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-04 11:55:02.446 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-04 11:55:02.446 [Debug] BridgedVocomService: Sending and receiving 4 bytes through bridge
2025-08-04 11:55:02.447 [Error] SPIProtocolHandler: No response received from Vocom device
2025-08-04 11:55:02.447 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-04 11:55:02.447 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-04 11:55:02.448 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-04 11:55:02.448 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-04 11:55:02.462 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-04 11:55:02.463 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-04 11:55:02.463 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-04 11:55:02.475 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-04 11:55:02.485 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-04 11:55:02.495 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-04 11:55:02.506 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-04 11:55:02.517 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-04 11:55:02.518 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-04 11:55:02.518 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-04 11:55:02.529 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-04 11:55:02.530 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-04 11:55:02.530 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-04 11:55:02.541 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-04 11:55:02.552 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-04 11:55:02.563 [Information] IICProtocolHandler: Enabling IIC module
2025-08-04 11:55:02.574 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-04 11:55:02.585 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-04 11:55:02.596 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-04 11:55:02.597 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-04 11:55:02.597 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-04 11:55:02.608 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-04 11:55:02.609 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-04 11:55:02.609 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-04 11:55:02.609 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-04 11:55:02.609 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-04 11:55:02.610 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-04 11:55:02.610 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-04 11:55:02.610 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-04 11:55:02.611 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-04 11:55:02.611 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-04 11:55:02.611 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-04 11:55:02.611 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-04 11:55:02.612 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-04 11:55:02.612 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-04 11:55:02.612 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-04 11:55:02.613 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-04 11:55:02.613 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-04 11:55:02.714 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-04 11:55:02.714 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-04 11:55:02.715 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-04 11:55:02.715 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:02.716 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-04 11:55:02.716 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-04 11:55:02.716 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:02.717 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-04 11:55:02.717 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-04 11:55:02.717 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:02.718 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-04 11:55:02.718 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-04 11:55:02.718 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 11:55:02.719 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-04 11:55:02.719 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-04 11:55:02.770 [Information] BackupService: Initializing backup service
2025-08-04 11:55:02.770 [Information] BackupService: Backup service initialized successfully
2025-08-04 11:55:02.822 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-04 11:55:02.822 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-04 11:55:02.824 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-04 11:55:02.824 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-04 11:55:02.877 [Information] BackupService: Getting predefined backup categories
2025-08-04 11:55:02.929 [Information] MainViewModel: Services initialized successfully
2025-08-04 11:55:02.931 [Information] MainViewModel: Scanning for Vocom devices
2025-08-04 11:55:02.933 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-08-04 11:55:02.933 [Information] BridgedVocomService: === About to call _bridge.DetectDevicesAsync() ===
2025-08-04 11:55:02.933 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-08-04 11:55:02.934 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-08-04 11:55:02.934 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-08-04 11:55:02.934 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-08-04 11:55:02.935 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-08-04 11:55:02.935 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:55:02.935 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-08-04 11:55:02.936 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-08-04 11:55:02.936 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:55:02.952 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:55:02.953 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:55:02.953 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:55:03.739 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-08-04 11:55:03.739 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-08-04 11:55:03.740 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:55:03.740 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:55:03.741 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-08-04 11:55:03.741 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-08-04 11:55:03.741 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:55:03.742 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:55:03.742 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-08-04 11:55:03.745 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-08-04 11:55:03.745 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-08-04 11:55:03.746 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-08-04 11:55:03.746 [Information] BridgedVocomService: === Returned from _bridge.DetectDevicesAsync() ===
2025-08-04 11:55:03.747 [Information] BridgedVocomService: Found 1 Vocom devices
2025-08-04 11:55:03.748 [Information] MainViewModel: Found 1 Vocom device(s)
2025-08-04 11:58:18.877 [Information] MainViewModel: Connecting to Vocom device SYSTEM_VOCOM_001
2025-08-04 11:58:18.878 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-08-04 11:58:18.878 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-08-04 11:58:18.879 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-08-04 11:58:18.879 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-08-04 11:58:18.879 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-08-04 11:58:18.880 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:58:18.880 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-08-04 11:58:18.880 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-08-04 11:58:18.881 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:58:18.884 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:58:18.884 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:58:18.885 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:58:19.512 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-08-04 11:58:19.512 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-08-04 11:58:19.513 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:58:19.513 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:58:19.513 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-08-04 11:58:19.514 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-08-04 11:58:19.514 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:58:19.515 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-08-04 11:58:19.515 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-08-04 11:58:19.517 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-08-04 11:58:19.518 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-08-04 11:58:19.518 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-08-04 11:58:19.519 [Warning] BridgedVocomService: Local state shows connected but bridge reports no connected device - synchronizing
2025-08-04 11:58:19.519 [Information] BridgedVocomService: Synchronized local state: disconnected from device SYSTEM_VOCOM_001
2025-08-04 11:58:19.520 [Information] ECUCommunicationService: Vocom device disconnected: SYSTEM_VOCOM_001
2025-08-04 11:58:19.524 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-04 11:58:19.525 [Information] ECUCommunicationService: No ECUs are connected
2025-08-04 11:58:19.526 [Information] MainViewModel: Vocom device SYSTEM_VOCOM_001 disconnected
2025-08-04 11:58:19.526 [Information] ECUCommunicationService: Vocom device disconnected: SYSTEM_VOCOM_001
2025-08-04 11:58:19.527 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-04 11:58:19.527 [Information] ECUCommunicationService: No ECUs are connected
2025-08-04 11:58:19.527 [Information] BridgedVocomService: Connecting to Vocom device SYSTEM_VOCOM_001 through bridge
2025-08-04 11:58:19.528 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: ConnectDevice
2025-08-04 11:58:19.528 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-08-04 11:58:19.528 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"ConnectDevice","Data":"{\u0022DeviceId\u0022:\u0022SYSTEM_VOCOM_001\u0022}"} ===
2025-08-04 11:58:19.529 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 85 ===
2025-08-04 11:58:19.529 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-08-04 11:58:19.530 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-08-04 11:58:19.530 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-08-04 11:58:19.531 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-08-04 11:58:20.261 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 77 bytes from pipe ===
2025-08-04 11:58:20.261 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":null,"Message":"Connected to device SYSTEM_VOCOM_001"} ===
2025-08-04 11:58:20.262 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-08-04 11:58:20.262 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-08-04 11:58:20.262 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Connected to device SYSTEM_VOCOM_001, Data length= ===
2025-08-04 11:58:20.263 [Information] VocomArchitectureBridge: Successfully connected to device SYSTEM_VOCOM_001 through bridge
2025-08-04 11:58:20.263 [Information] BridgedVocomService: === DEVICE CONNECTION DEBUG ===
2025-08-04 11:58:20.263 [Information] BridgedVocomService: Device ID: SYSTEM_VOCOM_001
2025-08-04 11:58:20.264 [Information] BridgedVocomService: Device Name: System Vocom Adapter (Real Hardware)
2025-08-04 11:58:20.264 [Information] BridgedVocomService: Device ConnectionStatus: Connected
2025-08-04 11:58:20.264 [Information] BridgedVocomService: _isConnected: True
2025-08-04 11:58:20.265 [Information] BridgedVocomService: CurrentDevice != null: True
2025-08-04 11:58:20.273 [Information] BridgedVocomService: CurrentDevice.ConnectionStatus: Connected
2025-08-04 11:58:20.273 [Information] BridgedVocomService: === END DEVICE CONNECTION DEBUG ===
2025-08-04 11:58:20.274 [Information] ECUCommunicationService: Vocom device connected: SYSTEM_VOCOM_001
2025-08-04 11:58:20.274 [Information] MainViewModel: Vocom device SYSTEM_VOCOM_001 connected
2025-08-04 11:58:20.275 [Information] ECUCommunicationService: Vocom device connected: SYSTEM_VOCOM_001
2025-08-04 11:58:20.275 [Information] BridgedVocomService: Successfully connected to device SYSTEM_VOCOM_001
2025-08-04 11:58:20.276 [Information] MainViewModel: Connected to Vocom device SYSTEM_VOCOM_001
2025-08-04 11:58:20.279 [Information] MainViewModel: Scanning for ECUs
2025-08-04 11:58:20.287 [Information] ECUCommunicationService: Scanning for ECUs
2025-08-04 11:58:20.288 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-08-04 11:58:20.789 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-08-04 11:58:21.090 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-08-04 11:58:21.391 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-08-04 11:58:21.691 [Information] ECUCommunicationService: Found 10 ECUs
2025-08-04 11:58:21.693 [Information] MainViewModel: Found 10 ECU(s)
