Log started at 7/30/2025 5:44:10 PM
2025-07-30 17:44:10.550 [Information] LoggingService: Logging service initialized
2025-07-30 17:44:10.563 [Information] App: Starting integrated application initialization
2025-07-30 17:44:10.565 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-30 17:44:10.567 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-30 17:44:10.569 [Information] IntegratedStartupService: Setting up application environment
2025-07-30 17:44:10.570 [Information] IntegratedStartupService: Application environment setup completed
2025-07-30 17:44:10.571 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-30 17:44:10.574 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-30 17:44:10.578 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-30 17:44:10.583 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-30 17:44:10.589 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.591 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.594 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.594 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-30 17:44:10.598 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.601 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.603 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.604 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:44:10.607 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.610 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.613 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.614 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:44:10.616 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.618 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.621 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.621 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:44:10.625 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.628 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.630 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.631 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:44:10.634 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.637 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.639 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.640 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:44:10.644 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:44:10.647 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.650 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:44:10.650 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:44:10.653 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-30 17:44:10.655 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-30 17:44:10.656 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-30 17:44:10.657 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-30 17:44:10.658 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-30 17:44:10.659 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-30 17:44:10.659 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-30 17:44:10.660 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-30 17:44:10.663 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-30 17:44:10.663 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-30 17:44:10.665 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-30 17:44:10.665 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:44:10.666 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:44:10.666 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:44:10.666 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:44:10.667 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:44:10.667 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:44:10.675 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-30 17:44:10.676 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-30 17:44:10.676 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-30 17:44:10.684 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-30 17:44:10.684 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-30 17:44:10.686 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-30 17:44:10.688 [Information] LibraryExtractor: Starting library extraction process
2025-07-30 17:44:10.692 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-30 17:44:10.696 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-30 17:44:10.699 [Information] LibraryExtractor: Copying system libraries
2025-07-30 17:44:10.707 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-30 17:44:10.716 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-30 17:44:40.728 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:44:41.730 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-30 17:45:11.733 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:45:11.734 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:45:41.737 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:45:42.738 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-30 17:46:12.741 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:46:12.742 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:46:42.750 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:46:43.779 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-30 17:47:13.783 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:47:13.784 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-30 17:47:13.784 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-30 17:47:13.790 [Information] LibraryExtractor: Verifying library extraction
2025-07-30 17:47:13.791 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-30 17:47:13.791 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-30 17:47:13.792 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-30 17:47:13.792 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-30 17:47:13.793 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-30 17:47:13.798 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-30 17:47:13.801 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-30 17:47:13.804 [Information] DependencyManager: Initializing dependency manager
2025-07-30 17:47:13.806 [Information] DependencyManager: Setting up library search paths
2025-07-30 17:47:13.808 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:47:13.809 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:47:13.809 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-30 17:47:13.810 [Information] DependencyManager: Updated PATH environment variable
2025-07-30 17:47:13.812 [Information] DependencyManager: Verifying required directories
2025-07-30 17:47:13.812 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:47:13.813 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:47:13.813 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-30 17:47:13.814 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-30 17:47:13.815 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-30 17:47:13.823 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-30 17:47:13.824 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-30 17:47:13.824 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-07-30 17:47:13.826 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-30 17:47:13.826 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-30 17:47:13.827 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-30 17:47:13.827 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-07-30 17:47:13.829 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-30 17:47:13.832 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-30 17:47:13.832 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-30 17:47:13.833 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-30 17:47:13.836 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-07-30 17:47:13.836 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-30 17:47:13.837 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-07-30 17:47:13.838 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:47:13.839 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:47:13.840 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:47:13.840 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:47:13.841 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:47:13.842 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:47:13.843 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:47:13.843 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:47:13.844 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:47:13.844 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:47:13.845 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:47:13.845 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:47:13.846 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-30 17:47:13.847 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-30 17:47:13.848 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-30 17:47:13.848 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-30 17:47:13.849 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-30 17:47:13.850 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-30 17:47:13.850 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-07-30 17:47:13.850 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-30 17:47:13.852 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-30 17:47:13.853 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-30 17:47:13.854 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:47:13.854 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-30 17:47:13.855 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:47:13.855 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:47:13.856 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-30 17:47:13.857 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-30 17:47:13.857 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-30 17:47:13.858 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-30 17:47:13.858 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-30 17:47:13.859 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-30 17:47:13.860 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-30 17:47:13.860 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-30 17:47:13.861 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-30 17:47:13.862 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-30 17:47:13.863 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-30 17:47:13.863 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-30 17:47:13.864 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-30 17:47:13.864 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-30 17:47:13.865 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-30 17:47:13.866 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-30 17:47:13.866 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-30 17:47:13.867 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-30 17:47:13.868 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-30 17:47:13.868 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-30 17:47:13.869 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-30 17:47:13.869 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-07-30 17:47:13.870 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-30 17:47:13.870 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-30 17:47:13.871 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-30 17:47:13.871 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-07-30 17:47:13.872 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-30 17:47:13.874 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-30 17:47:13.875 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-30 17:47:13.875 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-07-30 17:47:13.876 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-30 17:47:13.876 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-07-30 17:47:13.877 [Information] DependencyManager: Setting up environment variables
2025-07-30 17:47:13.877 [Information] DependencyManager: Environment variables configured
2025-07-30 17:47:13.879 [Information] DependencyManager: Verifying library loading status
2025-07-30 17:47:14.324 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-30 17:47:14.325 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-30 17:47:14.325 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-30 17:47:14.329 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-30 17:47:14.330 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-30 17:47:14.334 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-30 17:47:14.336 [Information] IntegratedStartupService: Verifying system readiness
2025-07-30 17:47:14.337 [Information] IntegratedStartupService: System readiness verification passed
2025-07-30 17:47:14.338 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-30 17:47:14.339 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-30 17:47:14.340 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-30 17:47:14.341 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:47:14.341 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:47:14.342 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-30 17:47:14.342 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-30 17:47:14.343 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-30 17:47:14.343 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:47:14.343 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-30 17:47:14.344 [Information] App: Integrated startup completed successfully
2025-07-30 17:47:14.347 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-30 17:47:14.368 [Information] App: Initializing application services
2025-07-30 17:47:14.371 [Information] AppConfigurationService: Initializing configuration service
2025-07-30 17:47:14.374 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-30 17:47:14.429 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-30 17:47:14.430 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-30 17:47:14.431 [Information] App: Configuration service initialized successfully
2025-07-30 17:47:14.432 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-30 17:47:14.433 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-30 17:47:14.440 [Information] App: Environment variable exists: True, not 'false': False
2025-07-30 17:47:14.441 [Information] App: Final useDummyImplementations value: False
2025-07-30 17:47:14.441 [Information] App: Updating config to NOT use dummy implementations
2025-07-30 17:47:14.444 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-30 17:47:14.458 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-30 17:47:14.460 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-30 17:47:14.460 [Information] App: usePatchedImplementation flag is: True
2025-07-30 17:47:14.461 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-30 17:47:14.461 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-30 17:47:14.461 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-30 17:47:14.462 [Information] App: verboseLogging flag is: True
2025-07-30 17:47:14.464 [Information] App: Verifying real hardware requirements...
2025-07-30 17:47:14.464 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-30 17:47:14.465 [Information] App: ✓ Found critical library: apci.dll
2025-07-30 17:47:14.465 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-30 17:47:14.465 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-30 17:47:14.466 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-30 17:47:14.466 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-30 17:47:14.467 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-30 17:47:14.467 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-30 17:47:14.478 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-30 17:47:14.481 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-30 17:47:14.481 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-30 17:47:14.484 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-30 17:47:14.488 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-30 17:49:02.494 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-30 17:49:02.495 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-30 17:49:02.495 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-30 17:49:02.496 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:49:02.496 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:49:02.497 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:49:02.497 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:49:02.497 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:49:02.498 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:49:02.498 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-30 17:49:02.500 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-30 17:49:02.500 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-30 17:49:02.500 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-30 17:49:02.501 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-30 17:49:02.501 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-30 17:49:02.501 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-30 17:49:02.502 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-30 17:49:02.502 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-30 17:49:02.503 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-30 17:49:02.504 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-30 17:49:02.506 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-30 17:49:02.506 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-30 17:49:02.507 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-30 17:49:02.507 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-30 17:49:02.509 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-30 17:49:02.509 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-30 17:49:02.512 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-30 17:49:02.514 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-30 17:49:02.515 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-30 17:49:02.516 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-30 17:49:02.544 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-30 17:49:02.545 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-30 17:49:02.549 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-30 17:49:02.550 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-30 17:49:02.551 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-30 17:49:02.551 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-30 17:49:02.553 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-30 17:49:02.554 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-30 17:49:02.554 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-30 17:49:02.555 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:49:02.555 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-30 17:49:02.555 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-30 17:49:02.556 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-30 17:49:02.558 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-30 17:49:02.563 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-30 17:49:02.567 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-30 17:49:02.568 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-30 17:49:02.568 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-30 17:49:02.585 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-30 17:49:02.602 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-30 17:49:02.604 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-30 17:49:02.605 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-30 17:49:02.606 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:49:02.606 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-30 17:49:02.607 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:49:02.607 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-30 17:49:02.608 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:49:02.608 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-30 17:49:02.609 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-30 17:49:02.610 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-30 17:49:02.611 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-30 17:49:02.613 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-30 17:49:02.613 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-30 17:49:02.614 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-30 17:49:02.616 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-30 17:49:02.617 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-30 17:49:02.618 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-30 17:49:02.618 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-30 17:49:02.618 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-30 17:49:02.619 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-30 17:49:02.620 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-30 17:49:02.621 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-30 17:49:02.621 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-30 17:49:02.621 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-30 17:49:02.622 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-30 17:49:02.622 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-30 17:49:02.623 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-30 17:49:02.623 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-30 17:49:02.623 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-30 17:49:02.625 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-30 17:49:02.626 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-30 17:49:02.626 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-30 17:49:02.627 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:49:02.627 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-30 17:49:02.630 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-30 17:49:02.630 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-30 17:49:02.630 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-30 17:49:02.631 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-30 17:49:02.632 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-30 17:49:02.632 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-30 17:49:02.632 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-30 17:49:02.634 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-30 17:49:02.636 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-30 17:49:02.636 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-30 17:49:02.642 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-30 17:49:02.642 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-30 17:49:02.643 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:49:02.643 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:49:02.646 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-30 17:49:02.647 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.648 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.649 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-30 17:49:02.649 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.650 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.650 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-30 17:49:02.651 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.651 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-30 17:49:02.652 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.652 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-30 17:49:02.653 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.653 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.654 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-30 17:49:02.654 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.655 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.655 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-30 17:49:02.656 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:49:02.656 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:49:02.657 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.658 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.658 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-30 17:49:02.658 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:49:02.659 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.659 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-30 17:49:02.661 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.661 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.662 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-30 17:49:02.662 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:49:02.663 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:49:02.663 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-30 17:49:02.664 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:49:02.664 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:49:02.665 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:49:02.669 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-30 17:49:02.669 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:49:02.670 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-30 17:49:02.671 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-30 17:49:02.671 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-30 17:49:02.673 [Information] VocomDriver: Initializing Vocom driver
2025-07-30 17:49:02.674 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-30 17:49:02.678 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-30 17:49:02.678 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 17:49:02.678 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 17:49:02.680 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 17:49:02.681 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:49:02.684 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-30 17:49:02.685 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-30 17:49:02.687 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-30 17:49:02.687 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-07-30 17:49:02.688 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-07-30 17:49:02.688 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:49:02.691 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-30 17:49:02.692 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-30 17:49:02.693 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-30 17:49:02.694 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-30 17:49:02.695 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-30 17:49:02.695 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-30 17:49:02.697 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-30 17:49:02.698 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-30 17:49:02.698 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-30 17:49:02.698 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-30 17:49:02.699 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-30 17:49:02.699 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-30 17:49:02.699 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-30 17:49:02.700 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-30 17:49:02.700 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-30 17:49:02.700 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-30 17:49:02.700 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-30 17:49:02.701 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-30 17:49:02.701 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-30 17:49:02.701 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-30 17:49:02.702 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-30 17:49:02.702 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-30 17:49:02.702 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-30 17:49:02.703 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-30 17:49:02.703 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-30 17:49:02.703 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-30 17:49:02.704 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-30 17:49:02.704 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-30 17:49:02.704 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-30 17:49:02.704 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-30 17:49:02.705 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-30 17:49:02.705 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-30 17:49:02.705 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-30 17:49:02.706 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-30 17:49:02.706 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-30 17:49:02.706 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-30 17:49:02.706 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-30 17:49:02.707 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-30 17:49:02.707 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-30 17:49:02.707 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-30 17:49:02.708 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-30 17:49:02.708 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-30 17:49:02.708 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-30 17:49:02.709 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-30 17:49:02.709 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-30 17:49:02.709 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-30 17:49:02.710 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-30 17:49:02.710 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-30 17:49:02.711 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-30 17:49:02.712 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-30 17:49:02.713 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-30 17:49:02.714 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-30 17:49:02.714 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-30 17:49:02.715 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-30 17:49:02.715 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-30 17:49:02.715 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-30 17:49:02.716 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-30 17:49:02.720 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-30 17:49:02.721 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-30 17:49:02.722 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-30 17:49:02.723 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-30 17:49:02.797 [Information] WiFiCommunicationService: WiFi is available
2025-07-30 17:49:02.798 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-30 17:49:02.800 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-30 17:49:02.801 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-30 17:49:02.803 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-30 17:49:02.804 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-30 17:49:02.806 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 17:49:02.807 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 17:49:02.809 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 17:49:02.811 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 17:49:02.816 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 17:49:02.816 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 17:49:02.817 [Information] VocomService: Native USB communication service initialized
2025-07-30 17:49:02.819 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 17:49:02.819 [Information] VocomService: Connection recovery service initialized
2025-07-30 17:49:02.820 [Information] VocomService: Enhanced services initialization completed
2025-07-30 17:49:02.821 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:02.838 [Information] VocomService: PTT application is not running
2025-07-30 17:49:02.840 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:02.842 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:02.843 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 17:49:02.844 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-30 17:49:02.845 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-30 17:49:02.849 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:49:02.849 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:49:02.851 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:49:02.853 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:49:03.155 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:49:03.156 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:49:03.158 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:49:03.160 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:49:03.160 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:49:03.163 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:49:03.165 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:49:03.168 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:49:03.427 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:49:03.430 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:49:03.433 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:49:03.434 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:49:03.435 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:49:03.444 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:49:03.446 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:49:03.446 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:49:03.447 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:49:03.447 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:03.447 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:03.450 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:49:03.452 [Debug] VocomService: WiFi is available
2025-07-30 17:49:03.453 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:49:03.453 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-30 17:49:03.455 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: b1538b22-0d97-43d2-bef7-109c4f8cbebc, Type: USB)
2025-07-30 17:49:03.455 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 1d6ba17e-1c9e-4d4c-b65d-5baa3b1ff2d3, Type: Bluetooth)
2025-07-30 17:49:03.455 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 4e9cd3c7-b223-434a-b31d-ffe98100ebf5, Type: WiFi)
2025-07-30 17:49:03.456 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-30 17:49:03.457 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-30 17:49:03.457 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-30 17:49:03.457 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-30 17:49:03.458 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-30 17:49:03.458 [Information] App: Architecture-aware Vocom service created successfully
2025-07-30 17:49:03.458 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 17:49:03.458 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 17:49:03.459 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 17:49:03.459 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 17:49:03.460 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 17:49:03.460 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 17:49:03.461 [Information] VocomService: Native USB communication service initialized
2025-07-30 17:49:03.461 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 17:49:03.461 [Information] VocomService: Connection recovery service initialized
2025-07-30 17:49:03.462 [Information] VocomService: Enhanced services initialization completed
2025-07-30 17:49:03.462 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:03.478 [Information] VocomService: PTT application is not running
2025-07-30 17:49:03.478 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:03.479 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:03.480 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 17:49:03.480 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-30 17:49:03.481 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-30 17:49:03.481 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-30 17:49:03.523 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:49:03.523 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:49:03.523 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:49:03.524 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:49:03.775 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:49:03.776 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:49:03.776 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:49:03.776 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:49:03.776 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:49:03.777 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:49:03.777 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:49:03.778 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:49:04.025 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:49:04.025 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:49:04.026 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:49:04.026 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:49:04.028 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:49:04.033 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:49:04.034 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:49:04.034 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:49:04.034 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:49:04.035 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:04.036 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:04.036 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:49:04.036 [Debug] VocomService: WiFi is available
2025-07-30 17:49:04.037 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:49:04.037 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-30 17:49:04.039 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:04.040 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:04.055 [Information] VocomService: PTT application is not running
2025-07-30 17:49:04.059 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 17:49:04.059 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 17:49:04.060 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:04.074 [Information] VocomService: PTT application is not running
2025-07-30 17:49:04.075 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 17:49:04.078 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 17:49:04.080 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:49:04.083 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:49:04.085 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:04.086 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:04.086 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:04.087 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:04.087 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:04.088 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:04.088 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:04.088 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:04.089 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:04.089 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:49:04.089 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:04.090 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:04.090 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:04.090 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:04.091 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:04.091 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:49:04.092 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:04.093 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:49:04.093 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:04.093 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:49:04.094 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:49:04.094 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:49:04.095 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 17:49:05.097 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 17:49:05.097 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:49:05.098 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:49:05.098 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:05.099 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:05.099 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:05.099 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:05.100 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:05.100 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:05.100 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:05.101 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:05.101 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:05.101 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:49:05.102 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:05.102 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:05.102 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:05.102 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:05.103 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:05.103 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:49:05.103 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:05.104 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:49:05.104 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:05.104 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:49:05.105 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:49:05.105 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:49:05.105 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 17:49:06.106 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 17:49:06.107 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:49:06.108 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.109 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.110 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.112 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.114 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.115 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.115 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.115 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.116 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.116 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.116 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:49:06.117 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.117 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.117 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.118 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.118 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.118 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.119 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:06.119 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:49:06.119 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:06.120 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:49:06.121 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.121 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:49:06.121 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 17:49:06.122 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 17:49:06.123 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 17:49:06.159 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 17:49:06.161 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.164 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 17:49:06.165 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 17:49:06.165 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 17:49:06.165 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 17:49:06.166 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 17:49:06.169 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 17:49:06.170 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 17:49:06.175 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:49:06.177 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:49:06.179 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:49:06.180 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 17:49:06.181 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 17:49:06.181 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 17:49:06.182 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:49:06.184 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:06.184 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-30 17:49:06.187 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-30 17:49:06.191 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:06.192 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-30 17:49:06.197 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-30 17:49:06.201 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-30 17:49:06.201 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-30 17:49:06.202 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:49:06.202 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-30 17:49:06.203 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:49:06.203 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-30 17:49:06.203 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-30 17:49:06.206 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-30 17:49:06.206 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:49:06.206 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:49:06.207 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:49:06.207 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:49:06.439 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:49:06.439 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:49:06.439 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:49:06.440 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:49:06.440 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:49:06.440 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:49:06.440 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:49:06.441 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:49:06.678 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:49:06.679 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:49:06.680 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:49:06.680 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:49:06.681 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:49:06.687 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:49:06.688 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:49:06.688 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:49:06.688 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:49:06.689 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:06.690 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:06.691 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:49:06.692 [Debug] VocomService: WiFi is available
2025-07-30 17:49:06.693 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:49:06.693 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:06.694 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:06.694 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:06.714 [Information] VocomService: PTT application is not running
2025-07-30 17:49:06.714 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 17:49:06.714 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 17:49:06.715 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:06.763 [Information] VocomService: PTT application is not running
2025-07-30 17:49:06.798 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 17:49:06.799 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 17:49:06.801 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:49:06.802 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.802 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.803 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.803 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.803 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.804 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.804 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.804 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.805 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.805 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.805 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:49:06.806 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.806 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.807 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:06.807 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:06.807 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:06.808 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.809 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:06.809 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:49:06.809 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:06.810 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:49:06.810 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:49:06.826 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:49:06.829 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 17:49:07.830 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 17:49:07.830 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:49:07.831 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:49:07.831 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:07.832 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:07.832 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:07.832 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:07.832 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:07.833 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:07.833 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:07.833 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:07.834 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:07.834 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:49:07.834 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:07.834 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:07.835 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:07.835 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:07.835 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:07.836 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:49:07.836 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:07.836 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:49:07.837 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:07.837 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:49:07.837 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:49:07.838 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:49:07.838 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 17:49:08.838 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 17:49:08.838 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:49:08.839 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:49:08.839 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:08.840 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:08.840 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:08.840 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:08.841 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:08.841 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:08.841 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:08.842 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:08.842 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:08.842 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:49:08.843 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:08.843 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:08.843 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:49:08.843 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:49:08.844 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:49:08.844 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:49:08.845 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:08.845 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:49:08.846 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:49:08.846 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:49:08.847 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:49:08.847 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:49:08.847 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 17:49:08.848 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 17:49:08.848 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 17:49:08.848 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 17:49:08.849 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 17:49:08.849 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 17:49:08.849 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 17:49:08.850 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 17:49:08.850 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 17:49:08.850 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 17:49:08.850 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 17:49:08.851 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 17:49:08.851 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:49:08.851 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:49:08.852 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:49:08.852 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 17:49:08.852 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 17:49:08.852 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 17:49:08.853 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:49:08.853 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:49:08.854 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:08.854 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:08.854 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:49:08.855 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-30 17:49:08.855 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:49:08.855 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:08.871 [Information] VocomService: PTT application is not running
2025-07-30 17:49:08.874 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:49:08.874 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:08.874 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:08.876 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-30 17:49:09.681 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:49:09.682 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:49:09.682 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-30 17:49:09.684 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-30 17:49:09.687 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-30 17:49:09.689 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-30 17:49:09.692 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-30 17:49:09.694 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-30 17:49:09.697 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-30 17:49:09.704 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:49:09.706 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:49:09.718 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:49:09.719 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-30 17:49:09.720 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-30 17:49:09.720 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:49:09.720 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:49:09.721 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-30 17:49:09.721 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:49:09.721 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-30 17:49:09.722 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-30 17:49:09.722 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-30 17:49:09.722 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-30 17:49:09.722 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-30 17:49:09.723 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-30 17:49:09.723 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-30 17:49:09.723 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-30 17:49:09.724 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-30 17:49:09.724 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-30 17:49:09.727 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-30 17:49:09.734 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-30 17:49:09.735 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-30 17:49:09.738 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-30 17:49:09.741 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:49:09.748 [Information] CANRegisterAccess: Read value 0x91 from register 0x0141 (simulated)
2025-07-30 17:49:09.750 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-30 17:49:09.751 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-30 17:49:09.751 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-30 17:49:09.757 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-30 17:49:09.757 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-30 17:49:09.763 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-30 17:49:09.764 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-30 17:49:09.765 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-30 17:49:09.771 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-30 17:49:09.772 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-30 17:49:09.772 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-30 17:49:09.778 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-30 17:49:09.778 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-30 17:49:09.784 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-30 17:49:09.784 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-30 17:49:09.790 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-30 17:49:09.790 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-30 17:49:09.796 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-30 17:49:09.797 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-30 17:49:09.803 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-30 17:49:09.803 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-30 17:49:09.809 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-30 17:49:09.809 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-30 17:49:09.815 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-30 17:49:09.815 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-30 17:49:09.821 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-30 17:49:09.821 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-30 17:49:09.827 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-30 17:49:09.827 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-30 17:49:09.833 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-30 17:49:09.833 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-30 17:49:09.839 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-30 17:49:09.839 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-30 17:49:09.845 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-30 17:49:09.845 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-30 17:49:09.851 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-30 17:49:09.851 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-30 17:49:09.857 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-30 17:49:09.857 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-30 17:49:09.864 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-30 17:49:09.864 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-30 17:49:09.870 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-30 17:49:09.870 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-30 17:49:09.876 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-30 17:49:09.876 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-30 17:49:09.876 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-30 17:49:09.882 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-30 17:49:09.882 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-30 17:49:09.883 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-30 17:49:09.883 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:49:09.889 [Information] CANRegisterAccess: Read value 0x00 from register 0x0141 (simulated)
2025-07-30 17:49:09.889 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-30 17:49:09.890 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-30 17:49:09.891 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-30 17:49:09.891 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 17:49:09.897 [Information] CANRegisterAccess: Read value 0x94 from register 0x0140 (simulated)
2025-07-30 17:49:09.897 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-30 17:49:09.898 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:49:09.900 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:49:09.901 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:49:09.912 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-30 17:49:09.913 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-30 17:49:09.914 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-30 17:49:09.919 [Information] VocomService: Sending data and waiting for response
2025-07-30 17:49:09.920 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-30 17:49:09.971 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-30 17:49:09.972 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-30 17:49:09.973 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-30 17:49:09.975 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:49:09.975 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:49:09.986 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:49:09.987 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-30 17:49:09.987 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-30 17:49:09.998 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-30 17:49:10.009 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-30 17:49:10.020 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-30 17:49:10.031 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-30 17:49:10.042 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:49:10.044 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:49:10.045 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:49:10.056 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:49:10.057 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-30 17:49:10.057 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-30 17:49:10.069 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-30 17:49:10.080 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-30 17:49:10.091 [Information] IICProtocolHandler: Enabling IIC module
2025-07-30 17:49:10.102 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-30 17:49:10.113 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-30 17:49:10.124 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:49:10.126 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:49:10.127 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:49:10.138 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:49:10.139 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-30 17:49:10.140 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:49:10.140 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:49:10.141 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-30 17:49:10.141 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:49:10.142 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-30 17:49:10.142 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-30 17:49:10.142 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-30 17:49:10.143 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-30 17:49:10.143 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-30 17:49:10.143 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-30 17:49:10.144 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-30 17:49:10.144 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-30 17:49:10.144 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-30 17:49:10.144 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-30 17:49:10.145 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-30 17:49:10.245 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:49:10.246 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-30 17:49:10.250 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-30 17:49:10.251 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:10.251 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-30 17:49:10.252 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-30 17:49:10.252 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:10.252 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-30 17:49:10.253 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-30 17:49:10.253 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:10.254 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-30 17:49:10.254 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-30 17:49:10.254 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:10.255 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-30 17:49:10.255 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-30 17:49:10.256 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-30 17:49:10.257 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-30 17:49:10.257 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-30 17:49:10.261 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-30 17:49:10.263 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-30 17:49:10.266 [Information] BackupService: Initializing backup service
2025-07-30 17:49:10.267 [Information] BackupService: Backup service initialized successfully
2025-07-30 17:49:10.267 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-30 17:49:10.267 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-30 17:49:10.270 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-30 17:49:10.310 [Information] BackupService: Compressing backup data
2025-07-30 17:49:10.320 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-30 17:49:10.321 [Information] BackupServiceFactory: Created template for category: Production
2025-07-30 17:49:10.321 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-30 17:49:10.322 [Information] BackupService: Compressing backup data
2025-07-30 17:49:10.323 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-30 17:49:10.324 [Information] BackupServiceFactory: Created template for category: Development
2025-07-30 17:49:10.324 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-30 17:49:10.325 [Information] BackupService: Compressing backup data
2025-07-30 17:49:10.326 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (443 bytes)
2025-07-30 17:49:10.326 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-30 17:49:10.326 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-30 17:49:10.327 [Information] BackupService: Compressing backup data
2025-07-30 17:49:10.328 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-07-30 17:49:10.328 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-30 17:49:10.329 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-30 17:49:10.329 [Information] BackupService: Compressing backup data
2025-07-30 17:49:10.332 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-30 17:49:10.332 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-30 17:49:10.333 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-30 17:49:10.333 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-30 17:49:10.334 [Information] BackupService: Compressing backup data
2025-07-30 17:49:10.335 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-30 17:49:10.335 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-30 17:49:10.336 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-30 17:49:10.337 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-30 17:49:10.341 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-30 17:49:10.343 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-30 17:49:10.417 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-30 17:49:10.418 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-30 17:49:10.420 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-30 17:49:10.420 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-30 17:49:10.420 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-30 17:49:10.422 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-30 17:49:10.423 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-30 17:49:10.427 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-30 17:49:10.427 [Information] App: Flash operation monitor service initialized successfully
2025-07-30 17:49:10.439 [Information] LicensingService: Initializing licensing service
2025-07-30 17:49:10.484 [Information] LicensingService: License information loaded successfully
2025-07-30 17:49:10.488 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-30 17:49:10.488 [Information] App: Licensing service initialized successfully
2025-07-30 17:49:10.489 [Information] App: License status: Trial
2025-07-30 17:49:10.489 [Information] App: Trial period: 27 days remaining
2025-07-30 17:49:10.490 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-30 17:49:10.518 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-30 17:49:10.673 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 17:49:10.673 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 17:49:10.673 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 17:49:10.674 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 17:49:10.674 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 17:49:10.675 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 17:49:10.675 [Information] VocomService: Native USB communication service initialized
2025-07-30 17:49:10.675 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 17:49:10.676 [Information] VocomService: Connection recovery service initialized
2025-07-30 17:49:10.676 [Information] VocomService: Enhanced services initialization completed
2025-07-30 17:49:10.676 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:49:10.690 [Information] VocomService: PTT application is not running
2025-07-30 17:49:10.690 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:10.691 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:10.691 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 17:49:10.742 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-30 17:49:10.742 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-30 17:49:10.743 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-30 17:49:10.743 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:49:10.743 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-30 17:49:10.743 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 01f923a3-069a-417b-8866-fb9fa05aa681
2025-07-30 17:49:10.745 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-30 17:49:10.746 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:49:10.746 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-30 17:49:10.747 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-30 17:49:10.748 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-30 17:49:10.749 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-30 17:49:10.750 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-30 17:49:10.750 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:49:10.751 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:49:10.762 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:49:10.762 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-30 17:49:10.763 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-30 17:49:10.763 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:49:10.764 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:49:10.764 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-30 17:49:10.764 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:49:10.764 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-30 17:49:10.765 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-30 17:49:10.765 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-30 17:49:10.765 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-30 17:49:10.766 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-30 17:49:10.766 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-30 17:49:10.766 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-30 17:49:10.766 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-30 17:49:10.767 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-30 17:49:10.767 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-30 17:49:10.767 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-30 17:49:10.773 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-30 17:49:10.773 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-30 17:49:10.774 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-30 17:49:10.774 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:49:10.780 [Information] CANRegisterAccess: Read value 0xFA from register 0x0141 (simulated)
2025-07-30 17:49:10.787 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:49:10.793 [Information] CANRegisterAccess: Read value 0xAB from register 0x0141 (simulated)
2025-07-30 17:49:10.793 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-30 17:49:10.794 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-30 17:49:10.794 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-30 17:49:10.800 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-30 17:49:10.800 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-30 17:49:10.807 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-30 17:49:10.807 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-30 17:49:10.807 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-30 17:49:10.815 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-30 17:49:10.815 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-30 17:49:10.816 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-30 17:49:10.822 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-30 17:49:10.822 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-30 17:49:10.830 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-30 17:49:10.831 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-30 17:49:10.837 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-30 17:49:10.837 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-30 17:49:10.843 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-30 17:49:10.843 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-30 17:49:10.849 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-30 17:49:10.849 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-30 17:49:10.856 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-30 17:49:10.856 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-30 17:49:10.863 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-30 17:49:10.864 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-30 17:49:10.870 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-30 17:49:10.870 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-30 17:49:10.877 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-30 17:49:10.877 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-30 17:49:10.883 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-30 17:49:10.883 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-30 17:49:10.890 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-30 17:49:10.890 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-30 17:49:10.896 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-30 17:49:10.897 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-30 17:49:10.903 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-30 17:49:10.903 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-30 17:49:10.909 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-30 17:49:10.909 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-30 17:49:10.916 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-30 17:49:10.916 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-30 17:49:10.923 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-30 17:49:10.923 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-30 17:49:10.930 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-30 17:49:10.930 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-30 17:49:10.931 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-30 17:49:10.937 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-30 17:49:10.937 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-30 17:49:10.937 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-30 17:49:10.938 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:49:10.944 [Information] CANRegisterAccess: Read value 0x02 from register 0x0141 (simulated)
2025-07-30 17:49:10.944 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-30 17:49:10.944 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-30 17:49:10.945 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-30 17:49:10.945 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 17:49:10.952 [Information] CANRegisterAccess: Read value 0x72 from register 0x0140 (simulated)
2025-07-30 17:49:10.952 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-30 17:49:10.953 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:49:10.953 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:49:10.953 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:49:10.965 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-30 17:49:10.965 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-30 17:49:10.965 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-30 17:49:10.966 [Information] VocomService: Sending data and waiting for response
2025-07-30 17:49:10.966 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-30 17:49:11.016 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-30 17:49:11.016 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-30 17:49:11.017 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-30 17:49:11.017 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:49:11.017 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:49:11.030 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:49:11.030 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-30 17:49:11.031 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-30 17:49:11.042 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-30 17:49:11.053 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-30 17:49:11.064 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-30 17:49:11.075 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-30 17:49:11.086 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:49:11.086 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:49:11.087 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:49:11.098 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:49:11.098 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-30 17:49:11.098 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-30 17:49:11.110 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-30 17:49:11.121 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-30 17:49:11.132 [Information] IICProtocolHandler: Enabling IIC module
2025-07-30 17:49:11.143 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-30 17:49:11.154 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-30 17:49:11.165 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:49:11.165 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:49:11.166 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:49:11.177 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:49:11.177 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-30 17:49:11.177 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:49:11.178 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:49:11.178 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-30 17:49:11.178 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:49:11.179 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-30 17:49:11.179 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-30 17:49:11.179 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-30 17:49:11.179 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-30 17:49:11.180 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-30 17:49:11.180 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-30 17:49:11.181 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-30 17:49:11.181 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-30 17:49:11.181 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-30 17:49:11.182 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-30 17:49:11.182 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-30 17:49:11.282 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:49:11.282 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-30 17:49:11.283 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-30 17:49:11.283 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:11.284 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-30 17:49:11.284 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-30 17:49:11.284 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:11.285 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-30 17:49:11.285 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-30 17:49:11.285 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:11.286 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-30 17:49:11.286 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-30 17:49:11.286 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:49:11.287 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-30 17:49:11.287 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-30 17:49:11.338 [Information] BackupService: Initializing backup service
2025-07-30 17:49:11.338 [Information] BackupService: Backup service initialized successfully
2025-07-30 17:49:11.390 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-30 17:49:11.390 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-30 17:49:11.392 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-30 17:49:11.392 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-30 17:49:11.443 [Information] BackupService: Getting predefined backup categories
2025-07-30 17:49:11.494 [Information] MainViewModel: Services initialized successfully
2025-07-30 17:49:11.498 [Information] MainViewModel: Scanning for Vocom devices
2025-07-30 17:49:11.499 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:49:11.499 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:49:11.500 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:49:11.500 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:49:11.745 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:49:11.745 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:49:11.746 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:49:11.746 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:49:11.748 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:49:11.748 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:49:11.748 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:49:11.749 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:49:12.043 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:49:12.043 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:49:12.044 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:49:12.044 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:49:12.045 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:49:12.055 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:49:12.055 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:49:12.056 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:49:12.056 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:49:12.056 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:49:12.058 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:49:12.059 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:49:12.059 [Debug] VocomService: WiFi is available
2025-07-30 17:49:12.060 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:49:12.062 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-30 17:51:04.503 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-30 17:51:04.504 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:04.506 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-30 17:51:04.508 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-30 17:51:04.904 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-30 17:51:04.907 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-30 17:51:04.909 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-30 17:51:04.917 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-30 17:51:04.919 [Information] ECUCommunicationService: No ECUs are connected
2025-07-30 17:51:04.920 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-30 17:51:04.921 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-30 17:51:04.922 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-30 17:51:04.922 [Information] ECUCommunicationService: No ECUs are connected
2025-07-30 17:51:04.924 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:51:04.947 [Information] VocomService: PTT application is not running
2025-07-30 17:51:04.947 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 17:51:04.948 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 17:51:04.948 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:51:04.968 [Information] VocomService: PTT application is not running
2025-07-30 17:51:04.969 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 17:51:04.970 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 17:51:04.971 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:51:04.971 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:51:04.972 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:04.973 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:04.973 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:04.974 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:04.974 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:04.974 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:04.975 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:04.975 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:04.976 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:04.976 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:51:04.977 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:04.977 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:04.977 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:04.978 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:04.978 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:04.979 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:51:04.979 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:51:04.980 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:51:04.980 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:51:04.980 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:51:04.981 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:51:04.981 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:51:04.981 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 17:51:05.990 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 17:51:05.991 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:51:05.992 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:51:05.994 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:05.996 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:05.997 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:05.998 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:05.999 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:06.000 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:06.001 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:06.003 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:06.004 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:06.005 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:51:06.006 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:06.007 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:06.008 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:06.009 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:06.010 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:06.011 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:51:06.012 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:51:06.013 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:51:06.014 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:51:06.015 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:51:06.016 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:51:06.017 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:51:06.019 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 17:51:07.022 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 17:51:07.024 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:51:07.025 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:51:07.027 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:07.035 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:07.037 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:07.038 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:07.039 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:07.040 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:07.041 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:07.042 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:07.043 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:07.045 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:51:07.046 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:07.046 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:07.048 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:51:07.049 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:51:07.050 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:51:07.051 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:51:07.052 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:51:07.054 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:51:07.055 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:51:07.056 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:51:07.057 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:51:07.058 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:51:07.058 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 17:51:07.059 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 17:51:07.060 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 17:51:07.060 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 17:51:07.061 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 17:51:07.062 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 17:51:07.062 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 17:51:07.063 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 17:51:07.063 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 17:51:07.064 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 17:51:07.064 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 17:51:07.065 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 17:51:07.066 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:51:07.066 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:51:07.067 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:51:07.068 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 17:51:07.068 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 17:51:07.069 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 17:51:07.070 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:51:07.071 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:51:07.072 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:51:07.073 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:51:07.074 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:51:07.075 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:51:07.076 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:07.076 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:07.077 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:07.078 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:07.079 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:07.079 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:51:07.081 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
