# Download and Install Visual C++ Redistributable
# This script downloads and installs the Microsoft Visual C++ 2015-2022 Redistributable (x64)
# to fix msvcr140.dll and related library issues

param(
    [switch]$Force,
    [switch]$Quiet
)

Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "Visual C++ Redistributable Installer" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  WARNING: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "   Some operations may fail without admin privileges" -ForegroundColor Yellow
    Write-Host ""
}

# Define download URL and paths
$vcRedistUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
$tempDir = $env:TEMP
$downloadPath = Join-Path $tempDir "vc_redist_x64.exe"
$logPath = Join-Path $PWD "vcredist_install.log"

Write-Host "🔍 Checking current Visual C++ installation..." -ForegroundColor Green

# Check if msvcr140.dll is available
$msvcr140Paths = @(
    "C:\Windows\System32\msvcr140.dll",
    "C:\Windows\SysWOW64\msvcr140.dll",
    "$PWD\msvcr140.dll",
    "$PWD\Libraries\msvcr140.dll"
)

$msvcr140Found = $false
foreach ($path in $msvcr140Paths) {
    if (Test-Path $path) {
        Write-Host "✅ Found msvcr140.dll at: $path" -ForegroundColor Green
        $msvcr140Found = $true
        break
    }
}

if ($msvcr140Found -and -not $Force) {
    Write-Host ""
    Write-Host "✅ Visual C++ Redistributable appears to be installed" -ForegroundColor Green
    Write-Host "   Use -Force parameter to reinstall anyway" -ForegroundColor Gray
    Write-Host ""
    exit 0
}

Write-Host ""
Write-Host "📥 Downloading Visual C++ Redistributable..." -ForegroundColor Green
Write-Host "   URL: $vcRedistUrl" -ForegroundColor Gray
Write-Host "   Destination: $downloadPath" -ForegroundColor Gray

try {
    # Download with progress
    $webClient = New-Object System.Net.WebClient
    $webClient.DownloadFile($vcRedistUrl, $downloadPath)
    
    if (Test-Path $downloadPath) {
        $fileSize = (Get-Item $downloadPath).Length
        Write-Host "✅ Download completed successfully" -ForegroundColor Green
        Write-Host "   File size: $([math]::Round($fileSize / 1MB, 2)) MB" -ForegroundColor Gray
    } else {
        throw "Download failed - file not found"
    }
} catch {
    Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔄 Trying alternative download method..." -ForegroundColor Yellow
    
    try {
        Invoke-WebRequest -Uri $vcRedistUrl -OutFile $downloadPath -UseBasicParsing
        Write-Host "✅ Alternative download successful" -ForegroundColor Green
    } catch {
        Write-Host "❌ Alternative download also failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 Manual installation required:" -ForegroundColor Yellow
        Write-Host "   1. Download from: $vcRedistUrl" -ForegroundColor Gray
        Write-Host "   2. Run the installer as Administrator" -ForegroundColor Gray
        Write-Host "   3. Restart the application" -ForegroundColor Gray
        exit 1
    }
}

Write-Host ""
Write-Host "🚀 Installing Visual C++ Redistributable..." -ForegroundColor Green

# Prepare installation arguments
$installArgs = @("/install", "/quiet", "/norestart")
if ($Quiet) {
    $installArgs += "/passive"
}

Write-Host "   Command: $downloadPath $($installArgs -join ' ')" -ForegroundColor Gray
Write-Host "   Log file: $logPath" -ForegroundColor Gray

try {
    # Run the installer
    $process = Start-Process -FilePath $downloadPath -ArgumentList $installArgs -Wait -PassThru -RedirectStandardOutput $logPath -RedirectStandardError $logPath
    
    Write-Host ""
    Write-Host "📊 Installation Results:" -ForegroundColor Green
    Write-Host "   Exit Code: $($process.ExitCode)" -ForegroundColor Gray
    
    switch ($process.ExitCode) {
        0 { 
            Write-Host "✅ Installation completed successfully" -ForegroundColor Green
            $success = $true
        }
        3010 { 
            Write-Host "✅ Installation completed - RESTART REQUIRED" -ForegroundColor Yellow
            Write-Host "   A system restart is needed to complete the installation" -ForegroundColor Yellow
            $success = $true
        }
        1638 { 
            Write-Host "ℹ️  A newer version is already installed" -ForegroundColor Blue
            $success = $true
        }
        1641 {
            Write-Host "✅ Installation completed - RESTART INITIATED" -ForegroundColor Yellow
            $success = $true
        }
        default {
            Write-Host "❌ Installation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
            $success = $false
        }
    }

} catch {
    Write-Host "❌ Installation error: $($_.Exception.Message)" -ForegroundColor Red
    $success = $false
}

# Clean up
try {
    if (Test-Path $downloadPath) {
        Remove-Item $downloadPath -Force
        Write-Host "🧹 Cleaned up temporary files" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️  Could not clean up temporary file: $downloadPath" -ForegroundColor Yellow
}

Write-Host ""

if ($success) {
    Write-Host "🔍 Verifying installation..." -ForegroundColor Green
    
    # Check again for msvcr140.dll
    $foundAfterInstall = $false
    foreach ($path in $msvcr140Paths) {
        if (Test-Path $path) {
            Write-Host "✅ Verified: msvcr140.dll found at $path" -ForegroundColor Green
            $foundAfterInstall = $true
            break
        }
    }
    
    if ($foundAfterInstall) {
        Write-Host ""
        Write-Host "🎉 Visual C++ Redistributable installation successful!" -ForegroundColor Green
        Write-Host "   The VolvoFlashWR application should now work properly" -ForegroundColor Green
        
        if ($process.ExitCode -eq 3010) {
            Write-Host ""
            Write-Host "⚠️  IMPORTANT: System restart required" -ForegroundColor Yellow
            Write-Host "   Please restart your computer to complete the installation" -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "⚠️  Installation completed but msvcr140.dll not found" -ForegroundColor Yellow
        Write-Host "   You may need to restart your computer" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Installation failed" -ForegroundColor Red
    Write-Host "   Check the log file for details: $logPath" -ForegroundColor Gray
    Write-Host ""
    Write-Host "💡 Try running this script as Administrator" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "Installation Complete" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

if ($success) {
    exit 0
} else {
    exit 1
}
