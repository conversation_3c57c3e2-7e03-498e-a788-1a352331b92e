using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Specialized installer for Visual C++ runtime dependencies
    /// Handles both x86 and x64 architectures with proper fallback mechanisms
    /// </summary>
    public class VCRuntimeInstaller
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string dllToLoad);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        // Visual C++ Redistributable download URLs with multiple sources
        private static readonly Dictionary<string, VCRedistInfo> VCRedistPackages = new()
        {
            ["vc_redist.x64.exe"] = new VCRedistInfo
            {
                Architecture = "x64",
                PrimaryUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                FallbackUrls = new[]
                {
                    "https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe",
                    "https://aka.ms/vs/16/release/vc_redist.x64.exe"
                },
                Libraries = new[]
                {
                    "msvcr140.dll", "msvcp140.dll", "vcruntime140.dll",
                    "api-ms-win-crt-runtime-l1-1-0.dll", "api-ms-win-crt-heap-l1-1-0.dll",
                    "api-ms-win-crt-string-l1-1-0.dll", "api-ms-win-crt-stdio-l1-1-0.dll",
                    "api-ms-win-crt-math-l1-1-0.dll", "api-ms-win-crt-locale-l1-1-0.dll"
                }
            },
            ["vc_redist.x86.exe"] = new VCRedistInfo
            {
                Architecture = "x86",
                PrimaryUrl = "https://aka.ms/vs/17/release/vc_redist.x86.exe",
                FallbackUrls = new[]
                {
                    "https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x86.exe",
                    "https://aka.ms/vs/16/release/vc_redist.x86.exe"
                },
                Libraries = new[]
                {
                    "msvcr140.dll", "msvcp140.dll", "vcruntime140.dll",
                    "api-ms-win-crt-runtime-l1-1-0.dll", "api-ms-win-crt-heap-l1-1-0.dll",
                    "api-ms-win-crt-string-l1-1-0.dll", "api-ms-win-crt-stdio-l1-1-0.dll",
                    "api-ms-win-crt-math-l1-1-0.dll", "api-ms-win-crt-locale-l1-1-0.dll"
                }
            }
        };

        public VCRuntimeInstaller(ILoggingService logger, string applicationPath)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = applicationPath ?? throw new ArgumentNullException(nameof(applicationPath));
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
        }

        /// <summary>
        /// Installs missing Visual C++ runtime dependencies
        /// </summary>
        public async Task<VCRuntimeInstallResult> InstallMissingRuntimesAsync()
        {
            var result = new VCRuntimeInstallResult();
            
            try
            {
                _logger.LogInformation("Starting Visual C++ runtime dependency installation", "VCRuntimeInstaller");

                // Check current architecture and what's missing
                bool isX64Process = Environment.Is64BitProcess;
                _logger.LogInformation($"Process architecture: {(isX64Process ? "x64" : "x86")}", "VCRuntimeInstaller");

                // Check what's already available
                var missingLibraries = await CheckMissingLibrariesAsync();
                if (missingLibraries.Count == 0)
                {
                    _logger.LogInformation("All Visual C++ runtime libraries are already available", "VCRuntimeInstaller");
                    result.Success = true;
                    return result;
                }

                _logger.LogInformation($"Found {missingLibraries.Count} missing Visual C++ runtime libraries", "VCRuntimeInstaller");

                // Try to install the appropriate redistributable
                string redistPackage = isX64Process ? "vc_redist.x64.exe" : "vc_redist.x86.exe";
                bool installSuccess = await InstallRedistributableAsync(redistPackage);

                if (installSuccess)
                {
                    // Verify installation
                    var stillMissing = await CheckMissingLibrariesAsync();
                    result.Success = stillMissing.Count == 0;
                    result.InstalledLibraries = missingLibraries.Count - stillMissing.Count;
                    result.MissingLibraries = stillMissing;

                    if (result.Success)
                    {
                        _logger.LogInformation("Visual C++ runtime installation completed successfully", "VCRuntimeInstaller");
                    }
                    else
                    {
                        _logger.LogWarning($"Visual C++ runtime installation partially successful. {stillMissing.Count} libraries still missing", "VCRuntimeInstaller");
                    }
                }
                else
                {
                    result.Success = false;
                    result.MissingLibraries = missingLibraries;
                    _logger.LogWarning("Visual C++ runtime installation failed", "VCRuntimeInstaller");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during Visual C++ runtime installation: {ex.Message}", "VCRuntimeInstaller");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Checks which Visual C++ runtime libraries are missing
        /// </summary>
        private async Task<List<string>> CheckMissingLibrariesAsync()
        {
            var missingLibraries = new List<string>();
            bool isX64Process = Environment.Is64BitProcess;
            string redistPackage = isX64Process ? "vc_redist.x64.exe" : "vc_redist.x86.exe";

            if (VCRedistPackages.TryGetValue(redistPackage, out var redistInfo))
            {
                foreach (string library in redistInfo.Libraries)
                {
                    if (!IsLibraryAvailable(library))
                    {
                        missingLibraries.Add(library);
                    }
                }
            }

            await Task.CompletedTask;
            return missingLibraries;
        }

        /// <summary>
        /// Checks if a library is available in the system or application directory
        /// </summary>
        private bool IsLibraryAvailable(string libraryName)
        {
            try
            {
                // Check in application directory
                string appPath = Path.Combine(_applicationPath, libraryName);
                if (File.Exists(appPath))
                {
                    return true;
                }

                // Check in Libraries subdirectory
                string libPath = Path.Combine(_librariesPath, libraryName);
                if (File.Exists(libPath))
                {
                    return true;
                }

                // Try to load from system
                IntPtr handle = LoadLibrary(libraryName);
                if (handle != IntPtr.Zero)
                {
                    FreeLibrary(handle);
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Installs a Visual C++ redistributable package
        /// </summary>
        private async Task<bool> InstallRedistributableAsync(string redistPackage)
        {
            if (!VCRedistPackages.TryGetValue(redistPackage, out var redistInfo))
            {
                _logger.LogWarning($"Unknown redistributable package: {redistPackage}", "VCRuntimeInstaller");
                return false;
            }

            _logger.LogInformation($"Installing Visual C++ redistributable: {redistPackage}", "VCRuntimeInstaller");

            // Try primary URL first, then fallback URLs
            var urlsToTry = new List<string> { redistInfo.PrimaryUrl };
            urlsToTry.AddRange(redistInfo.FallbackUrls);

            foreach (string downloadUrl in urlsToTry)
            {
                try
                {
                    _logger.LogInformation($"Attempting to download from: {downloadUrl}", "VCRuntimeInstaller");
                    
                    string tempPath = Path.Combine(Path.GetTempPath(), $"vcredist_{Guid.NewGuid()}.exe");

                    // Download with extended timeout
                    using var httpClient = new HttpClient();
                    httpClient.Timeout = TimeSpan.FromMinutes(10); // Extended timeout for redistributables

                    var response = await httpClient.GetAsync(downloadUrl);
                    response.EnsureSuccessStatusCode();

                    using (var fileStream = File.Create(tempPath))
                    {
                        await response.Content.CopyToAsync(fileStream);
                    }

                    _logger.LogInformation($"Downloaded redistributable to: {tempPath}", "VCRuntimeInstaller");

                    // Install the redistributable
                    bool installSuccess = await RunRedistributableInstallerAsync(tempPath);

                    // Cleanup
                    try
                    {
                        if (File.Exists(tempPath))
                            File.Delete(tempPath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Cleanup warning: {ex.Message}", "VCRuntimeInstaller");
                    }

                    if (installSuccess)
                    {
                        _logger.LogInformation($"Successfully installed Visual C++ redistributable from {downloadUrl}", "VCRuntimeInstaller");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to download/install from {downloadUrl}: {ex.Message}", "VCRuntimeInstaller");
                }
            }

            _logger.LogWarning($"Failed to install Visual C++ redistributable {redistPackage} from all available URLs", "VCRuntimeInstaller");
            return false;
        }

        /// <summary>
        /// Runs the Visual C++ redistributable installer
        /// </summary>
        private async Task<bool> RunRedistributableInstallerAsync(string installerPath)
        {
            try
            {
                _logger.LogInformation($"Running Visual C++ redistributable installer: {installerPath}", "VCRuntimeInstaller");

                var processInfo = new ProcessStartInfo
                {
                    FileName = installerPath,
                    Arguments = "/quiet /norestart",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    _logger.LogWarning("Failed to start redistributable installer process", "VCRuntimeInstaller");
                    return false;
                }

                // Wait for installation with timeout
                var processTask = process.WaitForExitAsync();
                var timeoutTask = Task.Delay(TimeSpan.FromMinutes(5));
                var completedTask = await Task.WhenAny(processTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    _logger.LogWarning("Visual C++ redistributable installation timed out", "VCRuntimeInstaller");
                    try { process.Kill(); } catch { }
                    return false;
                }

                int exitCode = process.ExitCode;
                _logger.LogInformation($"Visual C++ redistributable installer exit code: {exitCode}", "VCRuntimeInstaller");

                // Exit codes: 0 = success, 1638 = already installed, 3010 = success but reboot required
                return exitCode == 0 || exitCode == 1638 || exitCode == 3010;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error running Visual C++ redistributable installer: {ex.Message}", "VCRuntimeInstaller");
                return false;
            }
        }
    }

    /// <summary>
    /// Information about a Visual C++ redistributable package
    /// </summary>
    public class VCRedistInfo
    {
        public string Architecture { get; set; } = string.Empty;
        public string PrimaryUrl { get; set; } = string.Empty;
        public string[] FallbackUrls { get; set; } = Array.Empty<string>();
        public string[] Libraries { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// Result of Visual C++ runtime installation
    /// </summary>
    public class VCRuntimeInstallResult
    {
        public bool Success { get; set; }
        public int InstalledLibraries { get; set; }
        public List<string> MissingLibraries { get; set; } = new();
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
