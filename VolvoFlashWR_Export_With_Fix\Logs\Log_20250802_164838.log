Log started at 8/2/2025 4:48:38 PM
2025-08-02 16:48:38.436 [Information] LoggingService: Logging service initialized
2025-08-02 16:48:38.453 [Information] App: Starting integrated application initialization
2025-08-02 16:48:38.455 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-02 16:48:38.456 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-02 16:48:38.458 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-02 16:48:38.460 [Information] IntegratedStartupService: Setting up application environment
2025-08-02 16:48:38.461 [Information] IntegratedStartupService: Application environment setup completed
2025-08-02 16:48:38.466 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-02 16:48:38.469 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-02 16:48:38.471 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-02 16:48:38.490 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-02 16:49:52.972 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-02 16:49:54.976 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 16:49:55.042 [Information] X64LibraryResolver: ✓ Found compatible library: msvcp140.dll
2025-08-02 16:49:55.086 [Information] X64LibraryResolver: ✓ Found compatible library: vcruntime140.dll
2025-08-02 16:49:55.088 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-02 16:49:55.210 [Information] X64LibraryResolver: Found APCI library: apci.dll (x86)
2025-08-02 16:49:55.210 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apci.dll is x86, process is x64
2025-08-02 16:49:55.275 [Information] X64LibraryResolver: Found APCI library: apcidb.dll (x86)
2025-08-02 16:49:55.276 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apcidb.dll is x86, process is x64
2025-08-02 16:49:55.664 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlus.dll (x86)
2025-08-02 16:49:55.665 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 16:49:55.786 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlusData.dll (x86)
2025-08-02 16:49:55.786 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 16:49:55.788 [Information] X64LibraryResolver: Setting up architecture bridge for x86 library compatibility
2025-08-02 16:49:55.852 [Information] X64LibraryResolver: Found architecture bridge: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe (x86)
2025-08-02 16:49:55.853 [Information] X64LibraryResolver: ✓ Architecture bridge is properly configured
2025-08-02 16:49:55.854 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-02 16:49:55.861 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 16:49:55.862 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-02 16:49:55.862 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-02 16:49:55.863 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:49:55.863 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 16:49:55.864 [Information] X64LibraryResolver: Library resolution completed. Success: True
2025-08-02 16:49:55.865 [Information] IntegratedStartupService: x64 library resolution completed successfully
2025-08-02 16:49:55.865 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-02 16:49:55.866 [Information] IntegratedStartupService: Missing libraries: 1
2025-08-02 16:49:55.866 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-02 16:49:55.867 [Information] IntegratedStartupService: Incompatible libraries: 4
2025-08-02 16:49:55.867 [Information] IntegratedStartupService: Architecture bridge required: True
2025-08-02 16:49:55.868 [Information] IntegratedStartupService: Bridge path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 16:49:55.868 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 16:49:55.869 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-02 16:49:55.869 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-02 16:49:55.870 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 16:49:55.871 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 16:49:55.873 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-02 16:49:55.875 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-02 16:49:55.878 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-02 16:49:55.882 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-02 16:49:55.889 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.892 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.894 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.894 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-02 16:49:55.897 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.899 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.900 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.901 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:49:55.904 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.905 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.907 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.908 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 16:49:55.911 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.913 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.917 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.917 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 16:49:55.921 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.924 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.927 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.928 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 16:49:55.932 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.935 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.938 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.939 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 16:49:55.949 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-08-02 16:49:55.953 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.958 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-08-02 16:49:55.959 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 16:49:55.962 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-02 16:49:55.962 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-02 16:49:55.979 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-02 16:49:55.980 [Information] VCRedistBundler: Found 7 missing VC++ libraries: msvcr140.dll, api-ms-win-crt-runtime-l1-1-0.dll, api-ms-win-crt-heap-l1-1-0.dll, api-ms-win-crt-string-l1-1-0.dll, api-ms-win-crt-stdio-l1-1-0.dll, api-ms-win-crt-math-l1-1-0.dll, api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 16:49:55.989 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-02 16:49:55.993 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 16:51:05.296 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-02 16:51:05.302 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_ee720127
2025-08-02 16:51:13.413 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-02 16:51:13.414 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-02 16:51:13.415 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-02 16:51:13.427 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-02 16:51:13.428 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-02 16:51:13.436 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-02 16:51:13.436 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-02 16:51:13.436 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-02 16:51:13.528 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-02 16:51:13.528 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-02 16:51:13.567 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-02 16:51:13.567 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 16:51:13.567 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 16:51:13.568 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 16:51:13.568 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 16:51:13.568 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 16:51:13.569 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 16:51:13.574 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-02 16:51:13.575 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-02 16:51:13.575 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-02 16:51:13.577 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-02 16:51:13.578 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-02 16:51:13.579 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-02 16:51:13.579 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-02 16:51:13.663 [Warning] IntegratedStartupService: Architecture mismatch detected for apci.dll - x86 library in x64 process
2025-08-02 16:51:13.910 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlus.dll - x86 library in x64 process
2025-08-02 16:51:14.013 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlusData.dll - x86 library in x64 process
2025-08-02 16:51:14.013 [Information] IntegratedStartupService: Architecture mismatch detected - setting up bridge environment
2025-08-02 16:51:14.014 [Information] IntegratedStartupService: Architecture bridge executable found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 16:51:14.016 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-02 16:51:14.018 [Information] LibraryExtractor: Starting library extraction process
2025-08-02 16:51:14.021 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-02 16:51:14.025 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-02 16:51:14.026 [Information] LibraryExtractor: Copying system libraries
2025-08-02 16:51:14.031 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-02 16:51:14.032 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe
2025-08-02 16:51:34.326 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 16:51:35.330 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 2/3)
2025-08-02 16:51:56.162 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 16:51:57.164 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 3/3)
2025-08-02 16:52:18.644 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 16:52:18.647 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe
2025-08-02 16:53:06.273 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 16:53:07.279 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 2/3)
2025-08-02 16:53:53.386 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 16:53:54.390 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 3/3)
2025-08-02 16:54:33.436 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 16:54:33.440 [Warning] LibraryExtractor: Failed to download library: msvcr120.dll from all available URLs
2025-08-02 16:54:33.440 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 16:55:52.466 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 16:55:53.471 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 16:57:05.596 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 16:57:06.600 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 16:58:18.684 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 16:58:18.689 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 16:58:57.381 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 16:58:58.385 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 16:59:36.384 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 16:59:37.387 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:00:24.453 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 17:00:24.457 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-02 17:00:24.457 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 17:02:02.056 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:02:02.057 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_460c9ba9-b645-40ab-89a7-6e67cc083301.exe' is denied.
2025-08-02 17:02:03.057 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 17:03:42.012 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:03:42.013 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_30c9edf4-0922-4f41-b29b-ef7cf93724c5.exe' is denied.
2025-08-02 17:03:43.014 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:05:43.018 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing.
2025-08-02 17:05:43.018 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 17:06:30.068 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 17:06:31.072 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 17:07:14.563 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 17:07:15.567 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:08:01.195 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 17:08:01.199 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-02 17:08:01.200 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 17:09:24.645 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 17:09:25.649 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 17:10:52.375 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 17:10:53.380 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:12:10.934 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 17:12:10.940 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 17:13:00.842 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 17:13:01.845 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 17:13:52.502 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 17:13:53.506 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:14:44.274 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 17:14:44.314 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-02 17:14:44.315 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 17:16:39.071 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 17:16:40.077 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 17:18:02.777 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 17:18:03.782 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:19:24.295 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 17:19:24.303 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 17:20:10.466 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 17:20:11.470 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 17:20:58.939 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 17:20:59.942 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 17:21:42.226 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 17:21:42.230 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-02 17:21:42.231 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-02 17:21:42.231 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-02 17:21:42.234 [Information] LibraryExtractor: Verifying library extraction
2025-08-02 17:21:42.235 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-02 17:21:42.235 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-02 17:21:42.235 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-02 17:21:42.236 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-02 17:21:42.236 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-02 17:21:42.240 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-02 17:21:42.242 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-02 17:21:42.244 [Information] DependencyManager: Initializing dependency manager
2025-08-02 17:21:42.245 [Information] DependencyManager: Setting up library search paths
2025-08-02 17:21:42.247 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 17:21:42.247 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 17:21:42.248 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 17:21:42.248 [Information] DependencyManager: Updated PATH environment variable
2025-08-02 17:21:42.249 [Information] DependencyManager: Verifying required directories
2025-08-02 17:21:42.250 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 17:21:42.250 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 17:21:42.250 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-02 17:21:42.251 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 17:21:42.253 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-02 17:21:42.269 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 17:21:42.270 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 17:21:42.276 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 17:21:42.285 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 17:21:42.286 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 17:21:42.292 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 17:21:42.294 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 17:21:42.295 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 17:21:42.302 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 17:21:42.304 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 17:21:42.305 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:21:42.306 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:21:42.306 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 17:21:42.307 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 17:21:42.307 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 17:21:42.307 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 17:21:42.308 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 17:21:42.308 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 17:21:42.308 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 17:21:42.309 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 17:21:42.309 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 17:21:42.309 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 17:21:42.310 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 17:21:42.310 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 17:21:42.311 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 17:21:42.311 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 17:21:42.311 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 17:21:42.312 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 17:21:42.312 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-02 17:21:42.312 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-02 17:21:42.313 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-02 17:21:42.360 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-02 17:21:42.361 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 17:21:42.361 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-02 17:21:42.362 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 17:21:42.363 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 17:21:42.365 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-02 17:21:42.445 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 17:21:42.446 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-02 17:21:42.446 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 17:21:42.447 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-02 17:21:42.448 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-02 17:21:42.449 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 17:21:42.449 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-02 17:21:42.450 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 17:21:42.450 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-02 17:21:42.452 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-02 17:21:42.452 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 17:21:42.452 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-02 17:21:42.453 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 17:21:42.453 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-02 17:21:42.454 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-02 17:21:42.525 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-02 17:21:42.525 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 17:21:42.526 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 17:21:42.527 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 17:21:42.527 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 17:21:42.528 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-08-02 17:21:42.528 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 17:21:42.528 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 17:21:42.528 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-08-02 17:21:42.529 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-02 17:21:42.530 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 17:21:42.530 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 17:21:42.531 [Information] DependencyManager: Setting up environment variables
2025-08-02 17:21:42.532 [Information] DependencyManager: Environment variables configured
2025-08-02 17:21:42.533 [Information] DependencyManager: Verifying library loading status
2025-08-02 17:21:42.921 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-08-02 17:21:42.921 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-02 17:21:42.922 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-02 17:21:42.924 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-02 17:21:42.925 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-02 17:21:42.935 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-02 17:21:42.937 [Information] IntegratedStartupService: Verifying system readiness
2025-08-02 17:21:42.938 [Information] IntegratedStartupService: System readiness verification passed
2025-08-02 17:21:42.938 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-02 17:21:42.940 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-02 17:21:42.940 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-08-02 17:21:42.940 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 17:21:42.941 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 17:21:42.941 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-02 17:21:42.941 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-02 17:21:42.942 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-02 17:21:42.942 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 17:21:42.942 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-02 17:21:42.943 [Information] App: Integrated startup completed successfully
2025-08-02 17:21:42.945 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-02 17:21:43.225 [Information] App: Initializing application services
2025-08-02 17:21:43.226 [Information] AppConfigurationService: Initializing configuration service
2025-08-02 17:21:43.227 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 17:21:43.301 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 17:21:43.302 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-02 17:21:43.303 [Information] App: Configuration service initialized successfully
2025-08-02 17:21:43.304 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-02 17:21:43.304 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-02 17:21:43.304 [Information] App: Environment variable exists: True, not 'false': False
2025-08-02 17:21:43.305 [Information] App: Final useDummyImplementations value: False
2025-08-02 17:21:43.305 [Information] App: Updating config to NOT use dummy implementations
2025-08-02 17:21:43.306 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-02 17:21:43.326 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 17:21:43.329 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-02 17:21:43.330 [Information] App: usePatchedImplementation flag is: True
2025-08-02 17:21:43.330 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-02 17:21:43.330 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-02 17:21:43.331 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-02 17:21:43.331 [Information] App: verboseLogging flag is: True
2025-08-02 17:21:43.335 [Information] App: Verifying real hardware requirements...
2025-08-02 17:21:43.336 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-02 17:21:43.336 [Information] App: ✓ Found critical library: apci.dll
2025-08-02 17:21:43.336 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-02 17:21:43.337 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-02 17:21:43.338 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 17:21:43.339 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-02 17:21:43.339 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-02 17:21:43.340 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-02 17:21:43.351 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-02 17:21:43.353 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-02 17:21:43.354 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-02 17:21:43.357 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-02 17:21:43.361 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-02 17:23:16.418 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 17:23:16.418 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-02 17:23:16.419 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-02 17:23:16.419 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:23:16.419 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 17:23:16.419 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 17:23:16.420 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 17:23:16.420 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 17:23:16.420 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 17:23:16.420 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-02 17:23:16.421 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-02 17:23:16.422 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-02 17:23:16.422 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 17:23:16.422 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 17:23:16.422 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-02 17:23:16.422 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-02 17:23:16.422 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-02 17:23:16.423 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-02 17:23:16.424 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-02 17:23:16.424 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-02 17:23:16.426 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-02 17:23:16.426 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-02 17:23:16.427 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-02 17:23:16.427 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-02 17:23:16.428 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-02 17:23:16.428 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-08-02 17:23:16.431 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-08-02 17:23:16.433 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-08-02 17:23:16.434 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-08-02 17:23:16.434 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-08-02 17:23:16.462 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-08-02 17:23:16.463 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-08-02 17:23:16.468 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-08-02 17:23:16.468 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-08-02 17:23:16.469 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-08-02 17:23:16.469 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-08-02 17:23:16.472 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-08-02 17:23:16.473 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-08-02 17:23:16.473 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-08-02 17:23:16.473 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:23:16.474 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-08-02 17:23:16.474 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-08-02 17:23:16.474 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-08-02 17:23:16.476 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-08-02 17:23:16.480 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-08-02 17:23:16.494 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-08-02 17:23:16.495 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 17:23:16.496 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-08-02 17:23:16.512 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 17:23:16.550 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-08-02 17:23:16.551 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-08-02 17:23:16.551 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-08-02 17:23:16.552 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 17:23:16.552 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-08-02 17:23:16.552 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 17:23:16.553 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-08-02 17:23:16.648 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 17:23:16.648 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-08-02 17:23:16.649 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-08-02 17:23:16.650 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-08-02 17:23:16.650 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-08-02 17:23:16.651 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-08-02 17:23:16.651 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-08-02 17:23:16.652 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-08-02 17:23:16.654 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-08-02 17:23:16.655 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-08-02 17:23:16.656 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 17:23:16.656 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-08-02 17:23:16.657 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-08-02 17:23:16.659 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-08-02 17:23:16.661 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-08-02 17:23:16.661 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-08-02 17:23:16.661 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-08-02 17:23:16.661 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-08-02 17:23:16.662 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-08-02 17:23:16.662 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-08-02 17:23:16.663 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-08-02 17:23:16.663 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-08-02 17:23:16.664 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-08-02 17:23:16.666 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-08-02 17:23:16.666 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-08-02 17:23:16.667 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-08-02 17:23:16.667 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:23:16.668 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-08-02 17:23:16.669 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-08-02 17:23:16.669 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 17:23:16.670 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-08-02 17:23:16.670 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-08-02 17:23:16.671 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-08-02 17:23:16.671 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-08-02 17:23:16.671 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-08-02 17:23:16.674 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-08-02 17:23:16.676 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-08-02 17:23:16.676 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-08-02 17:23:17.009 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-08-02 17:23:17.009 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-08-02 17:23:17.009 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 17:23:17.009 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 17:23:17.011 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-08-02 17:23:17.013 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:17.015 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:17.015 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-08-02 17:23:17.016 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:17.016 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:17.017 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-08-02 17:23:17.256 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:17.256 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-08-02 17:23:17.362 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:17.363 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-08-02 17:23:17.364 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:17.365 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:17.365 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-08-02 17:23:17.474 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:17.583 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:17.584 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-08-02 17:23:17.673 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 17:23:17.837 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 17:23:17.969 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:18.096 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:18.096 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-08-02 17:23:18.097 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 17:23:18.393 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:18.394 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-08-02 17:23:18.487 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:18.582 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:18.582 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-08-02 17:23:18.741 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 17:23:18.902 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 17:23:18.902 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-08-02 17:23:19.047 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 17:23:19.212 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 17:23:19.272 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-08-02 17:23:19.276 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-08-02 17:23:19.276 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 17:23:19.276 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-08-02 17:23:19.277 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-08-02 17:23:19.277 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-08-02 17:23:19.279 [Information] VocomDriver: Initializing Vocom driver
2025-08-02 17:23:19.281 [Information] VocomNativeInterop: Initializing Vocom driver
2025-08-02 17:23:19.285 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-08-02 17:23:19.285 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 17:23:19.285 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 17:23:19.287 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 17:23:19.288 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 17:23:19.290 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-08-02 17:23:19.291 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-08-02 17:23:19.293 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-08-02 17:23:19.349 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-08-02 17:23:19.350 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-08-02 17:23:19.350 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 17:23:19.412 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-08-02 17:23:19.570 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-08-02 17:23:19.722 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-08-02 17:23:19.769 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-08-02 17:23:19.770 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-08-02 17:23:19.770 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-08-02 17:23:19.771 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-08-02 17:23:19.772 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-08-02 17:23:19.772 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-08-02 17:23:19.772 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-08-02 17:23:19.772 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-08-02 17:23:19.772 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-08-02 17:23:19.773 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-08-02 17:23:19.773 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-08-02 17:23:19.773 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-08-02 17:23:19.773 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-08-02 17:23:19.773 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-08-02 17:23:19.774 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-08-02 17:23:19.774 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-08-02 17:23:19.774 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-08-02 17:23:19.774 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-08-02 17:23:19.774 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-08-02 17:23:19.775 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-08-02 17:23:19.775 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-08-02 17:23:19.775 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-08-02 17:23:19.775 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-08-02 17:23:19.775 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-08-02 17:23:19.775 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-08-02 17:23:19.776 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-08-02 17:23:19.776 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-08-02 17:23:19.776 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-08-02 17:23:19.776 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-08-02 17:23:19.776 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-08-02 17:23:19.777 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-08-02 17:23:19.777 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-08-02 17:23:19.777 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-08-02 17:23:19.777 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-08-02 17:23:19.777 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-08-02 17:23:19.778 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-08-02 17:23:19.778 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-08-02 17:23:19.778 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-08-02 17:23:19.778 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-08-02 17:23:19.778 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-08-02 17:23:19.779 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-08-02 17:23:19.779 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-08-02 17:23:19.779 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-08-02 17:23:19.779 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-08-02 17:23:19.780 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-08-02 17:23:19.780 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-08-02 17:23:19.781 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-08-02 17:23:19.782 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-08-02 17:23:19.783 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-08-02 17:23:19.784 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-08-02 17:23:19.784 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-08-02 17:23:19.784 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-08-02 17:23:19.785 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-08-02 17:23:19.785 [Information] VocomDriver: Vocom driver initialized successfully
2025-08-02 17:23:19.789 [Information] VocomService: Initializing Vocom service with dependencies
2025-08-02 17:23:19.790 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-08-02 17:23:19.791 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-08-02 17:23:19.792 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-08-02 17:23:19.866 [Information] WiFiCommunicationService: WiFi is available
2025-08-02 17:23:19.866 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-08-02 17:23:19.868 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-08-02 17:23:19.869 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-08-02 17:23:19.871 [Information] BluetoothCommunicationService: Bluetooth is available
2025-08-02 17:23:19.872 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-08-02 17:23:19.873 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 17:23:19.874 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 17:23:19.875 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 17:23:19.877 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 17:23:19.881 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 17:23:19.882 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 17:23:19.882 [Information] VocomService: Native USB communication service initialized
2025-08-02 17:23:19.883 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 17:23:19.883 [Information] VocomService: Connection recovery service initialized
2025-08-02 17:23:19.884 [Information] VocomService: Enhanced services initialization completed
2025-08-02 17:23:19.886 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:19.901 [Information] VocomService: PTT application is not running
2025-08-02 17:23:19.903 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:19.905 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:19.905 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 17:23:19.906 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-08-02 17:23:19.906 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-08-02 17:23:19.909 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 17:23:19.909 [Information] VocomService: Using new enhanced device detection service
2025-08-02 17:23:19.911 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 17:23:19.912 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 17:23:20.325 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 17:23:20.326 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 17:23:20.327 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 17:23:20.329 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 17:23:20.329 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 17:23:20.331 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 17:23:20.334 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 17:23:20.336 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 17:23:20.754 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 17:23:20.756 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 17:23:20.758 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 17:23:20.759 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 17:23:20.761 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 17:23:20.772 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 17:23:20.773 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 17:23:20.773 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 17:23:20.773 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 17:23:20.774 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:20.774 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:20.776 [Debug] VocomService: Checking if WiFi is available
2025-08-02 17:23:20.782 [Debug] VocomService: WiFi is available
2025-08-02 17:23:20.782 [Information] VocomService: Found 3 Vocom devices
2025-08-02 17:23:20.784 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-08-02 17:23:20.786 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 5368e9fb-4773-4a2e-906f-e93e27149f9f, Type: USB)
2025-08-02 17:23:20.786 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: d2d4d32f-85e9-414d-8cee-31118872877d, Type: Bluetooth)
2025-08-02 17:23:20.787 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: cd04d359-6b2a-4965-8d2f-ba49b3aa9a98, Type: WiFi)
2025-08-02 17:23:20.788 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-08-02 17:23:20.789 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-08-02 17:23:20.789 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-08-02 17:23:20.789 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-08-02 17:23:20.789 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-08-02 17:23:20.789 [Information] App: Architecture-aware Vocom service created successfully
2025-08-02 17:23:20.790 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 17:23:20.790 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 17:23:20.790 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 17:23:20.790 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 17:23:20.791 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 17:23:20.791 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 17:23:20.791 [Information] VocomService: Native USB communication service initialized
2025-08-02 17:23:20.792 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 17:23:20.792 [Information] VocomService: Connection recovery service initialized
2025-08-02 17:23:20.792 [Information] VocomService: Enhanced services initialization completed
2025-08-02 17:23:20.792 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:20.807 [Information] VocomService: PTT application is not running
2025-08-02 17:23:20.807 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:20.808 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:20.808 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 17:23:20.808 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-02 17:23:20.809 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-02 17:23:20.809 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-02 17:23:20.842 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 17:23:20.842 [Information] VocomService: Using new enhanced device detection service
2025-08-02 17:23:20.842 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 17:23:20.843 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 17:23:21.110 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 17:23:21.110 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 17:23:21.110 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 17:23:21.110 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 17:23:21.111 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 17:23:21.111 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 17:23:21.111 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 17:23:21.111 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 17:23:21.375 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 17:23:21.376 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 17:23:21.376 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 17:23:21.377 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 17:23:21.377 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 17:23:21.385 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 17:23:21.385 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 17:23:21.386 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 17:23:21.386 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 17:23:21.386 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:21.386 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:21.387 [Debug] VocomService: Checking if WiFi is available
2025-08-02 17:23:21.387 [Debug] VocomService: WiFi is available
2025-08-02 17:23:21.387 [Information] VocomService: Found 3 Vocom devices
2025-08-02 17:23:21.387 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-08-02 17:23:21.390 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:21.390 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:21.404 [Information] VocomService: PTT application is not running
2025-08-02 17:23:21.408 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 17:23:21.408 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 17:23:21.409 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:21.422 [Information] VocomService: PTT application is not running
2025-08-02 17:23:21.422 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 17:23:21.424 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:21.426 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:21.427 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:21.429 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:21.430 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:21.431 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:21.431 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:21.431 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:21.432 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:21.432 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:21.432 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:21.433 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:21.433 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:21.433 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:21.434 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:21.434 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:21.434 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:21.434 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:21.435 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:21.436 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:21.436 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:21.437 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:21.437 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:21.437 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:21.437 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:21.438 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 17:23:22.439 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:22.439 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:22.439 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:22.440 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:22.440 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:22.440 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:22.440 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:22.441 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:22.441 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:22.441 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:22.442 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:22.443 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:22.443 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:22.443 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:22.443 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:22.444 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:22.444 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:22.444 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:22.445 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:22.445 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:22.445 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:22.445 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:22.446 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:22.446 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:22.446 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:22.446 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 17:23:23.446 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:23.447 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:23.447 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:23.447 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:23.448 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:23.448 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:23.448 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:23.449 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:23.450 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:23.450 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:23.451 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:23.451 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:23.451 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:23.451 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:23.452 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:23.452 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:23.453 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:23.453 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:23.453 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:23.454 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:23.454 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:23.454 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:23.454 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:23.455 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:23.455 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:23.455 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 17:23:23.456 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 17:23:23.456 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 17:23:23.625 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 17:23:23.626 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 17:23:23.627 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 17:23:23.628 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 17:23:23.628 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 17:23:23.628 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 17:23:23.629 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 17:23:23.631 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 17:23:23.632 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 17:23:23.635 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-08-02 17:23:23.638 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 17:23:23.640 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 17:23:23.641 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 17:23:23.641 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 17:23:23.641 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 17:23:23.641 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:23.642 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:23.642 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-08-02 17:23:23.646 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-02 17:23:23.649 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:23.650 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-02 17:23:23.655 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 17:23:23.657 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 17:23:23.657 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 17:23:23.658 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 17:23:23.658 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 17:23:23.659 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 17:23:23.659 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 17:23:23.660 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 17:23:23.662 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 17:23:23.662 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 17:23:23.663 [Information] VocomService: Using new enhanced device detection service
2025-08-02 17:23:23.663 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 17:23:23.663 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 17:23:23.907 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 17:23:23.907 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 17:23:23.907 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 17:23:23.907 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 17:23:23.907 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 17:23:23.908 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 17:23:23.908 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 17:23:23.908 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 17:23:24.168 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 17:23:24.169 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 17:23:24.169 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 17:23:24.170 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 17:23:24.171 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 17:23:24.176 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 17:23:24.176 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 17:23:24.177 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 17:23:24.177 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 17:23:24.177 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:24.177 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:24.178 [Debug] VocomService: Checking if WiFi is available
2025-08-02 17:23:24.178 [Debug] VocomService: WiFi is available
2025-08-02 17:23:24.179 [Information] VocomService: Found 3 Vocom devices
2025-08-02 17:23:24.179 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:24.179 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:24.180 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:24.193 [Information] VocomService: PTT application is not running
2025-08-02 17:23:24.194 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 17:23:24.194 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 17:23:24.194 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:24.207 [Information] VocomService: PTT application is not running
2025-08-02 17:23:24.207 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 17:23:24.207 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:24.207 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:24.208 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:24.208 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:24.208 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:24.209 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:24.210 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:24.210 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:24.211 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:24.211 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:24.211 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:24.211 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:24.212 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:24.212 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:24.212 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:24.212 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:24.212 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:24.213 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:24.213 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:24.213 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:24.213 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:24.213 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:24.214 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:24.214 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:24.214 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:24.214 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 17:23:25.214 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:25.215 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:25.215 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:25.216 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:25.217 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:25.217 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:25.218 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:25.218 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:25.218 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:25.219 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:25.219 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:25.219 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:25.219 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:25.219 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:25.220 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:25.220 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:25.220 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:25.220 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:25.221 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:25.221 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:25.221 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:25.221 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:25.222 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:25.222 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:25.222 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:25.222 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 17:23:26.222 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:26.223 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:26.223 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:26.223 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:26.223 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:26.224 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:26.224 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:26.224 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:26.224 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:26.224 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:26.225 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:26.225 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:26.225 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:26.225 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:26.226 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:26.226 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:26.226 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:26.227 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:26.227 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:26.228 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:26.228 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:26.229 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:26.229 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:26.229 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:26.230 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:26.230 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 17:23:26.230 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 17:23:26.231 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 17:23:26.231 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 17:23:26.231 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 17:23:26.231 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 17:23:26.231 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 17:23:26.232 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 17:23:26.232 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 17:23:26.232 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 17:23:26.233 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 17:23:26.233 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 17:23:26.233 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-08-02 17:23:26.234 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 17:23:26.234 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 17:23:26.235 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 17:23:26.235 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 17:23:26.235 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 17:23:26.235 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:26.236 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:26.236 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:26.236 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:26.237 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:26.237 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:26.237 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:26.237 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:26.250 [Information] VocomService: PTT application is not running
2025-08-02 17:23:26.252 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:26.252 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:26.253 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:26.254 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-08-02 17:23:27.059 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:27.060 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:27.061 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-08-02 17:23:27.061 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:27.064 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 17:23:27.068 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 17:23:27.115 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 17:23:27.153 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 17:23:27.169 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 17:23:27.180 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 17:23:27.184 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 17:23:27.196 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 17:23:27.196 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 17:23:27.197 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 17:23:27.197 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 17:23:27.197 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 17:23:27.197 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 17:23:27.198 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 17:23:27.198 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 17:23:27.198 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 17:23:27.198 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 17:23:27.198 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 17:23:27.199 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 17:23:27.199 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 17:23:27.200 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 17:23:27.200 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 17:23:27.200 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 17:23:27.201 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 17:23:27.205 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 17:23:27.212 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 17:23:27.212 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 17:23:27.216 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 17:23:27.219 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:27.226 [Information] CANRegisterAccess: Read value 0x07 from register 0x0141 (simulated)
2025-08-02 17:23:27.227 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 17:23:27.228 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 17:23:27.229 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 17:23:27.234 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 17:23:27.235 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 17:23:27.241 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 17:23:27.241 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 17:23:27.242 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 17:23:27.247 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 17:23:27.247 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 17:23:27.248 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 17:23:27.253 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 17:23:27.253 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 17:23:27.259 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 17:23:27.259 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 17:23:27.265 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 17:23:27.265 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 17:23:27.271 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 17:23:27.271 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 17:23:27.277 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 17:23:27.277 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 17:23:27.283 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 17:23:27.284 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 17:23:27.290 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 17:23:27.290 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 17:23:27.296 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 17:23:27.296 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 17:23:27.302 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 17:23:27.302 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 17:23:27.308 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 17:23:27.308 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 17:23:27.314 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 17:23:27.314 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 17:23:27.320 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 17:23:27.320 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 17:23:27.326 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 17:23:27.326 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 17:23:27.332 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 17:23:27.332 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 17:23:27.338 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 17:23:27.338 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 17:23:27.344 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 17:23:27.344 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 17:23:27.350 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 17:23:27.351 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 17:23:27.351 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 17:23:27.357 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 17:23:27.358 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 17:23:27.358 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 17:23:27.358 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:27.364 [Information] CANRegisterAccess: Read value 0x6D from register 0x0141 (simulated)
2025-08-02 17:23:27.370 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:27.376 [Information] CANRegisterAccess: Read value 0xA8 from register 0x0141 (simulated)
2025-08-02 17:23:27.376 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 17:23:27.376 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 17:23:27.377 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 17:23:27.377 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 17:23:27.383 [Information] CANRegisterAccess: Read value 0x3D from register 0x0140 (simulated)
2025-08-02 17:23:27.383 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 17:23:27.384 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 17:23:27.387 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 17:23:27.387 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 17:23:27.398 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 17:23:27.399 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 17:23:27.399 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 17:23:27.405 [Information] VocomService: Sending data and waiting for response
2025-08-02 17:23:27.406 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 17:23:27.457 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 17:23:27.458 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 17:23:27.458 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 17:23:27.460 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 17:23:27.460 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 17:23:27.471 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 17:23:27.472 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 17:23:27.472 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 17:23:27.483 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 17:23:27.494 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 17:23:27.505 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 17:23:27.516 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 17:23:27.526 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 17:23:27.529 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 17:23:27.529 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 17:23:27.539 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 17:23:27.540 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 17:23:27.541 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 17:23:27.552 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 17:23:27.562 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 17:23:27.573 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 17:23:27.584 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 17:23:27.595 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 17:23:27.607 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 17:23:27.609 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 17:23:27.609 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 17:23:27.620 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 17:23:27.622 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 17:23:27.622 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 17:23:27.622 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 17:23:27.622 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 17:23:27.622 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 17:23:27.623 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 17:23:27.623 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 17:23:27.623 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 17:23:27.624 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 17:23:27.624 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 17:23:27.624 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 17:23:27.624 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 17:23:27.625 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 17:23:27.625 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 17:23:27.625 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 17:23:27.625 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 17:23:27.726 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 17:23:27.726 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 17:23:27.729 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 17:23:27.730 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:27.730 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 17:23:27.731 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 17:23:27.732 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:27.732 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 17:23:27.733 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 17:23:27.733 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:27.733 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 17:23:27.734 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 17:23:27.734 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:27.735 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 17:23:27.735 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 17:23:27.736 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-08-02 17:23:27.737 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-02 17:23:27.737 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-02 17:23:27.740 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-02 17:23:27.741 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-02 17:23:27.779 [Information] BackupService: Initializing backup service
2025-08-02 17:23:27.779 [Information] BackupService: Backup service initialized successfully
2025-08-02 17:23:27.779 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-02 17:23:27.780 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-02 17:23:27.782 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-02 17:23:27.821 [Information] BackupService: Compressing backup data
2025-08-02 17:23:27.835 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-08-02 17:23:27.837 [Information] BackupServiceFactory: Created template for category: Production
2025-08-02 17:23:27.837 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-02 17:23:27.838 [Information] BackupService: Compressing backup data
2025-08-02 17:23:27.839 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-08-02 17:23:27.840 [Information] BackupServiceFactory: Created template for category: Development
2025-08-02 17:23:27.840 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-02 17:23:27.841 [Information] BackupService: Compressing backup data
2025-08-02 17:23:27.842 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-08-02 17:23:27.842 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-02 17:23:27.842 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-02 17:23:27.843 [Information] BackupService: Compressing backup data
2025-08-02 17:23:27.844 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-08-02 17:23:27.844 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-02 17:23:27.845 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-02 17:23:27.845 [Information] BackupService: Compressing backup data
2025-08-02 17:23:27.846 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-08-02 17:23:27.847 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-02 17:23:27.847 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-02 17:23:27.847 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-02 17:23:27.848 [Information] BackupService: Compressing backup data
2025-08-02 17:23:27.849 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-08-02 17:23:27.850 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-02 17:23:27.850 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-02 17:23:27.852 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-02 17:23:27.856 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 17:23:27.859 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 17:23:27.949 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 17:23:27.950 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 17:23:27.952 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-02 17:23:27.953 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-02 17:23:27.953 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-02 17:23:27.955 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-02 17:23:27.955 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-02 17:23:27.960 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-02 17:23:27.960 [Information] App: Flash operation monitor service initialized successfully
2025-08-02 17:23:27.973 [Information] LicensingService: Initializing licensing service
2025-08-02 17:23:28.028 [Information] LicensingService: License information loaded successfully
2025-08-02 17:23:28.031 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-02 17:23:28.031 [Information] App: Licensing service initialized successfully
2025-08-02 17:23:28.031 [Information] App: License status: Trial
2025-08-02 17:23:28.032 [Information] App: Trial period: 24 days remaining
2025-08-02 17:23:28.033 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-02 17:23:28.062 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-02 17:23:28.214 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 17:23:28.214 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 17:23:28.214 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 17:23:28.214 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 17:23:28.215 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 17:23:28.215 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 17:23:28.215 [Information] VocomService: Native USB communication service initialized
2025-08-02 17:23:28.216 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 17:23:28.216 [Information] VocomService: Connection recovery service initialized
2025-08-02 17:23:28.216 [Information] VocomService: Enhanced services initialization completed
2025-08-02 17:23:28.216 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:28.231 [Information] VocomService: PTT application is not running
2025-08-02 17:23:28.231 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:28.232 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:28.233 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 17:23:28.283 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 17:23:28.283 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 17:23:28.284 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 17:23:28.284 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 17:23:28.285 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-08-02 17:23:28.285 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 1bc524a6-774c-421c-84d2-a046759534c7
2025-08-02 17:23:28.287 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-08-02 17:23:28.287 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 17:23:28.288 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-08-02 17:23:28.288 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-08-02 17:23:28.290 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-08-02 17:23:28.290 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-08-02 17:23:28.291 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-08-02 17:23:28.291 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-08-02 17:23:28.292 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-08-02 17:23:28.303 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 17:23:28.303 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-08-02 17:23:28.304 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-08-02 17:23:28.304 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-08-02 17:23:28.304 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-08-02 17:23:28.304 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-08-02 17:23:28.305 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-08-02 17:23:28.305 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-08-02 17:23:28.305 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-08-02 17:23:28.306 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-08-02 17:23:28.306 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-08-02 17:23:28.306 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-08-02 17:23:28.306 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-08-02 17:23:28.307 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-08-02 17:23:28.307 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-08-02 17:23:28.307 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-08-02 17:23:28.307 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-08-02 17:23:28.308 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-08-02 17:23:28.314 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-08-02 17:23:28.314 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-08-02 17:23:28.315 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-08-02 17:23:28.315 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:28.321 [Information] CANRegisterAccess: Read value 0xFC from register 0x0141 (simulated)
2025-08-02 17:23:28.327 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:28.333 [Information] CANRegisterAccess: Read value 0x09 from register 0x0141 (simulated)
2025-08-02 17:23:28.333 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-08-02 17:23:28.334 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-08-02 17:23:28.334 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-08-02 17:23:28.340 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-08-02 17:23:28.340 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-08-02 17:23:28.346 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-08-02 17:23:28.346 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-08-02 17:23:28.347 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-08-02 17:23:28.353 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-08-02 17:23:28.353 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-08-02 17:23:28.354 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-08-02 17:23:28.360 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-08-02 17:23:28.360 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-08-02 17:23:28.366 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-08-02 17:23:28.366 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-08-02 17:23:28.372 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-08-02 17:23:28.372 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-08-02 17:23:28.379 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-08-02 17:23:28.379 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-08-02 17:23:28.385 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-08-02 17:23:28.386 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-08-02 17:23:28.392 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-08-02 17:23:28.392 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-08-02 17:23:28.398 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-08-02 17:23:28.398 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-08-02 17:23:28.404 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-08-02 17:23:28.404 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-08-02 17:23:28.410 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-08-02 17:23:28.410 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-08-02 17:23:28.416 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-08-02 17:23:28.416 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-08-02 17:23:28.422 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-08-02 17:23:28.422 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-08-02 17:23:28.428 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-08-02 17:23:28.428 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-08-02 17:23:28.434 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-08-02 17:23:28.435 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-08-02 17:23:28.441 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-08-02 17:23:28.441 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-08-02 17:23:28.447 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-08-02 17:23:28.447 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-08-02 17:23:28.453 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-08-02 17:23:28.453 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-08-02 17:23:28.460 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-08-02 17:23:28.460 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-08-02 17:23:28.460 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-08-02 17:23:28.467 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-08-02 17:23:28.467 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-08-02 17:23:28.468 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-08-02 17:23:28.468 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:28.510 [Information] CANRegisterAccess: Read value 0x0D from register 0x0141 (simulated)
2025-08-02 17:23:28.516 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:28.522 [Information] CANRegisterAccess: Read value 0xF1 from register 0x0141 (simulated)
2025-08-02 17:23:28.528 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:28.534 [Information] CANRegisterAccess: Read value 0xB9 from register 0x0141 (simulated)
2025-08-02 17:23:28.540 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-08-02 17:23:28.546 [Information] CANRegisterAccess: Read value 0x2A from register 0x0141 (simulated)
2025-08-02 17:23:28.546 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-08-02 17:23:28.546 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-08-02 17:23:28.547 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-08-02 17:23:28.547 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 17:23:28.553 [Information] CANRegisterAccess: Read value 0xAD from register 0x0140 (simulated)
2025-08-02 17:23:28.559 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 17:23:28.565 [Information] CANRegisterAccess: Read value 0x44 from register 0x0140 (simulated)
2025-08-02 17:23:28.571 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-08-02 17:23:28.577 [Information] CANRegisterAccess: Read value 0x35 from register 0x0140 (simulated)
2025-08-02 17:23:28.577 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-08-02 17:23:28.577 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-08-02 17:23:28.578 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-08-02 17:23:28.578 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-08-02 17:23:28.589 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-08-02 17:23:28.589 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-08-02 17:23:28.589 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-08-02 17:23:28.590 [Information] VocomService: Sending data and waiting for response
2025-08-02 17:23:28.590 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-08-02 17:23:28.639 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-08-02 17:23:28.640 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-08-02 17:23:28.640 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-08-02 17:23:28.640 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-08-02 17:23:28.641 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-08-02 17:23:28.651 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 17:23:28.652 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-08-02 17:23:28.652 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-08-02 17:23:28.663 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-08-02 17:23:28.674 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-08-02 17:23:28.685 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-08-02 17:23:28.696 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-08-02 17:23:28.707 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-08-02 17:23:28.708 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-08-02 17:23:28.708 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-08-02 17:23:28.719 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 17:23:28.720 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-08-02 17:23:28.720 [Information] IICProtocolHandler: Checking IIC bus status
2025-08-02 17:23:28.731 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-08-02 17:23:28.742 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-08-02 17:23:28.753 [Information] IICProtocolHandler: Enabling IIC module
2025-08-02 17:23:28.764 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-08-02 17:23:28.775 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-08-02 17:23:28.786 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-08-02 17:23:28.787 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-08-02 17:23:28.787 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-08-02 17:23:28.798 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 17:23:28.798 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-08-02 17:23:28.799 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-08-02 17:23:28.799 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-08-02 17:23:28.799 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-08-02 17:23:28.800 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-08-02 17:23:28.800 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-08-02 17:23:28.800 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-08-02 17:23:28.800 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-08-02 17:23:28.801 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-08-02 17:23:28.801 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-08-02 17:23:28.801 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-08-02 17:23:28.802 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-08-02 17:23:28.802 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-08-02 17:23:28.802 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-08-02 17:23:28.802 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-08-02 17:23:28.803 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-08-02 17:23:28.904 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-08-02 17:23:28.905 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-08-02 17:23:28.905 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-08-02 17:23:28.905 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:28.905 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-08-02 17:23:28.906 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-08-02 17:23:28.906 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:28.906 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-08-02 17:23:28.906 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-08-02 17:23:28.907 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:28.907 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-08-02 17:23:28.907 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-08-02 17:23:28.907 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 17:23:28.908 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-08-02 17:23:28.908 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-08-02 17:23:28.959 [Information] BackupService: Initializing backup service
2025-08-02 17:23:28.960 [Information] BackupService: Backup service initialized successfully
2025-08-02 17:23:29.010 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 17:23:29.011 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 17:23:29.012 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 17:23:29.012 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 17:23:29.063 [Information] BackupService: Getting predefined backup categories
2025-08-02 17:23:29.114 [Information] MainViewModel: Services initialized successfully
2025-08-02 17:23:29.117 [Information] MainViewModel: Scanning for Vocom devices
2025-08-02 17:23:29.119 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 17:23:29.119 [Information] VocomService: Using new enhanced device detection service
2025-08-02 17:23:29.120 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 17:23:29.120 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 17:23:29.383 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-08-02 17:23:29.384 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 17:23:29.384 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 17:23:29.384 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-08-02 17:23:29.384 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-08-02 17:23:29.384 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-08-02 17:23:29.385 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-08-02 17:23:29.385 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-08-02 17:23:29.691 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-08-02 17:23:29.691 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-08-02 17:23:29.692 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-08-02 17:23:29.692 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-08-02 17:23:29.693 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-08-02 17:23:29.701 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-08-02 17:23:29.701 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-08-02 17:23:29.702 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-08-02 17:23:29.702 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-08-02 17:23:29.702 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 17:23:29.704 [Debug] VocomService: Bluetooth is enabled
2025-08-02 17:23:29.705 [Debug] VocomService: Checking if WiFi is available
2025-08-02 17:23:29.705 [Debug] VocomService: WiFi is available
2025-08-02 17:23:29.706 [Information] VocomService: Found 3 Vocom devices
2025-08-02 17:23:29.707 [Information] MainViewModel: Found 3 Vocom device(s)
2025-08-02 17:23:39.642 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-08-02 17:23:39.642 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:39.644 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-08-02 17:23:39.645 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:40.051 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-08-02 17:23:40.052 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-08-02 17:23:40.052 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 17:23:40.055 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 17:23:40.056 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 17:23:40.057 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-08-02 17:23:40.058 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-08-02 17:23:40.058 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-08-02 17:23:40.058 [Information] ECUCommunicationService: No ECUs are connected
2025-08-02 17:23:40.059 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:40.071 [Information] VocomService: PTT application is not running
2025-08-02 17:23:40.071 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-08-02 17:23:40.071 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-08-02 17:23:40.072 [Information] VocomService: Checking if PTT application is running
2025-08-02 17:23:40.083 [Information] VocomService: PTT application is not running
2025-08-02 17:23:40.083 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-08-02 17:23:40.083 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:40.083 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:40.084 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:40.086 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:40.087 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:40.087 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:40.087 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:40.087 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:40.088 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:40.088 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:40.088 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:40.089 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:40.089 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:40.089 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:40.090 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:40.090 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:40.090 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:40.090 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:40.090 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:40.091 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:40.091 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:40.091 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:40.091 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:40.092 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:40.092 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:40.092 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 17:23:41.097 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:41.098 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:41.098 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:41.099 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:41.099 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:41.099 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:41.099 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:41.099 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:41.100 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:41.100 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:41.100 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:41.100 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:41.100 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:41.100 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:41.101 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:41.101 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:41.101 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:41.101 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:41.102 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:41.102 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:41.102 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:41.102 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:41.103 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:41.103 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:41.103 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:41.103 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 17:23:42.104 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-08-02 17:23:42.104 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-08-02 17:23:42.105 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-08-02 17:23:42.105 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:42.105 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:42.106 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:42.106 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:42.106 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:42.106 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:42.107 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:42.107 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:42.107 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:42.108 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 17:23:42.108 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:42.108 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:42.108 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 17:23:42.109 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-08-02 17:23:42.109 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-08-02 17:23:42.109 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-08-02 17:23:42.110 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:42.110 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 17:23:42.110 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-08-02 17:23:42.110 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 17:23:42.111 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-08-02 17:23:42.111 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-08-02 17:23:42.111 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 17:23:42.112 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 17:23:42.112 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-08-02 17:23:42.112 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 17:23:42.112 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-08-02 17:23:42.113 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 17:23:42.113 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 17:23:42.113 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 17:23:42.113 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 17:23:42.114 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-08-02 17:23:42.114 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 17:23:42.114 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-08-02 17:23:42.114 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-08-02 17:23:42.115 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 17:23:42.115 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-08-02 17:23:42.115 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 17:23:42.116 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 17:23:42.116 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-08-02 17:23:42.116 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:42.116 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:42.117 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:42.117 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:42.118 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:42.118 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-08-02 17:23:42.118 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:42.119 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:42.119 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:42.119 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:42.119 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:42.119 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-08-02 17:23:42.120 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
