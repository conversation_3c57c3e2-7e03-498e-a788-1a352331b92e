@echo off
title Test Bridge Service Architecture Fix
echo === Testing Bridge Service Architecture Fix ===
echo.

echo 1. Verifying bridge executable exists...
if exist "VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe" (
    echo ✓ Bridge executable found
) else (
    echo ✗ Bridge executable not found
    pause
    exit /b 1
)

echo.
echo 2. Testing bridge architecture and APCI library loading...
echo Starting bridge process for 5 seconds to verify x86 architecture...
echo.

cd VolvoFlashWR_Export_With_Fix\Bridge
start /B VolvoFlashWR.VocomBridge.exe test_pipe > bridge_test.log 2>&1

echo Waiting 5 seconds for bridge initialization...
timeout /t 5 /nobreak > nul

echo.
echo 3. Checking bridge test results...
if exist "bridge_test.log" (
    echo ✓ Bridge log file created
    echo.
    echo === Bridge Test Results ===
    type bridge_test.log | findstr /C:"Process Architecture" /C:"Found required library" /C:"Successfully loaded APCI"
    echo === End Results ===
    echo.
    
    findstr /C:"Process Architecture: x86" bridge_test.log > nul
    if %errorlevel% == 0 (
        echo ✓ Bridge is x86 architecture (correct for APCI libraries)
    ) else (
        echo ✗ Bridge architecture issue detected
    )
    
    findstr /C:"Successfully loaded APCI library" bridge_test.log > nul
    if %errorlevel% == 0 (
        echo ✓ Bridge can load APCI libraries
    ) else (
        echo ✗ Bridge cannot load APCI libraries
    )
) else (
    echo ✗ Bridge test log not created
)

echo.
echo 4. Cleaning up test process...
taskkill /F /IM VolvoFlashWR.VocomBridge.exe > nul 2>&1
if exist "bridge_test.log" del "bridge_test.log"

cd ..\..

echo.
echo 5. Next steps to complete the fix:
echo    a) Fix CompatibilityVocomService compilation issues in main project
echo    b) Compile Communication project with updated ArchitectureAwareVocomServiceFactory
echo    c) Deploy updated DLL to export folder
echo    d) Run application and verify bridge service fallback in logs
echo.

echo === Bridge Service Architecture Test Complete ===
echo.
echo The bridge service is properly configured and can load x86 APCI libraries.
echo The architecture-aware factory fix should now properly fall back to bridge service
echo when direct service communication fails due to architecture mismatch.
echo.
pause
