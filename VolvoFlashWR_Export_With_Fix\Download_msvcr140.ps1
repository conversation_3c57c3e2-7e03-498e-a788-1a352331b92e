# PowerShell script to download msvcr140.dll specifically
# This addresses the critical missing library issue

Write-Host "=== msvcr140.dll Download Script ===" -ForegroundColor Green
Write-Host ""

# Create directories
$VCRedistDir = ".\Libraries\VCRedist"
if (!(Test-Path $VCRedistDir)) {
    New-Item -ItemType Directory -Path $VCRedistDir -Force | Out-Null
}

# Function to download with retry
function Download-WithRetry {
    param(
        [string]$Url,
        [string]$OutputPath,
        [int]$MaxRetries = 3
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            Write-Host "Downloading from: $Url (Attempt $i/$MaxRetries)" -ForegroundColor Cyan
            Invoke-WebRequest -Uri $Url -OutFile $OutputPath -TimeoutSec 60 -UseBasicParsing
            
            if (Test-Path $OutputPath) {
                $fileSize = (Get-Item $OutputPath).Length
                if ($fileSize -gt 1000) {  # Basic size check
                    Write-Host "Download successful! File size: $fileSize bytes" -ForegroundColor Green
                    return $true
                }
            }
        }
        catch {
            Write-Host "Download attempt $i failed: $($_.Exception.Message)" -ForegroundColor Red
            if (Test-Path $OutputPath) {
                Remove-Item $OutputPath -Force -ErrorAction SilentlyContinue
            }
        }
        
        if ($i -lt $MaxRetries) {
            Start-Sleep -Seconds 3
        }
    }
    return $false
}

# Try to download msvcr140.dll from multiple sources
Write-Host "Attempting to download msvcr140.dll..." -ForegroundColor Cyan

$msvcr140Path = Join-Path $VCRedistDir "msvcr140.dll"

# Source 1: Try to extract from Microsoft's redistributable
Write-Host "Method 1: Extracting from Microsoft Visual C++ Redistributable..." -ForegroundColor Yellow

$vcRedistUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
$vcRedistPath = "$env:TEMP\vc_redist_temp.exe"

if (Download-WithRetry -Url $vcRedistUrl -OutputPath $vcRedistPath) {
    Write-Host "Redistributable downloaded, attempting extraction..." -ForegroundColor Cyan
    
    # Try to run the installer in extract mode
    try {
        $extractDir = "$env:TEMP\vcredist_extract"
        if (Test-Path $extractDir) {
            Remove-Item -Path $extractDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $extractDir -Force | Out-Null
        
        # Try different extraction methods
        $extracted = $false
        
        # Method A: Use /extract switch
        try {
            Start-Process -FilePath $vcRedistPath -ArgumentList "/extract:$extractDir", "/quiet" -Wait -WindowStyle Hidden
            Start-Sleep -Seconds 5
            
            $msvcr140Files = Get-ChildItem -Path $extractDir -Name "msvcr140.dll" -Recurse -ErrorAction SilentlyContinue
            if ($msvcr140Files) {
                $sourceFile = Get-ChildItem -Path $extractDir -Name "msvcr140.dll" -Recurse | Select-Object -First 1
                Copy-Item -Path $sourceFile.FullName -Destination $msvcr140Path -Force
                Write-Host "Successfully extracted msvcr140.dll!" -ForegroundColor Green
                $extracted = $true
            }
        }
        catch {
            Write-Host "Extraction method A failed: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Clean up
        if (Test-Path $extractDir) {
            Remove-Item -Path $extractDir -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        if ($extracted) {
            Write-Host "msvcr140.dll successfully obtained!" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Clean up downloaded installer
    if (Test-Path $vcRedistPath) {
        Remove-Item $vcRedistPath -Force -ErrorAction SilentlyContinue
    }
}

# Check if we got the file
if (!(Test-Path $msvcr140Path)) {
    Write-Host ""
    Write-Host "=== Alternative Solution ===" -ForegroundColor Yellow
    Write-Host "msvcr140.dll could not be automatically downloaded." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Manual steps to resolve this:" -ForegroundColor White
    Write-Host "1. Download Microsoft Visual C++ 2015-2022 Redistributable (x64)" -ForegroundColor White
    Write-Host "   URL: https://aka.ms/vs/17/release/vc_redist.x64.exe" -ForegroundColor White
    Write-Host "2. Run the installer as Administrator" -ForegroundColor White
    Write-Host "3. RESTART YOUR COMPUTER after installation" -ForegroundColor White
    Write-Host "4. Run this script again" -ForegroundColor White
    Write-Host ""
    Write-Host "The application may still work with the other libraries we've fixed." -ForegroundColor Cyan
    Write-Host "Try running: .\Run_With_VCRedist_Fix.bat" -ForegroundColor Cyan
} else {
    Write-Host ""
    Write-Host "✓ msvcr140.dll is now available!" -ForegroundColor Green
    Write-Host "Copying to application directory..." -ForegroundColor Cyan
    
    # Copy to main directory
    Copy-Item -Path $msvcr140Path -Destination ".\msvcr140.dll" -Force
    Write-Host "✓ msvcr140.dll copied to application directory" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "You can now try running: .\Run_With_VCRedist_Fix.bat" -ForegroundColor Green
}

Write-Host ""
Write-Host "Script completed!" -ForegroundColor Green
