using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Enhanced library resolver specifically designed for x64 architecture compatibility
    /// Handles the architecture mismatch between x64 application and x86 APCI libraries
    /// </summary>
    public class X64LibraryResolver
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly bool _is64BitProcess;

        // Windows API imports for library loading
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern int GetLastError();

        // Critical Visual C++ runtime libraries for x64
        private static readonly Dictionary<string, X64LibraryInfo> RequiredX64Libraries = new()
        {
            ["msvcr140.dll"] = new X64LibraryInfo
            {
                Name = "msvcr140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x64)",
                DownloadUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                Architecture = "x64",
                IsRequired = true
            },
            ["msvcp140.dll"] = new X64LibraryInfo
            {
                Name = "msvcp140.dll",
                Description = "Microsoft Visual C++ 2015-2022 C++ Runtime (x64)",
                DownloadUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                Architecture = "x64",
                IsRequired = true
            },
            ["vcruntime140.dll"] = new X64LibraryInfo
            {
                Name = "vcruntime140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x64)",
                DownloadUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                Architecture = "x64",
                IsRequired = true
            }
        };

        // APCI libraries that are x86 and need special handling
        private static readonly Dictionary<string, X64LibraryInfo> ApciLibraries = new()
        {
            ["apci.dll"] = new X64LibraryInfo
            {
                Name = "apci.dll",
                Description = "APCI Communication Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            },
            ["apcidb.dll"] = new X64LibraryInfo
            {
                Name = "apcidb.dll",
                Description = "APCI Database Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            },
            ["Volvo.ApciPlus.dll"] = new X64LibraryInfo
            {
                Name = "Volvo.ApciPlus.dll",
                Description = "Volvo APCI Plus Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            },
            ["Volvo.ApciPlusData.dll"] = new X64LibraryInfo
            {
                Name = "Volvo.ApciPlusData.dll",
                Description = "Volvo APCI Plus Data Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            }
        };

        public X64LibraryResolver(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = AppDomain.CurrentDomain.BaseDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _is64BitProcess = Environment.Is64BitProcess;

            _logger.LogInformation($"X64LibraryResolver initialized for {(_is64BitProcess ? "x64" : "x86")} process", "X64LibraryResolver");
        }

        /// <summary>
        /// Resolves all library dependencies for x64 architecture
        /// </summary>
        public async Task<LibraryResolutionResult> ResolveLibrariesAsync()
        {
            var result = new LibraryResolutionResult();

            try
            {
                _logger.LogInformation("Starting x64 library resolution", "X64LibraryResolver");

                // Ensure libraries directory exists
                if (!Directory.Exists(_librariesPath))
                {
                    Directory.CreateDirectory(_librariesPath);
                    _logger.LogInformation($"Created libraries directory: {_librariesPath}", "X64LibraryResolver");
                }

                // Step 1: Resolve Visual C++ runtime libraries
                await ResolveVCRuntimeLibrariesAsync(result);

                // Step 2: Analyze APCI library compatibility
                await AnalyzeApciLibrariesAsync(result);

                // Step 3: Set up architecture bridge if needed
                await SetupArchitectureBridgeAsync(result);

                // Step 4: Configure environment for x64 compatibility
                ConfigureX64Environment(result);

                _logger.LogInformation($"Library resolution completed. Success: {result.IsSuccessful}", "X64LibraryResolver");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during library resolution: {ex.Message}", "X64LibraryResolver");
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private async Task ResolveVCRuntimeLibrariesAsync(LibraryResolutionResult result)
        {
            _logger.LogInformation("Resolving Visual C++ runtime libraries", "X64LibraryResolver");

            foreach (var library in RequiredX64Libraries.Values)
            {
                string libraryPath = Path.Combine(_librariesPath, library.Name);
                
                if (File.Exists(libraryPath))
                {
                    // Verify architecture compatibility
                    if (IsLibraryCompatible(libraryPath))
                    {
                        result.ResolvedLibraries.Add(library.Name);
                        _logger.LogInformation($"✓ Found compatible library: {library.Name}", "X64LibraryResolver");
                        continue;
                    }
                    else
                    {
                        _logger.LogWarning($"⚠ Library architecture mismatch: {library.Name}", "X64LibraryResolver");
                    }
                }

                // Try to resolve from system
                if (await TryResolveFromSystemAsync(library))
                {
                    result.ResolvedLibraries.Add(library.Name);
                    continue;
                }

                // Try to download and install
                if (await TryDownloadAndInstallAsync(library))
                {
                    result.ResolvedLibraries.Add(library.Name);
                    continue;
                }

                // Failed to resolve
                result.MissingLibraries.Add(library.Name);
                _logger.LogWarning($"✗ Failed to resolve: {library.Name}", "X64LibraryResolver");
            }
        }

        private async Task AnalyzeApciLibrariesAsync(LibraryResolutionResult result)
        {
            _logger.LogInformation("Analyzing APCI library compatibility", "X64LibraryResolver");

            foreach (var library in ApciLibraries.Values)
            {
                var paths = new[]
                {
                    Path.Combine(_applicationPath, library.Name),
                    Path.Combine(_librariesPath, library.Name)
                };

                bool found = false;
                foreach (string path in paths)
                {
                    if (File.Exists(path))
                    {
                        string arch = GetLibraryArchitecture(path);
                        _logger.LogInformation($"Found APCI library: {library.Name} ({arch})", "X64LibraryResolver");
                        
                        if (arch == "x86" && _is64BitProcess)
                        {
                            result.IncompatibleLibraries.Add(library.Name);
                            result.RequiresArchitectureBridge = true;
                            _logger.LogWarning($"⚠ Architecture mismatch: {library.Name} is x86, process is x64", "X64LibraryResolver");
                        }
                        else if (arch == "x64")
                        {
                            result.CompatibleLibraries.Add(library.Name);
                            _logger.LogInformation($"✓ Compatible: {library.Name} is x64", "X64LibraryResolver");
                        }
                        
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    result.MissingLibraries.Add(library.Name);
                    _logger.LogWarning($"✗ Missing: {library.Name}", "X64LibraryResolver");
                }
            }
        }

        private async Task SetupArchitectureBridgeAsync(LibraryResolutionResult result)
        {
            if (!result.RequiresArchitectureBridge)
            {
                _logger.LogInformation("Architecture bridge not required", "X64LibraryResolver");
                return;
            }

            _logger.LogInformation("Setting up architecture bridge for x86 library compatibility", "X64LibraryResolver");

            string bridgePath = Path.Combine(_applicationPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");
            
            if (File.Exists(bridgePath))
            {
                string bridgeArch = GetLibraryArchitecture(bridgePath);
                _logger.LogInformation($"Found architecture bridge: {bridgePath} ({bridgeArch})", "X64LibraryResolver");
                
                if (bridgeArch == "x86")
                {
                    result.ArchitectureBridgeAvailable = true;
                    result.BridgePath = bridgePath;
                    _logger.LogInformation("✓ Architecture bridge is properly configured", "X64LibraryResolver");
                }
                else
                {
                    _logger.LogWarning($"⚠ Bridge architecture mismatch: expected x86, found {bridgeArch}", "X64LibraryResolver");
                }
            }
            else
            {
                _logger.LogWarning($"⚠ Architecture bridge not found: {bridgePath}", "X64LibraryResolver");
                result.Recommendations.Add("Consider building the VocomBridge project as x86 for APCI library compatibility");
            }

            await Task.CompletedTask;
        }

        private void ConfigureX64Environment(LibraryResolutionResult result)
        {
            _logger.LogInformation("Configuring environment for x64 compatibility", "X64LibraryResolver");

            // Set environment variables for proper library loading
            var envVars = new Dictionary<string, string>
            {
                ["USE_PATCHED_IMPLEMENTATION"] = "true",
                ["PHOENIX_VOCOM_ENABLED"] = "true",
                ["VERBOSE_LOGGING"] = "true", 
                ["APCI_LIBRARY_PATH"] = _librariesPath,
                ["FORCE_ARCHITECTURE_BRIDGE"] = result.RequiresArchitectureBridge.ToString().ToLower()
            };

            foreach (var envVar in envVars)
            {
                Environment.SetEnvironmentVariable(envVar.Key, envVar.Value, EnvironmentVariableTarget.Process);
                result.EnvironmentVariables[envVar.Key] = envVar.Value;
                _logger.LogInformation($"Set environment variable: {envVar.Key} = {envVar.Value}", "X64LibraryResolver");
            }

            // Add libraries path to PATH environment variable
            string currentPath = Environment.GetEnvironmentVariable("PATH", EnvironmentVariableTarget.Process) ?? "";
            if (!currentPath.Contains(_librariesPath))
            {
                string newPath = $"{_librariesPath};{currentPath}";
                Environment.SetEnvironmentVariable("PATH", newPath, EnvironmentVariableTarget.Process);
                _logger.LogInformation($"Added libraries path to PATH: {_librariesPath}", "X64LibraryResolver");
            }
        }

        private async Task<bool> TryResolveFromSystemAsync(X64LibraryInfo library)
        {
            var systemPaths = new[]
            {
                Environment.GetFolderPath(Environment.SpecialFolder.System),
                Environment.GetFolderPath(Environment.SpecialFolder.SystemX86)
            };

            foreach (string systemPath in systemPaths)
            {
                string sourcePath = Path.Combine(systemPath, library.Name);
                if (File.Exists(sourcePath) && IsLibraryCompatible(sourcePath))
                {
                    try
                    {
                        string targetPath = Path.Combine(_librariesPath, library.Name);
                        File.Copy(sourcePath, targetPath, true);
                        _logger.LogInformation($"✓ Copied from system: {library.Name}", "X64LibraryResolver");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to copy {library.Name} from system: {ex.Message}", "X64LibraryResolver");
                    }
                }
            }

            return false;
        }

        private async Task<bool> TryDownloadAndInstallAsync(X64LibraryInfo library)
        {
            if (string.IsNullOrEmpty(library.DownloadUrl))
                return false;

            try
            {
                _logger.LogInformation($"Downloading Visual C++ Redistributable for {library.Name}", "X64LibraryResolver");

                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(5);

                string tempPath = Path.Combine(Path.GetTempPath(), $"vcredist_{Guid.NewGuid()}.exe");

                var response = await httpClient.GetAsync(library.DownloadUrl);
                response.EnsureSuccessStatusCode();

                using (var fileStream = File.Create(tempPath))
                {
                    await response.Content.CopyToAsync(fileStream);
                }

                // Install the redistributable
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempPath,
                    Arguments = "/quiet /norestart",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    if (process.ExitCode == 0)
                    {
                        _logger.LogInformation($"✓ Installed Visual C++ Redistributable for {library.Name}", "X64LibraryResolver");

                        // Try to copy the library after installation
                        await Task.Delay(2000); // Wait for installation to complete
                        return await TryResolveFromSystemAsync(library);
                    }
                }

                // Cleanup
                if (File.Exists(tempPath))
                    File.Delete(tempPath);

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to download/install {library.Name}: {ex.Message}", "X64LibraryResolver");
                return false;
            }
        }

        private bool IsLibraryCompatible(string libraryPath)
        {
            try
            {
                string arch = GetLibraryArchitecture(libraryPath);
                return (_is64BitProcess && arch == "x64") || (!_is64BitProcess && arch == "x86");
            }
            catch
            {
                return false;
            }
        }

        private string GetLibraryArchitecture(string libraryPath)
        {
            try
            {
                using var fileStream = new FileStream(libraryPath, FileMode.Open, FileAccess.Read);
                using var reader = new BinaryReader(fileStream);

                // Skip DOS header
                fileStream.Seek(60, SeekOrigin.Begin);
                int peOffset = reader.ReadInt32();

                // Go to PE header
                fileStream.Seek(peOffset + 4, SeekOrigin.Begin);
                ushort machineType = reader.ReadUInt16();

                return machineType switch
                {
                    0x014c => "x86",
                    0x8664 => "x64",
                    _ => "unknown"
                };
            }
            catch
            {
                return "unknown";
            }
        }
    }

    /// <summary>
    /// Information about a library for x64 resolution
    /// </summary>
    public class X64LibraryInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string Architecture { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public bool RequiresBridge { get; set; }
    }

    /// <summary>
    /// Result of library resolution process
    /// </summary>
    public class LibraryResolutionResult
    {
        public bool IsSuccessful => MissingLibraries.Count == 0 || (RequiresArchitectureBridge && ArchitectureBridgeAvailable);
        public string ErrorMessage { get; set; } = string.Empty;

        public List<string> ResolvedLibraries { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
        public List<string> CompatibleLibraries { get; set; } = new();
        public List<string> IncompatibleLibraries { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();

        public bool RequiresArchitectureBridge { get; set; }
        public bool ArchitectureBridgeAvailable { get; set; }
        public string BridgePath { get; set; } = string.Empty;

        public Dictionary<string, string> EnvironmentVariables { get; set; } = new();
    }
}
