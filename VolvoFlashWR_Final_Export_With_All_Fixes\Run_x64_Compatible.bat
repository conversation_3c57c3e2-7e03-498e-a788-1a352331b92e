@echo off
echo === VolvoFlashWR x64 Enhanced Startup ===
echo Setting up environment for x64 architecture compatibility...
echo.

REM Set environment variables for proper library loading
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%~dp0Libraries
set FORCE_ARCHITECTURE_BRIDGE=true

REM Add Libraries directory to PATH
set PATH=%~dp0Libraries;%PATH%

echo Environment configured for x64 compatibility
echo Architecture Bridge: Enabled for x86 library compatibility
echo Starting VolvoFlashWR...
echo.

REM Start the application
"%~dp0VolvoFlashWR.Launcher.exe"

if errorlevel 1 (
    echo.
    echo === Application Error Detected ===
    echo Check the logs in the Logs directory for details
    echo Common issues:
    echo - Missing Visual C++ Redistributables
    echo - Architecture compatibility problems
    echo - Hardware connection issues
    echo.
    pause
)
