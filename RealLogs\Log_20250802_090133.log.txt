Log started at 8/2/2025 9:01:33 AM
2025-08-02 09:01:33.783 [Information] LoggingService: Logging service initialized
2025-08-02 09:01:33.795 [Information] App: Starting integrated application initialization
2025-08-02 09:01:33.796 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-02 09:01:33.797 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-02 09:01:33.799 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-02 09:01:33.801 [Information] IntegratedStartupService: Setting up application environment
2025-08-02 09:01:33.803 [Information] IntegratedStartupService: Application environment setup completed
2025-08-02 09:01:33.808 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-02 09:01:33.811 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-02 09:01:33.813 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-02 09:01:33.822 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-02 09:01:44.878 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-02 09:01:46.882 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 09:01:46.883 [Information] X64LibraryResolver: ✓ Found compatible library: msvcp140.dll
2025-08-02 09:01:46.883 [Information] X64LibraryResolver: ✓ Found compatible library: vcruntime140.dll
2025-08-02 09:01:46.885 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-02 09:01:46.886 [Information] X64LibraryResolver: Found APCI library: apci.dll (x86)
2025-08-02 09:01:46.886 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apci.dll is x86, process is x64
2025-08-02 09:01:46.886 [Information] X64LibraryResolver: Found APCI library: apcidb.dll (x86)
2025-08-02 09:01:46.887 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apcidb.dll is x86, process is x64
2025-08-02 09:01:47.074 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlus.dll (x86)
2025-08-02 09:01:47.074 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 09:01:47.074 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlusData.dll (x86)
2025-08-02 09:01:47.075 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 09:01:47.076 [Information] X64LibraryResolver: Setting up architecture bridge for x86 library compatibility
2025-08-02 09:01:47.076 [Information] X64LibraryResolver: Found architecture bridge: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe (x86)
2025-08-02 09:01:47.077 [Information] X64LibraryResolver: ✓ Architecture bridge is properly configured
2025-08-02 09:01:47.077 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-02 09:01:47.080 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 09:01:47.081 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-02 09:01:47.081 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-02 09:01:47.081 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:01:47.082 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 09:01:47.082 [Information] X64LibraryResolver: Library resolution completed. Success: True
2025-08-02 09:01:47.082 [Information] IntegratedStartupService: x64 library resolution completed successfully
2025-08-02 09:01:47.083 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-02 09:01:47.083 [Information] IntegratedStartupService: Missing libraries: 1
2025-08-02 09:01:47.083 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-02 09:01:47.084 [Information] IntegratedStartupService: Incompatible libraries: 4
2025-08-02 09:01:47.084 [Information] IntegratedStartupService: Architecture bridge required: True
2025-08-02 09:01:47.084 [Information] IntegratedStartupService: Bridge path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 09:01:47.085 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-02 09:01:47.085 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-02 09:01:47.085 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-02 09:01:47.086 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:01:47.086 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-02 09:01:47.088 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-02 09:01:47.089 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-02 09:01:47.090 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-02 09:01:47.092 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-02 09:01:47.120 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-08-02 09:01:47.155 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-02 09:01:47.157 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-02 09:01:47.157 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-02 09:01:47.158 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-02 09:01:47.158 [Information] VCRedistBundler: Found 1 missing VC++ libraries: msvcr140.dll
2025-08-02 09:01:47.159 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-02 09:01:47.161 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 09:01:56.078 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-02 09:01:56.081 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_b801c921
2025-08-02 09:01:59.123 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-02 09:01:59.124 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-02 09:01:59.125 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-02 09:01:59.126 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-08-02 09:01:59.126 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-02 09:01:59.127 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-08-02 09:01:59.127 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-02 09:01:59.127 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-02 09:01:59.129 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-08-02 09:01:59.129 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-02 09:01:59.130 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-08-02 09:01:59.131 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:01:59.131 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:01:59.131 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 09:01:59.132 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 09:01:59.132 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 09:01:59.133 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 09:01:59.133 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 09:01:59.133 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 09:01:59.133 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 09:01:59.134 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 09:01:59.134 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 09:01:59.134 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 09:01:59.137 [Information] VCRedistBundler: VC++ Redistributable verification: 10/11 (90.9%) required libraries found
2025-08-02 09:01:59.137 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-08-02 09:01:59.139 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 10 available, 1 missing
2025-08-02 09:01:59.139 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64))
2025-08-02 09:01:59.140 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-02 09:01:59.141 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-02 09:01:59.141 [Warning] IntegratedStartupService: Architecture mismatch detected for apci.dll - x86 library in x64 process
2025-08-02 09:01:59.141 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlus.dll - x86 library in x64 process
2025-08-02 09:01:59.142 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlusData.dll - x86 library in x64 process
2025-08-02 09:01:59.142 [Information] IntegratedStartupService: Architecture mismatch detected - setting up bridge environment
2025-08-02 09:01:59.142 [Information] IntegratedStartupService: Architecture bridge executable found at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-02 09:01:59.144 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-02 09:01:59.145 [Information] LibraryExtractor: Starting library extraction process
2025-08-02 09:01:59.146 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-02 09:01:59.148 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-02 09:01:59.149 [Information] LibraryExtractor: Copying system libraries
2025-08-02 09:01:59.152 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-02 09:01:59.153 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe
2025-08-02 09:02:05.914 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 09:02:06.917 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 2/3)
2025-08-02 09:02:10.329 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 09:02:11.331 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 3/3)
2025-08-02 09:02:14.499 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 09:02:14.502 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe
2025-08-02 09:02:23.697 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 09:02:24.700 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 2/3)
2025-08-02 09:02:30.173 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 09:02:31.176 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 3/3)
2025-08-02 09:02:37.225 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-02 09:02:37.225 [Debug] LibraryExtractor: Cleanup warning for msvcr120.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_f9ad1489-3916-425b-8d22-47d52921ecbd.exe' is denied.
2025-08-02 09:02:37.226 [Warning] LibraryExtractor: Failed to download library: msvcr120.dll from all available URLs
2025-08-02 09:02:37.227 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 09:02:51.751 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 09:02:51.752 [Debug] LibraryExtractor: Cleanup warning for msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_43f2d54d-8008-46e1-b150-e0c556f5984d.exe' is denied.
2025-08-02 09:02:52.753 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:03:04.860 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 09:03:05.863 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:03:19.385 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 09:03:19.388 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 09:03:30.098 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 09:03:31.102 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:03:37.762 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 09:03:37.762 [Debug] LibraryExtractor: Cleanup warning for msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_d847665b-d6fd-490b-8096-ed09c1ffb196.exe' is denied.
2025-08-02 09:03:38.762 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:03:44.770 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-02 09:03:44.770 [Debug] LibraryExtractor: Cleanup warning for msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_e47e48fc-710c-42e5-abc6-a24e8bb41ab5.exe' is denied.
2025-08-02 09:03:44.771 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-02 09:03:44.772 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 09:03:58.833 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 09:03:59.835 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:04:08.528 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 09:04:08.529 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_5d6d42dc-cc1f-4a7c-a023-1c68fd0e4213.exe' is denied.
2025-08-02 09:04:09.529 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:04:21.115 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 09:04:21.120 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 09:04:27.362 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 09:04:27.362 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_3e04394c-df3d-43c5-b306-0e3c3985ad46.exe' is denied.
2025-08-02 09:04:28.362 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:04:34.753 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 09:04:34.753 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_a23690c4-fa56-4271-abfe-0798a5b29010.exe' is denied.
2025-08-02 09:04:35.754 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:04:44.237 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-02 09:04:44.239 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_7769d74b-252e-46ce-98c9-b6724394b826.exe' is denied.
2025-08-02 09:04:44.240 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-02 09:04:44.241 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 09:04:54.141 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 09:04:54.141 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-heap-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_7386f950-d816-46d9-b7b2-9274d190a29f.exe' is denied.
2025-08-02 09:04:55.142 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:05:09.127 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 09:05:10.129 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:05:20.655 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 09:05:20.660 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 09:05:28.910 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 09:05:28.911 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-heap-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_ffe48309-40b4-42b2-b2f8-bb56b67d20b2.exe' is denied.
2025-08-02 09:05:29.912 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:05:35.472 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 09:05:35.472 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-heap-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_d44b9366-43a0-4aa4-922b-5a38c54bc6b6.exe' is denied.
2025-08-02 09:05:36.472 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:05:41.026 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-02 09:05:41.031 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-02 09:05:41.031 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 09:05:52.759 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 09:05:52.760 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_ef655f28-46d5-43ea-a52a-f1f0a5ab770f.exe' is denied.
2025-08-02 09:05:53.760 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:06:02.006 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 09:06:02.006 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_01be5c1b-c770-4aa2-8ddc-7a1cca6edf2d.exe' is denied.
2025-08-02 09:06:03.007 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:06:14.019 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 09:06:14.020 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_5cd5f627-c503-4e17-b1ba-6359977a9765.exe' is denied.
2025-08-02 09:06:14.021 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-02 09:06:21.702 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 09:06:21.703 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_acd92525-e606-4723-9f80-8cce74c40274.exe' is denied.
2025-08-02 09:06:22.703 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-02 09:06:29.939 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 09:06:29.939 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_b349653e-fb22-4217-9b41-b4f2636a2555.exe' is denied.
2025-08-02 09:06:30.940 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-02 09:06:39.129 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-02 09:06:39.130 [Debug] LibraryExtractor: Cleanup warning for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_f392041a-ee08-47f3-8830-f67ac7a7abc0.exe' is denied.
2025-08-02 09:06:39.131 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-02 09:06:39.131 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-02 09:06:39.132 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-02 09:06:39.134 [Information] LibraryExtractor: Verifying library extraction
2025-08-02 09:06:39.135 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-02 09:06:39.135 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-02 09:06:39.135 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-02 09:06:39.136 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-02 09:06:39.136 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-02 09:06:39.137 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-02 09:06:39.138 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-02 09:06:39.139 [Information] DependencyManager: Initializing dependency manager
2025-08-02 09:06:39.139 [Information] DependencyManager: Setting up library search paths
2025-08-02 09:06:39.140 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:06:39.140 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 09:06:39.141 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix
2025-08-02 09:06:39.141 [Information] DependencyManager: Updated PATH environment variable
2025-08-02 09:06:39.142 [Information] DependencyManager: Verifying required directories
2025-08-02 09:06:39.142 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:06:39.142 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 09:06:39.142 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-08-02 09:06:39.143 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 09:06:39.144 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-02 09:06:39.147 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 09:06:39.147 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 09:06:39.148 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 09:06:39.148 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-08-02 09:06:39.151 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-08-02 09:06:39.151 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr120.dll
2025-08-02 09:06:39.151 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 09:06:39.152 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 09:06:39.152 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 09:06:39.152 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-08-02 09:06:39.152 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-08-02 09:06:39.153 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp120.dll
2025-08-02 09:06:39.153 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 09:06:39.153 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-02 09:06:39.155 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 09:06:39.155 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 09:06:39.156 [Debug] DependencyManager: Architecture mismatch: Library api-ms-win-crt-runtime-l1-1-0.dll is x86, process is x64
2025-08-02 09:06:39.156 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:06:39.157 [Warning] DependencyManager: Failed to load VC++ Redistributable library api-ms-win-crt-runtime-l1-1-0.dll: Error 193
2025-08-02 09:06:39.157 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:06:39.157 [Debug] DependencyManager: Architecture mismatch: Library api-ms-win-crt-heap-l1-1-0.dll is x86, process is x64
2025-08-02 09:06:39.157 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 09:06:39.158 [Warning] DependencyManager: Failed to load VC++ Redistributable library api-ms-win-crt-heap-l1-1-0.dll: Error 193
2025-08-02 09:06:39.158 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 09:06:39.158 [Debug] DependencyManager: Architecture mismatch: Library api-ms-win-crt-string-l1-1-0.dll is x86, process is x64
2025-08-02 09:06:39.159 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\api-ms-win-crt-string-l1-1-0.dll
2025-08-02 09:06:39.159 [Warning] DependencyManager: Failed to load VC++ Redistributable library api-ms-win-crt-string-l1-1-0.dll: Error 193
2025-08-02 09:06:39.159 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 09:06:39.159 [Debug] DependencyManager: Architecture mismatch: Library api-ms-win-crt-stdio-l1-1-0.dll is x86, process is x64
2025-08-02 09:06:39.160 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 09:06:39.160 [Warning] DependencyManager: Failed to load VC++ Redistributable library api-ms-win-crt-stdio-l1-1-0.dll: Error 193
2025-08-02 09:06:39.160 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 09:06:39.161 [Debug] DependencyManager: Architecture mismatch: Library api-ms-win-crt-math-l1-1-0.dll is x86, process is x64
2025-08-02 09:06:39.161 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\api-ms-win-crt-math-l1-1-0.dll
2025-08-02 09:06:39.161 [Warning] DependencyManager: Failed to load VC++ Redistributable library api-ms-win-crt-math-l1-1-0.dll: Error 193
2025-08-02 09:06:39.161 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 09:06:39.162 [Debug] DependencyManager: Architecture mismatch: Library api-ms-win-crt-locale-l1-1-0.dll is x86, process is x64
2025-08-02 09:06:39.162 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 09:06:39.162 [Warning] DependencyManager: Failed to load VC++ Redistributable library api-ms-win-crt-locale-l1-1-0.dll: Error 193
2025-08-02 09:06:39.163 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 09:06:39.163 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 09:06:39.163 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-02 09:06:39.164 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 09:06:39.164 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-02 09:06:39.164 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 09:06:39.164 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-02 09:06:39.165 [Information] DependencyManager: VC++ Redistributable library loading: 2/14 (14.3%) libraries loaded
2025-08-02 09:06:39.165 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-02 09:06:39.166 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-02 09:06:39.167 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-08-02 09:06:39.167 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 09:06:39.168 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-08-02 09:06:39.168 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 09:06:39.168 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 09:06:39.168 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-02 09:06:39.169 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 09:06:39.169 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-08-02 09:06:39.169 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-08-02 09:06:39.169 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-08-02 09:06:39.170 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-08-02 09:06:39.170 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 09:06:39.170 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-08-02 09:06:39.171 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-08-02 09:06:39.171 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-08-02 09:06:39.171 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-02 09:06:39.171 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 09:06:39.172 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-08-02 09:06:39.172 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-08-02 09:06:39.172 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-08-02 09:06:39.173 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-08-02 09:06:39.173 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-08-02 09:06:39.173 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 09:06:39.173 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-08-02 09:06:39.174 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 09:06:39.174 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-08-02 09:06:39.174 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-08-02 09:06:39.174 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-08-02 09:06:39.175 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-08-02 09:06:39.175 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 09:06:39.175 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-08-02 09:06:39.176 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-08-02 09:06:39.176 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-08-02 09:06:39.176 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-08-02 09:06:39.177 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-02 09:06:39.177 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-08-02 09:06:39.177 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-08-02 09:06:39.178 [Information] DependencyManager: Setting up environment variables
2025-08-02 09:06:39.178 [Information] DependencyManager: Environment variables configured
2025-08-02 09:06:39.179 [Information] DependencyManager: Verifying library loading status
2025-08-02 09:06:39.301 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-08-02 09:06:39.302 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-02 09:06:39.302 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-02 09:06:39.303 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-08-02 09:06:39.304 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-02 09:06:39.307 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-02 09:06:39.308 [Information] IntegratedStartupService: Verifying system readiness
2025-08-02 09:06:39.308 [Information] IntegratedStartupService: System readiness verification passed
2025-08-02 09:06:39.309 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-02 09:06:39.310 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-02 09:06:39.310 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix
2025-08-02 09:06:39.310 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:06:39.310 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 09:06:39.311 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-02 09:06:39.311 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-02 09:06:39.311 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-02 09:06:39.311 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:06:39.311 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-02 09:06:39.312 [Information] App: Integrated startup completed successfully
2025-08-02 09:06:39.313 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-08-02 09:06:39.323 [Information] App: Initializing application services
2025-08-02 09:06:39.325 [Information] AppConfigurationService: Initializing configuration service
2025-08-02 09:06:39.325 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Config
2025-08-02 09:06:39.351 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 09:06:39.352 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-02 09:06:39.352 [Information] App: Configuration service initialized successfully
2025-08-02 09:06:39.353 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-02 09:06:39.353 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-02 09:06:39.353 [Information] App: Environment variable exists: True, not 'false': False
2025-08-02 09:06:39.354 [Information] App: Final useDummyImplementations value: False
2025-08-02 09:06:39.354 [Information] App: Updating config to NOT use dummy implementations
2025-08-02 09:06:39.355 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-08-02 09:06:39.362 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-08-02 09:06:39.363 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-02 09:06:39.363 [Information] App: usePatchedImplementation flag is: True
2025-08-02 09:06:39.363 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-02 09:06:39.364 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries'
2025-08-02 09:06:39.364 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-02 09:06:39.364 [Information] App: verboseLogging flag is: True
2025-08-02 09:06:39.366 [Information] App: Verifying real hardware requirements...
2025-08-02 09:06:39.366 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-02 09:06:39.366 [Information] App: ✓ Found critical library: apci.dll
2025-08-02 09:06:39.367 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-02 09:06:39.367 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-08-02 09:06:39.367 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 09:06:39.367 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-02 09:06:39.367 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-08-02 09:06:39.368 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-08-02 09:06:39.379 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-02 09:06:39.380 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-02 09:06:39.380 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-02 09:06:39.381 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-02 09:06:39.384 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-02 09:06:50.042 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_cc341135-851a-4252-88c6-6a623520be36.exe' is denied.
2025-08-02 09:06:50.043 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-02 09:06:50.044 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-02 09:06:50.044 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-02 09:06:50.044 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:06:50.044 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-02 09:06:50.044 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-02 09:06:50.045 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-02 09:06:50.045 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-02 09:06:50.045 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-02 09:06:50.045 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-02 09:06:50.046 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-02 09:06:50.046 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-02 09:06:50.047 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 09:06:50.047 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-02 09:06:50.047 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-02 09:06:50.047 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-02 09:06:50.047 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-02 09:06:50.048 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-02 09:06:50.048 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-02 09:06:50.048 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-02 09:06:50.050 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-02 09:06:50.050 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-02 09:06:50.051 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-02 09:06:50.051 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-02 09:06:50.052 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-02 09:06:50.052 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-08-02 09:06:50.053 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-08-02 09:06:50.054 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-08-02 09:06:50.054 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-08-02 09:06:50.054 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-08-02 09:06:50.070 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-08-02 09:06:50.071 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-08-02 09:06:50.073 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-08-02 09:06:50.073 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-08-02 09:06:50.073 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-08-02 09:06:50.074 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-08-02 09:06:50.075 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-08-02 09:06:50.075 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-08-02 09:06:50.075 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-08-02 09:06:50.075 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:06:50.075 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-08-02 09:06:50.076 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-08-02 09:06:50.076 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-08-02 09:06:50.078 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-08-02 09:06:50.079 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-08-02 09:06:50.081 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-08-02 09:06:50.082 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 09:06:50.082 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-08-02 09:06:50.089 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-08-02 09:06:50.097 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-08-02 09:06:50.098 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-08-02 09:06:50.098 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-08-02 09:06:50.099 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 09:06:50.099 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-08-02 09:06:50.099 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 09:06:50.100 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-08-02 09:06:50.100 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-08-02 09:06:50.100 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-08-02 09:06:50.101 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-08-02 09:06:50.101 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-08-02 09:06:50.101 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-08-02 09:06:50.102 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-08-02 09:06:50.102 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-08-02 09:06:50.103 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-08-02 09:06:50.104 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-08-02 09:06:50.104 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-08-02 09:06:50.105 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-02 09:06:50.105 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-08-02 09:06:50.105 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-08-02 09:06:50.105 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-08-02 09:06:50.106 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-08-02 09:06:50.106 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-08-02 09:06:50.107 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-08-02 09:06:50.107 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-08-02 09:06:50.107 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-08-02 09:06:50.107 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-08-02 09:06:50.108 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-08-02 09:06:50.109 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-08-02 09:06:50.110 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-08-02 09:06:50.110 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-08-02 09:06:50.111 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-08-02 09:06:50.111 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-08-02 09:06:50.111 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:06:50.112 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-08-02 09:06:50.112 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-08-02 09:06:50.112 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-02 09:06:50.113 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-08-02 09:06:50.113 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-08-02 09:06:50.114 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-08-02 09:06:50.114 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-08-02 09:06:50.114 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-08-02 09:06:50.115 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-08-02 09:06:50.117 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-08-02 09:06:50.117 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-08-02 09:06:50.120 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-08-02 09:06:50.120 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-08-02 09:06:50.120 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 09:06:50.120 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 09:06:50.122 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-08-02 09:06:50.122 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.123 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.124 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-08-02 09:06:50.124 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.124 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.125 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-08-02 09:06:50.125 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.126 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-08-02 09:06:50.126 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.126 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-08-02 09:06:50.127 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.127 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.128 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-08-02 09:06:50.128 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.129 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.129 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-08-02 09:06:50.129 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\
2025-08-02 09:06:50.130 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\
2025-08-02 09:06:50.130 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.131 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.131 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-08-02 09:06:50.131 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries
2025-08-02 09:06:50.132 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.132 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-08-02 09:06:50.132 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.133 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.133 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-08-02 09:06:50.134 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-08-02 09:06:50.134 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-08-02 09:06:50.134 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-08-02 09:06:50.135 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\
2025-08-02 09:06:50.135 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\
2025-08-02 09:06:50.135 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\
2025-08-02 09:06:50.138 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-08-02 09:06:50.139 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-08-02 09:06:50.139 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-08-02 09:06:50.140 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-08-02 09:06:50.140 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-08-02 09:06:50.142 [Information] VocomDriver: Initializing Vocom driver
2025-08-02 09:06:50.143 [Information] VocomNativeInterop: Initializing Vocom driver
2025-08-02 09:06:50.145 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-08-02 09:06:50.146 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 09:06:50.146 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 09:06:50.147 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-08-02 09:06:50.148 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-08-02 09:06:50.150 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-08-02 09:06:50.152 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-08-02 09:06:50.153 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-08-02 09:06:50.154 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-08-02 09:06:50.154 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-08-02 09:06:50.155 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-08-02 09:06:50.156 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-08-02 09:06:50.156 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-08-02 09:06:50.157 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-08-02 09:06:50.158 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-08-02 09:06:50.158 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-08-02 09:06:50.158 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-08-02 09:06:50.160 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-08-02 09:06:50.160 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-08-02 09:06:50.160 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-08-02 09:06:50.161 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-08-02 09:06:50.161 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-08-02 09:06:50.161 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-08-02 09:06:50.161 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-08-02 09:06:50.162 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-08-02 09:06:50.162 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-08-02 09:06:50.162 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-08-02 09:06:50.162 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-08-02 09:06:50.162 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-08-02 09:06:50.163 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-08-02 09:06:50.163 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-08-02 09:06:50.163 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-08-02 09:06:50.163 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-08-02 09:06:50.164 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-08-02 09:06:50.164 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-08-02 09:06:50.164 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-08-02 09:06:50.165 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-08-02 09:06:50.165 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-08-02 09:06:50.165 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-08-02 09:06:50.165 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-08-02 09:06:50.165 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-08-02 09:06:50.166 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-08-02 09:06:50.166 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-08-02 09:06:50.166 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-08-02 09:06:50.166 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-08-02 09:06:50.166 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-08-02 09:06:50.167 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-08-02 09:06:50.167 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-08-02 09:06:50.167 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-08-02 09:06:50.167 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-08-02 09:06:50.167 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-08-02 09:06:50.168 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-08-02 09:06:50.168 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-08-02 09:06:50.168 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-08-02 09:06:50.168 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-08-02 09:06:50.169 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-08-02 09:06:50.169 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-08-02 09:06:50.169 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-08-02 09:06:50.169 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-08-02 09:06:50.169 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-08-02 09:06:50.170 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-08-02 09:06:50.171 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-08-02 09:06:50.171 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-08-02 09:06:50.172 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-08-02 09:06:50.172 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-08-02 09:06:50.172 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-08-02 09:06:50.172 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-08-02 09:06:50.172 [Information] VocomDriver: Vocom driver initialized successfully
2025-08-02 09:06:50.174 [Information] VocomService: Initializing Vocom service with dependencies
2025-08-02 09:06:50.175 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-08-02 09:06:50.176 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-08-02 09:06:50.177 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-08-02 09:06:50.215 [Information] WiFiCommunicationService: WiFi is available
2025-08-02 09:06:50.216 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-08-02 09:06:50.218 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-08-02 09:06:50.218 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-08-02 09:06:50.220 [Information] BluetoothCommunicationService: Bluetooth is available
2025-08-02 09:06:50.220 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-08-02 09:06:50.221 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 09:06:50.222 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 09:06:50.223 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 09:06:50.224 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 09:06:50.227 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 09:06:50.227 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 09:06:50.227 [Information] VocomService: Native USB communication service initialized
2025-08-02 09:06:50.227 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 09:06:50.228 [Information] VocomService: Connection recovery service initialized
2025-08-02 09:06:50.228 [Information] VocomService: Enhanced services initialization completed
2025-08-02 09:06:50.229 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:50.239 [Information] VocomService: PTT application is not running
2025-08-02 09:06:50.240 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 09:06:50.241 [Debug] VocomService: Bluetooth is enabled
2025-08-02 09:06:50.242 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 09:06:50.242 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-08-02 09:06:50.242 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-08-02 09:06:50.244 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 09:06:50.244 [Information] VocomService: Using new enhanced device detection service
2025-08-02 09:06:50.245 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 09:06:50.246 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 09:06:50.510 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-08-02 09:06:50.511 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-08-02 09:06:50.512 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 09:06:50.512 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 09:06:50.513 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-08-02 09:06:50.614 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-08-02 09:06:50.615 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-08-02 09:06:50.616 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-08-02 09:06:50.616 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 1 total devices
2025-08-02 09:06:50.618 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (ID: USB\VID_178E&PID_0024\0000007658, Type: USB)
2025-08-02 09:06:50.618 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 1 real Vocom devices - using direct service
2025-08-02 09:06:50.618 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Serial: 88890300)
2025-08-02 09:06:50.619 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-08-02 09:06:50.619 [Information] App: Architecture-aware Vocom service created successfully
2025-08-02 09:06:50.619 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 09:06:50.619 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 09:06:50.620 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 09:06:50.620 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 09:06:50.620 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 09:06:50.621 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 09:06:50.621 [Information] VocomService: Native USB communication service initialized
2025-08-02 09:06:50.621 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 09:06:50.621 [Information] VocomService: Connection recovery service initialized
2025-08-02 09:06:50.621 [Information] VocomService: Enhanced services initialization completed
2025-08-02 09:06:50.622 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:50.630 [Information] VocomService: PTT application is not running
2025-08-02 09:06:50.631 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 09:06:50.631 [Debug] VocomService: Bluetooth is enabled
2025-08-02 09:06:50.631 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 09:06:50.632 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-02 09:06:50.632 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-02 09:06:50.632 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-02 09:06:50.659 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 09:06:50.660 [Information] VocomService: Using new enhanced device detection service
2025-08-02 09:06:50.660 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 09:06:50.660 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 09:06:50.835 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-08-02 09:06:50.835 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-08-02 09:06:50.835 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 09:06:50.836 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 09:06:50.836 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-08-02 09:06:50.936 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-08-02 09:06:50.936 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-08-02 09:06:50.936 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-08-02 09:06:50.937 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-08-02 09:06:50.938 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-08-02 09:06:50.938 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:50.946 [Information] VocomService: PTT application is not running
2025-08-02 09:06:50.948 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-08-02 09:06:50.949 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.949 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:50.958 [Information] VocomService: PTT application is not running
2025-08-02 09:06:50.959 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.960 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.961 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.962 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.963 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.964 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.964 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.964 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.964 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.965 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.965 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.965 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.965 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.966 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.966 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.966 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.966 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.967 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.967 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.967 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.967 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.967 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.968 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.968 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.968 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.968 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.969 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.969 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.969 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.969 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.969 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.970 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.970 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:50.970 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.970 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.970 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.971 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:50.971 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:50.971 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:50.971 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:50.972 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.972 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:50.972 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:50.973 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:50.973 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:50.973 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.973 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:50.974 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 09:06:51.975 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:51.975 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:51.975 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:51.976 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.976 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.976 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.977 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.977 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.977 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.977 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.977 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.977 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.978 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.978 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.978 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.978 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.978 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.979 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.979 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.979 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.979 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.979 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.979 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.980 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.980 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.980 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.980 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.980 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.981 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.981 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.981 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.981 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:51.981 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.981 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.982 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.982 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:51.982 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:51.982 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:51.982 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:51.983 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:51.983 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:51.983 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:51.983 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:51.983 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:51.984 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:51.984 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:51.984 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 09:06:52.985 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:52.985 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:52.986 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:52.986 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.986 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.987 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.987 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.987 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.987 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.988 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.988 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.988 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.988 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.988 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.989 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.989 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.989 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.989 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.989 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.990 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.990 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.990 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.990 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.990 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.991 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.991 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.991 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.991 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.992 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.992 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.992 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.992 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:52.993 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.993 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.993 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.993 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:52.993 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:52.994 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:52.994 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:52.994 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:52.994 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:52.994 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:52.995 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:52.995 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:52.995 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:52.995 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:52.996 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 09:06:52.996 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 09:06:52.996 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.017 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 09:06:53.018 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.019 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 09:06:53.019 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 09:06:53.020 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 09:06:53.020 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 09:06:53.020 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-08-02 09:06:53.021 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 09:06:53.022 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-08-02 09:06:53.023 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:06:53.024 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:06:53.024 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 09:06:53.024 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 09:06:53.025 [Error] VocomService: Standard USB connection failed for device 88890300
2025-08-02 09:06:53.025 [Error] VocomService: All USB connection methods failed for device 88890300
2025-08-02 09:06:53.026 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:53.026 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-08-02 09:06:53.028 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-02 09:06:53.030 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-02 09:06:53.030 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-02 09:06:53.032 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 09:06:53.034 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 09:06:53.034 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 09:06:53.035 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 09:06:53.035 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 09:06:53.035 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 09:06:53.035 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 09:06:53.035 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 09:06:53.037 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 09:06:53.037 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 09:06:53.037 [Information] VocomService: Using new enhanced device detection service
2025-08-02 09:06:53.038 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 09:06:53.038 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 09:06:53.205 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-08-02 09:06:53.206 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-08-02 09:06:53.206 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 09:06:53.206 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 09:06:53.206 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-08-02 09:06:53.306 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-08-02 09:06:53.307 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-08-02 09:06:53.307 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-08-02 09:06:53.307 [Information] VocomService: Attempting to connect to Vocom device 88890300 via USB
2025-08-02 09:06:53.308 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-08-02 09:06:53.308 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:53.317 [Information] VocomService: PTT application is not running
2025-08-02 09:06:53.317 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-08-02 09:06:53.318 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.318 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:53.328 [Information] VocomService: PTT application is not running
2025-08-02 09:06:53.328 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.328 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.328 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.329 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.329 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.329 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.330 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.330 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.330 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.330 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.330 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.331 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.331 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.331 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.331 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.332 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.332 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.332 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.332 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.333 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.333 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.333 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.334 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.334 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.334 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.334 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.335 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.335 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.335 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.335 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.336 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.336 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.336 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:53.337 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.337 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.337 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.337 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:53.338 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:53.338 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:53.338 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:53.339 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.339 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:53.339 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:53.340 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:53.340 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:53.340 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.340 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:53.340 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 09:06:54.340 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:54.341 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:54.341 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:54.341 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.342 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.342 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.342 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.342 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.342 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.343 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.343 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.343 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.343 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.343 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.344 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.344 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.344 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.344 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.344 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.345 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.345 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.345 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.345 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.346 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.346 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.346 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.346 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.347 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.347 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.347 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.347 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.347 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:54.348 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.348 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.348 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.348 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:54.348 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:54.349 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:54.349 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:54.349 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:54.349 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:54.350 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:54.350 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:54.350 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:54.350 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:54.351 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:54.351 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 09:06:55.350 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.351 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.351 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.351 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.352 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.352 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.352 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.352 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.352 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.353 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.353 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.353 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.353 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.353 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.354 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.354 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.354 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.354 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.355 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.355 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.355 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.355 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.355 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.356 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.356 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.356 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.356 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.357 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.357 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.357 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.357 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.357 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:55.358 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.358 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.358 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.358 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:55.359 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:55.359 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:55.359 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:55.359 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.360 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:55.360 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:55.360 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:55.360 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:55.360 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.361 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.361 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 09:06:55.361 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 09:06:55.361 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.362 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 09:06:55.362 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:55.362 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 09:06:55.362 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 09:06:55.362 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 09:06:55.363 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 09:06:55.363 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-08-02 09:06:55.363 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 09:06:55.363 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-08-02 09:06:55.364 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:06:55.364 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:06:55.364 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 09:06:55.364 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 09:06:55.364 [Error] VocomService: Standard USB connection failed for device 88890300
2025-08-02 09:06:55.365 [Error] VocomService: All USB connection methods failed for device 88890300
2025-08-02 09:06:55.365 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:06:55.365 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:55.366 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:55.366 [Warning] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:55.366 [Error] VocomService: Failed to connect to any Vocom device
2025-08-02 09:06:55.366 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-08-02 09:06:55.367 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-02 09:06:55.368 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-08-02 09:06:56.369 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-08-02 09:06:56.370 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 09:06:56.370 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 09:06:56.370 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 09:06:56.370 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 09:06:56.371 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 09:06:56.371 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 09:06:56.371 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 09:06:56.371 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 09:06:56.371 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 09:06:56.372 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 09:06:56.372 [Information] VocomService: Using new enhanced device detection service
2025-08-02 09:06:56.372 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 09:06:56.372 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 09:06:56.546 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-08-02 09:06:56.547 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-08-02 09:06:56.547 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 09:06:56.547 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 09:06:56.547 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-08-02 09:06:56.648 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-08-02 09:06:56.649 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-08-02 09:06:56.649 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-08-02 09:06:56.649 [Information] VocomService: Attempting to connect to Vocom device 88890300 via USB
2025-08-02 09:06:56.649 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-08-02 09:06:56.650 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:56.659 [Information] VocomService: PTT application is not running
2025-08-02 09:06:56.659 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-08-02 09:06:56.660 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.660 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:06:56.671 [Information] VocomService: PTT application is not running
2025-08-02 09:06:56.671 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.671 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.671 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.672 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.672 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.672 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.672 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.673 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.673 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.673 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.673 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.673 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.674 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.674 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.674 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.674 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.675 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.675 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.675 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.675 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.675 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.676 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.676 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.676 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.676 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.676 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.677 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.677 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.677 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.677 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.677 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.678 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.678 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:56.678 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.678 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.679 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.679 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:56.679 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:56.679 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:56.680 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:56.680 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.680 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:56.680 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:56.681 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:56.681 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:56.681 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.681 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:56.681 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 09:06:57.682 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:57.683 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:57.683 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:57.684 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.684 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.684 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.685 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.685 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.685 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.685 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.685 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.686 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.686 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.686 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.686 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.687 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.687 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.687 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.687 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.688 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.688 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.688 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.688 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.688 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.688 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.689 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.689 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.689 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.689 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.689 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.690 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.690 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:57.690 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.690 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.690 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.690 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:57.691 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:57.691 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:57.691 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:57.691 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:57.691 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:57.692 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:57.692 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:57.692 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:57.692 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:57.692 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:57.693 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 09:06:58.693 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.693 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.693 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.694 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.694 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.694 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.695 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.695 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.695 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.696 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.696 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.696 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.696 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.697 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.697 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.697 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.697 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.697 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.698 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.698 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.698 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.698 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.699 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.699 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.699 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.699 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.699 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.700 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.700 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.700 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.700 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.701 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:06:58.701 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.701 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.702 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.702 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:06:58.702 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:06:58.703 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:06:58.703 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:06:58.703 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.704 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:58.704 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:06:58.704 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:06:58.705 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:06:58.705 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.705 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.705 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 09:06:58.705 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 09:06:58.705 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.706 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 09:06:58.706 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:06:58.706 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 09:06:58.707 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 09:06:58.707 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 09:06:58.707 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 09:06:58.708 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-08-02 09:06:58.708 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 09:06:58.708 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-08-02 09:06:58.708 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:06:58.708 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:06:58.709 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 09:06:58.709 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 09:06:58.709 [Error] VocomService: Standard USB connection failed for device 88890300
2025-08-02 09:06:58.709 [Error] VocomService: All USB connection methods failed for device 88890300
2025-08-02 09:06:58.710 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:06:58.710 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:06:58.710 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:58.710 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:58.711 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:58.711 [Warning] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:06:58.711 [Error] VocomService: Failed to connect to any Vocom device
2025-08-02 09:06:58.712 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-08-02 09:06:58.712 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-08-02 09:06:58.712 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-02 09:06:58.712 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-08-02 09:07:00.713 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-08-02 09:07:00.713 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-02 09:07:00.714 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-02 09:07:00.714 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-02 09:07:00.714 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 09:07:00.714 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-02 09:07:00.715 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-02 09:07:00.715 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-02 09:07:00.715 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-02 09:07:00.715 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-08-02 09:07:00.715 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 09:07:00.716 [Information] VocomService: Using new enhanced device detection service
2025-08-02 09:07:00.716 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 09:07:00.716 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 09:07:00.890 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-08-02 09:07:00.891 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-08-02 09:07:00.891 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 09:07:00.891 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 09:07:00.891 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-08-02 09:07:00.991 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-08-02 09:07:00.992 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-08-02 09:07:00.992 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-08-02 09:07:00.992 [Information] VocomService: Attempting to connect to Vocom device 88890300 via USB
2025-08-02 09:07:00.992 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-08-02 09:07:00.993 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:07:01.001 [Information] VocomService: PTT application is not running
2025-08-02 09:07:01.001 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-08-02 09:07:01.001 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.002 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:07:01.010 [Information] VocomService: PTT application is not running
2025-08-02 09:07:01.011 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.011 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.011 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.011 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.012 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.012 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.012 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.012 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.013 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.013 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.013 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.013 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.013 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.014 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.014 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.014 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.014 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.015 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.015 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.015 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.015 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.015 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.016 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.016 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.016 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.016 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.017 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.017 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.017 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.017 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.017 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.018 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.018 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:01.018 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.018 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.018 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.019 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:07:01.019 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:01.019 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:01.019 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:01.020 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.020 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:01.020 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:07:01.020 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:01.020 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:07:01.021 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.021 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:01.021 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 09:07:02.022 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:02.022 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:02.023 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:02.023 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.024 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.024 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.024 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.024 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.024 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.025 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.025 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.025 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.025 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.025 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.026 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.026 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.026 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.026 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.027 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.027 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.027 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.027 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.027 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.028 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.028 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.028 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.028 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.028 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.029 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.029 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.029 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.029 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:02.029 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.030 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.030 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.030 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:07:02.030 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:02.030 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:02.030 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:02.031 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:02.031 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:02.031 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:07:02.031 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:02.031 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:07:02.032 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:02.032 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:02.032 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 09:07:03.032 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.033 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.033 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.033 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.034 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.034 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.034 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.034 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.035 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.035 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.035 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.036 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.036 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.036 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.036 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.037 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.037 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.037 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.037 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.038 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.038 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.038 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.038 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.039 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.039 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.039 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.039 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.040 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.040 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.040 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.041 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.041 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:03.041 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.041 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.042 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.042 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:07:03.042 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:03.042 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:03.043 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:03.043 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.043 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:03.043 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:07:03.044 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:03.044 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:07:03.044 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.044 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.044 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 09:07:03.045 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 09:07:03.045 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.045 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 09:07:03.046 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:03.046 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 09:07:03.046 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 09:07:03.047 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 09:07:03.047 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 09:07:03.047 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-08-02 09:07:03.047 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 09:07:03.048 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-08-02 09:07:03.048 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:07:03.048 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:07:03.049 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 09:07:03.049 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 09:07:03.050 [Error] VocomService: Standard USB connection failed for device 88890300
2025-08-02 09:07:03.050 [Error] VocomService: All USB connection methods failed for device 88890300
2025-08-02 09:07:03.050 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:03.050 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:03.051 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:03.051 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:03.051 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:03.052 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:03.052 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:03.052 [Warning] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:03.052 [Error] VocomService: Failed to connect to any Vocom device
2025-08-02 09:07:03.053 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-08-02 09:07:03.053 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-08-02 09:07:03.053 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-08-02 09:07:03.053 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-02 09:07:03.053 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-08-02 09:07:06.054 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-08-02 09:07:06.054 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-08-02 09:07:06.055 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-08-02 09:07:06.056 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-08-02 09:07:06.557 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-08-02 09:07:06.557 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-08-02 09:07:06.558 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-08-02 09:07:06.558 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-08-02 09:07:06.560 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-02 09:07:06.561 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-02 09:07:06.562 [Information] BackupService: Initializing backup service
2025-08-02 09:07:06.562 [Information] BackupService: Backup service initialized successfully
2025-08-02 09:07:06.562 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-02 09:07:06.563 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-02 09:07:06.564 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-02 09:07:06.586 [Information] BackupService: Compressing backup data
2025-08-02 09:07:06.591 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (446 bytes)
2025-08-02 09:07:06.592 [Information] BackupServiceFactory: Created template for category: Production
2025-08-02 09:07:06.592 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-02 09:07:06.592 [Information] BackupService: Compressing backup data
2025-08-02 09:07:06.593 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-08-02 09:07:06.593 [Information] BackupServiceFactory: Created template for category: Development
2025-08-02 09:07:06.593 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-02 09:07:06.594 [Information] BackupService: Compressing backup data
2025-08-02 09:07:06.594 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-08-02 09:07:06.595 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-02 09:07:06.595 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-02 09:07:06.595 [Information] BackupService: Compressing backup data
2025-08-02 09:07:06.596 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-08-02 09:07:06.596 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-02 09:07:06.597 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-02 09:07:06.597 [Information] BackupService: Compressing backup data
2025-08-02 09:07:06.598 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-08-02 09:07:06.598 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-02 09:07:06.598 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-02 09:07:06.599 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-02 09:07:06.599 [Information] BackupService: Compressing backup data
2025-08-02 09:07:06.600 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (508 bytes)
2025-08-02 09:07:06.600 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-02 09:07:06.600 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-02 09:07:06.601 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-02 09:07:06.603 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 09:07:06.604 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 09:07:06.642 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 09:07:06.642 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 09:07:06.643 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-02 09:07:06.643 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-02 09:07:06.644 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-02 09:07:06.644 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-02 09:07:06.645 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-02 09:07:06.647 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-02 09:07:06.647 [Information] App: Flash operation monitor service initialized successfully
2025-08-02 09:07:06.653 [Information] LicensingService: Initializing licensing service
2025-08-02 09:07:06.682 [Information] LicensingService: License information loaded successfully
2025-08-02 09:07:06.684 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-02 09:07:06.684 [Information] App: Licensing service initialized successfully
2025-08-02 09:07:06.685 [Information] App: License status: Trial
2025-08-02 09:07:06.685 [Information] App: Trial period: 30 days remaining
2025-08-02 09:07:06.686 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-02 09:07:06.700 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-08-02 09:07:06.832 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-08-02 09:07:06.833 [Information] VocomService: Initializing enhanced Vocom services
2025-08-02 09:07:06.833 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-08-02 09:07:06.833 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-08-02 09:07:06.834 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-08-02 09:07:06.834 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-08-02 09:07:06.834 [Information] VocomService: Native USB communication service initialized
2025-08-02 09:07:06.834 [Information] VocomService: Enhanced device detection service initialized
2025-08-02 09:07:06.834 [Information] VocomService: Connection recovery service initialized
2025-08-02 09:07:06.835 [Information] VocomService: Enhanced services initialization completed
2025-08-02 09:07:06.835 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:07:06.843 [Information] VocomService: PTT application is not running
2025-08-02 09:07:06.843 [Debug] VocomService: Checking if Bluetooth is enabled
2025-08-02 09:07:06.844 [Debug] VocomService: Bluetooth is enabled
2025-08-02 09:07:06.845 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-08-02 09:07:06.895 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-08-02 09:07:07.396 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-08-02 09:07:07.447 [Information] BackupService: Initializing backup service
2025-08-02 09:07:07.448 [Information] BackupService: Backup service initialized successfully
2025-08-02 09:07:07.498 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-02 09:07:07.499 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-02 09:07:07.499 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (2)\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-08-02 09:07:07.500 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-02 09:07:07.550 [Information] BackupService: Getting predefined backup categories
2025-08-02 09:07:07.603 [Information] MainViewModel: Services initialized successfully
2025-08-02 09:07:07.605 [Information] MainViewModel: Scanning for Vocom devices
2025-08-02 09:07:07.606 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-08-02 09:07:07.607 [Information] VocomService: Using new enhanced device detection service
2025-08-02 09:07:07.607 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-08-02 09:07:07.607 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-08-02 09:07:07.767 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-08-02 09:07:07.767 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-08-02 09:07:07.767 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-08-02 09:07:07.768 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-08-02 09:07:07.768 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-08-02 09:07:07.887 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-08-02 09:07:07.887 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-08-02 09:07:07.887 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-08-02 09:07:07.888 [Information] MainViewModel: Found 1 Vocom device(s)
2025-08-02 09:07:11.223 [Information] MainViewModel: Connecting to Vocom device 88890300
2025-08-02 09:07:11.224 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-08-02 09:07:11.224 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:07:11.233 [Information] VocomService: PTT application is not running
2025-08-02 09:07:11.233 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-08-02 09:07:11.233 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.233 [Information] VocomService: Checking if PTT application is running
2025-08-02 09:07:11.242 [Information] VocomService: PTT application is not running
2025-08-02 09:07:11.243 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.243 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.243 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.243 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.244 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.244 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.244 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.244 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.244 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.244 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.245 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.245 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.245 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.245 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.246 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.246 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.246 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.246 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.246 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.246 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.247 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.247 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.247 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.247 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.247 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.248 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.248 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.248 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.248 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.248 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.248 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.249 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.249 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:11.249 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.249 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.249 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.249 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:07:11.250 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:11.250 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:11.250 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:11.250 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.250 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:11.250 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:07:11.251 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:11.251 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:07:11.251 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.251 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:11.251 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-08-02 09:07:12.266 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:12.266 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:12.266 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:12.267 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.267 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.267 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.267 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.268 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.268 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.268 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.268 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.268 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.268 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.269 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.269 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.269 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.269 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.269 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.270 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.270 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.270 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.270 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.270 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.270 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.271 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.271 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.271 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.271 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.271 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.271 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:07:12.272 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:12.273 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:12.273 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:12.273 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:12.273 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:12.273 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:07:12.273 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:12.274 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:07:12.274 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:12.274 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:12.274 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-08-02 09:07:13.286 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.287 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.287 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.288 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.288 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.288 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.289 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.289 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.289 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.290 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.290 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.290 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.290 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.291 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.291 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.291 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.291 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.292 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.292 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.292 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.292 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.293 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.293 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.293 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.293 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.294 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.294 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.294 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.294 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.295 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.295 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.295 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-08-02 09:07:13.296 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.296 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.296 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.296 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-08-02 09:07:13.297 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024\0000007658'
2025-08-02 09:07:13.297 [Debug] NativeVocomUSBCommunication: Extracted serial number: 0000007658
2025-08-02 09:07:13.297 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: False, Vocom match: False
2025-08-02 09:07:13.298 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.298 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:13.298 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-08-02 09:07:13.298 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-08-02 09:07:13.299 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-08-02 09:07:13.299 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.299 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.300 [Warning] VocomService: All 3 native USB connection attempts failed
2025-08-02 09:07:13.300 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-08-02 09:07:13.301 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.301 [Information] ModernUSBCommunicationService: USB availability check: True
2025-08-02 09:07:13.301 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-08-02 09:07:13.301 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-08-02 09:07:13.301 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-08-02 09:07:13.302 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-08-02 09:07:13.302 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-08-02 09:07:13.302 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-08-02 09:07:13.302 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-08-02 09:07:13.303 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-08-02 09:07:13.303 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:07:13.303 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-08-02 09:07:13.303 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-08-02 09:07:13.303 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-08-02 09:07:13.304 [Error] VocomService: Standard USB connection failed for device 88890300
2025-08-02 09:07:13.304 [Error] VocomService: All USB connection methods failed for device 88890300
2025-08-02 09:07:13.304 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:13.304 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:13.304 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:13.305 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300
2025-08-02 09:07:13.305 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:13.305 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:13.306 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:13.306 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:13.306 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-08-02 09:07:13.307 [Error] MainViewModel: Failed to connect to Vocom device 88890300
