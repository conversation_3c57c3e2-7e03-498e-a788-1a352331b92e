# Simple Visual C++ Redistributable Installer
# Downloads and installs Microsoft Visual C++ 2015-2022 Redistributable (x64)

param(
    [switch]$Quiet
)

Write-Host "Visual C++ Redistributable Installer" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""

# Check if msvcr140.dll exists
$msvcr140Paths = @(
    "C:\Windows\System32\msvcr140.dll",
    "C:\Windows\SysWOW64\msvcr140.dll"
)

$found = $false
foreach ($path in $msvcr140Paths) {
    if (Test-Path $path) {
        Write-Host "Found msvcr140.dll at: $path" -ForegroundColor Green
        $found = $true
        break
    }
}

if ($found) {
    Write-Host "Visual C++ Redistributable appears to be installed" -ForegroundColor Green
    exit 0
}

Write-Host "Visual C++ Redistributable NOT found - installing..." -ForegroundColor Yellow
Write-Host ""

# Download URL
$url = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
$tempFile = Join-Path $env:TEMP "vc_redist_x64.exe"

Write-Host "Downloading from: $url" -ForegroundColor Gray

try {
    # Download the file
    Invoke-WebRequest -Uri $url -OutFile $tempFile -UseBasicParsing
    Write-Host "Download completed" -ForegroundColor Green
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Installing Visual C++ Redistributable..." -ForegroundColor Green

try {
    # Install silently
    $process = Start-Process -FilePath $tempFile -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
    
    Write-Host "Installation completed with exit code: $($process.ExitCode)" -ForegroundColor Gray
    
    # Clean up
    Remove-Item $tempFile -Force -ErrorAction SilentlyContinue
    
    # Check results
    switch ($process.ExitCode) {
        0 { 
            Write-Host "Installation successful" -ForegroundColor Green
            exit 0
        }
        3010 { 
            Write-Host "Installation successful - restart required" -ForegroundColor Yellow
            exit 0
        }
        1638 { 
            Write-Host "Newer version already installed" -ForegroundColor Blue
            exit 0
        }
        default { 
            Write-Host "Installation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "Installation error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
