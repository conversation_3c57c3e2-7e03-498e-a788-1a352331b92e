@echo off
echo ===============================================
echo Visual C++ Redistributable Installation Script
echo ===============================================
echo.
echo This script will download and install the required
echo Visual C++ Redistributable packages for VolvoFlashWR.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator: YES
) else (
    echo Running as Administrator: NO
    echo.
    echo WARNING: Some installations may require administrator privileges.
    echo If installation fails, try running this script as Administrator.
    echo.
)

echo.
echo Detecting system architecture...

REM Detect system architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=x64
    echo System Architecture: x64
) else if "%PROCESSOR_ARCHITEW6432%"=="AMD64" (
    set ARCH=x64
    echo System Architecture: x64
) else (
    set ARCH=x86
    echo System Architecture: x86
)

echo.
echo Creating temporary directory...
set TEMP_DIR=%TEMP%\VolvoFlashWR_VCRedist_%RANDOM%
mkdir "%TEMP_DIR%" 2>nul

echo.
echo ===============================================
echo Installing Visual C++ 2015-2022 Redistributable (%ARCH%)
echo ===============================================

REM Set download URLs based on architecture
if "%ARCH%"=="x64" (
    set VCREDIST_URL=https://aka.ms/vs/17/release/vc_redist.x64.exe
    set VCREDIST_FALLBACK=https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
    set VCREDIST_FILE=vc_redist.x64.exe
) else (
    set VCREDIST_URL=https://aka.ms/vs/17/release/vc_redist.x86.exe
    set VCREDIST_FALLBACK=https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x86.exe
    set VCREDIST_FILE=vc_redist.x86.exe
)

set VCREDIST_PATH=%TEMP_DIR%\%VCREDIST_FILE%

echo.
echo Downloading Visual C++ Redistributable...
echo URL: %VCREDIST_URL%
echo.

REM Try to download using PowerShell (more reliable than other methods)
powershell -Command "& {try { Invoke-WebRequest -Uri '%VCREDIST_URL%' -OutFile '%VCREDIST_PATH%' -TimeoutSec 300; Write-Host 'Download completed successfully' } catch { Write-Host 'Primary download failed, trying fallback URL...'; try { Invoke-WebRequest -Uri '%VCREDIST_FALLBACK%' -OutFile '%VCREDIST_PATH%' -TimeoutSec 300; Write-Host 'Fallback download completed successfully' } catch { Write-Host 'All download attempts failed'; exit 1 } }}"

if not exist "%VCREDIST_PATH%" (
    echo.
    echo ERROR: Failed to download Visual C++ Redistributable
    echo.
    echo Manual download instructions:
    echo 1. Open a web browser
    echo 2. Go to: %VCREDIST_URL%
    echo 3. Download and run the installer manually
    echo 4. Choose "Install" and follow the prompts
    echo.
    pause
    goto cleanup
)

echo.
echo Download completed. File size:
dir "%VCREDIST_PATH%" | find "%VCREDIST_FILE%"

echo.
echo Installing Visual C++ Redistributable...
echo This may take a few minutes...
echo.

REM Install the redistributable
"%VCREDIST_PATH%" /quiet /norestart

set INSTALL_RESULT=%ERRORLEVEL%

echo.
echo Installation completed with exit code: %INSTALL_RESULT%

REM Interpret exit codes
if %INSTALL_RESULT%==0 (
    echo Status: Installation successful
) else if %INSTALL_RESULT%==1638 (
    echo Status: Already installed (newer or same version)
) else if %INSTALL_RESULT%==3010 (
    echo Status: Installation successful (reboot required)
    echo.
    echo WARNING: A system reboot is required to complete the installation.
    echo Please restart your computer before running VolvoFlashWR.
) else (
    echo Status: Installation failed (exit code %INSTALL_RESULT%)
    echo.
    echo This may happen if:
    echo - Administrator privileges are required
    echo - A newer version is already installed
    echo - System files are locked by another process
    echo.
    echo Try running this script as Administrator or restart your computer.
)

echo.
echo ===============================================
echo Verifying Installation
echo ===============================================

echo.
echo Checking for required Visual C++ runtime libraries...

REM Check for key libraries in system directories
set FOUND_LIBS=0

if exist "%SystemRoot%\System32\msvcr140.dll" (
    echo [OK] msvcr140.dll found in System32
    set /a FOUND_LIBS+=1
) else (
    echo [MISSING] msvcr140.dll not found in System32
)

if exist "%SystemRoot%\System32\msvcp140.dll" (
    echo [OK] msvcp140.dll found in System32
    set /a FOUND_LIBS+=1
) else (
    echo [MISSING] msvcp140.dll not found in System32
)

if exist "%SystemRoot%\System32\vcruntime140.dll" (
    echo [OK] vcruntime140.dll found in System32
    set /a FOUND_LIBS+=1
) else (
    echo [MISSING] vcruntime140.dll not found in System32
)

echo.
echo Found %FOUND_LIBS% out of 3 key Visual C++ runtime libraries.

if %FOUND_LIBS% GEQ 2 (
    echo.
    echo SUCCESS: Visual C++ runtime installation appears to be successful!
    echo VolvoFlashWR should now be able to load the required libraries.
) else (
    echo.
    echo WARNING: Some Visual C++ runtime libraries are still missing.
    echo This may indicate:
    echo - Installation failed or was incomplete
    echo - Administrator privileges were required
    echo - A system reboot is needed
    echo.
    echo Recommendations:
    echo 1. Restart your computer
    echo 2. Run this script as Administrator
    echo 3. Check Windows Update for system updates
    echo 4. Try downloading and installing manually from Microsoft
)

echo.
echo ===============================================
echo Installation Summary
echo ===============================================
echo.
echo Architecture: %ARCH%
echo Download URL: %VCREDIST_URL%
echo Installation Result: %INSTALL_RESULT%
echo Libraries Found: %FOUND_LIBS%/3
echo.

if %INSTALL_RESULT%==3010 (
    echo IMPORTANT: System reboot required!
    echo Please restart your computer before running VolvoFlashWR.
    echo.
)

:cleanup
echo Cleaning up temporary files...
if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%" 2>nul
)

echo.
echo Installation script completed.
echo.
echo You can now try running VolvoFlashWR using:
echo   Run_Normal_Mode.bat
echo   or
echo   Run_x64_Compatible.bat
echo.
pause
