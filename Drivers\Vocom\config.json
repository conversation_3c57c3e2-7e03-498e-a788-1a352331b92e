{"vocom": {"name": "Vocom 1 Adapter", "version": "1.0", "manufacturer": "Volvo", "supportedProtocols": ["CAN", "J1939", "KWP2000", "UDS"], "connectionTypes": ["USB", "Bluetooth", "WiFi"]}, "hardware": {"usbVendorId": "0x178E", "usbProductId": "0x0024", "bluetoothClass": "0x001F00", "wifiFrequency": "2.4GHz"}, "communication": {"can": {"highSpeed": {"enabled": true, "baudRates": [125000, 250000, 500000, 1000000], "defaultBaudRate": 500000, "terminationResistance": "120ohm"}, "lowSpeed": {"enabled": true, "baudRates": [33300, 83300, 125000], "defaultBaudRate": 125000, "terminationResistance": "none"}}, "j1939": {"enabled": true, "baudRate": 250000, "addressClaiming": true, "transportProtocol": true}, "kwp2000": {"enabled": true, "baudRates": [9600, 10400, 19200, 38400], "defaultBaudRate": 10400, "initializationType": "fast"}, "uds": {"enabled": true, "transportLayer": "CAN", "addressingMode": "normal", "functionalAddressing": true}}, "driver": {"libraryName": "WUDFPuma.dll", "libraryPath": "Libraries", "systemDriverPath": "C:\\Program Files (x86)\\88890020 Adapter\\UMDF\\WUDFPuma.dll", "initializationFunction": "Vocom_Initialize_WUDFPuma", "cleanupFunction": "Vocom_Cleanup_WUDFPuma", "architecture": "x64", "version": "*******", "dependencies": ["WUDFUpdate_01009.dll", "WdfCoInstaller01009.dll", "winusbcoinstaller2.dll"], "criticalLibraries": ["WUDFPuma.dll", "apci.dll", "Volvo.ApciPlus.dll", "Volvo.ApciPlusData.dll"], "optionalLibraries": ["Ionic.Zip.Reduced.dll", "SharpCompress.dll"]}, "phoenix": {"enabled": true, "adapterType": "Vocom1", "libraryPath": "C:\\Program Files (x86)\\Phoenix Diag\\Flash Editor Plus 2021", "configurationFile": "vocom_adapter.xml", "fallbackEnabled": true, "priority": 1, "requiredLibraries": ["Volvo.ApciPlus.dll", "Volvo.ApciPlusData.dll"]}, "connection": {"timeout": {"connectionMs": 5000, "responseMs": 1000, "keepAliveMs": 30000}, "retries": {"connectionAttempts": 3, "messageRetries": 3, "delayBetweenRetriesMs": 500}, "bufferSizes": {"transmitBuffer": 4096, "receiveBuffer": 4096, "maxMessageSize": 4095}}, "diagnostics": {"logging": {"enabled": true, "level": "Debug", "logToFile": true, "logToConsole": false}, "monitoring": {"connectionStatus": true, "messageTraffic": true, "errorCounting": true}, "testing": {"loopbackTest": true, "signalQualityTest": true, "performanceTest": true}}, "security": {"encryption": {"enabled": false, "algorithm": "AES256", "keyExchange": "ECDH"}, "authentication": {"enabled": false, "method": "certificate", "certificatePath": ""}}, "features": {"firmwareUpdate": {"enabled": true, "autoCheck": false, "updateServer": ""}, "calibration": {"enabled": true, "autoCalibration": true, "calibrationFile": "vocom_calibration.dat"}, "passThrough": {"enabled": true, "protocols": ["J2534", "RP1210"]}}, "realHardwareMode": {"enabled": true, "priority": "Phoenix > PatchedDriver > StandardDriver > DummyMode", "fallbackChain": ["PhoenixVocomAdapter", "PatchedVocomDeviceDriver", "VocomDriver", "VocomDeviceDriver", "DummyVocomService"], "environmentVariables": {"PHOENIX_VOCOM_ENABLED": "true", "USE_PATCHED_IMPLEMENTATION": "true", "APCI_LIBRARY_PATH": "Libraries", "VERBOSE_LOGGING": "true", "VOCOM_DRIVER_MODE": "REAL_HARDWARE"}, "libraryVerification": {"enabled": true, "strictMode": false, "logMissingLibraries": true, "fallbackOnMissing": true}, "deviceDetection": {"autoScan": true, "scanInterval": 5000, "maxScanAttempts": 10, "deviceTimeout": 3000, "supportedDevices": ["Vocom 1", "Vocom 2", "Vocom 3"]}, "errorHandling": {"autoRetry": true, "maxRetries": 3, "retryDelay": 2000, "logAllAttempts": true, "gracefulDegradation": true}}}