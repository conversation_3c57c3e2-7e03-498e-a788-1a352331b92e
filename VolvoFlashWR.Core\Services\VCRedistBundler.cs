using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Bundles and manages Visual C++ Redistributable libraries
    /// Ensures all required runtime libraries are available without system installation
    /// </summary>
    public class VCRedistBundler
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly string _vcRedistPath;

        // Windows API imports
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        // Critical Visual C++ Runtime libraries - supporting both x86 and x64
        private static readonly Dictionary<string, VCRedistLibrary> RequiredLibraries = new()
        {
            // Visual C++ 2013 Redistributable (x86)
            ["msvcr120.dll"] = new VCRedistLibrary
            {
                Name = "msvcr120.dll",
                Description = "Microsoft Visual C++ 2013 Runtime (x86)",
                Version = "12.0",
                Architecture = "x86",
                IsRequired = true
            },
            ["msvcp120.dll"] = new VCRedistLibrary
            {
                Name = "msvcp120.dll",
                Description = "Microsoft Visual C++ 2013 C++ Runtime (x86)",
                Version = "12.0",
                Architecture = "x86",
                IsRequired = true
            },

            // Visual C++ 2015-2022 Redistributable (x64) - Primary for x64 process
            ["msvcr140.dll"] = new VCRedistLibrary
            {
                Name = "msvcr140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x64)",
                Version = "14.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["msvcp140.dll"] = new VCRedistLibrary
            {
                Name = "msvcp140.dll",
                Description = "Microsoft Visual C++ 2015-2022 C++ Runtime (x64)",
                Version = "14.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["vcruntime140.dll"] = new VCRedistLibrary
            {
                Name = "vcruntime140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x64)",
                Version = "14.0",
                Architecture = "x64",
                IsRequired = true
            },

            // Universal CRT libraries (x64) - Primary for x64 process
            ["api-ms-win-crt-runtime-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-runtime-l1-1-0.dll",
                Description = "Universal CRT Runtime (x64)",
                Version = "10.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["api-ms-win-crt-heap-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-heap-l1-1-0.dll",
                Description = "Universal CRT Heap (x64)",
                Version = "10.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["api-ms-win-crt-string-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-string-l1-1-0.dll",
                Description = "Universal CRT String (x64)",
                Version = "10.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["api-ms-win-crt-stdio-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-stdio-l1-1-0.dll",
                Description = "Universal CRT Standard I/O (x64)",
                Version = "10.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["api-ms-win-crt-math-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-math-l1-1-0.dll",
                Description = "Universal CRT Math (x64)",
                Version = "10.0",
                Architecture = "x64",
                IsRequired = true
            },
            ["api-ms-win-crt-locale-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-locale-l1-1-0.dll",
                Description = "Universal CRT Locale (x64)",
                Version = "10.0",
                Architecture = "x64",
                IsRequired = true
            }
        };

        public VCRedistBundler(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _vcRedistPath = Path.Combine(_librariesPath, "VCRedist");
        }

        /// <summary>
        /// Bundles all required Visual C++ Redistributable libraries
        /// </summary>
        public async Task<bool> BundleVCRedistLibrariesAsync()
        {
            try
            {
                // Check if VC++ download is disabled via environment variable
                string skipDownload = Environment.GetEnvironmentVariable("SKIP_VCREDIST_DOWNLOAD");
                if (!string.IsNullOrEmpty(skipDownload) && skipDownload.ToLower() == "true")
                {
                    _logger.LogInformation("Skipping VC++ Redistributable bundling due to SKIP_VCREDIST_DOWNLOAD environment variable", "VCRedistBundler");
                    return true; // Return true to indicate success (skip operation)
                }

                _logger.LogInformation("Starting Visual C++ Redistributable bundling with integrated download", "VCRedistBundler");

                // Create VCRedist directory
                if (!Directory.Exists(_vcRedistPath))
                {
                    Directory.CreateDirectory(_vcRedistPath);
                    _logger.LogInformation($"Created VCRedist directory: {_vcRedistPath}", "VCRedistBundler");
                }

                // Priority 1: Copy from pre-bundled Libraries directory
                await CopyPreBundledLibrariesAsync();

                // Priority 2: Copy system libraries
                await CopySystemVCRedistLibrariesAsync();

                // Priority 3: Extract embedded libraries
                await ExtractEmbeddedVCRedistLibrariesAsync();

                // Priority 4: Download missing critical libraries (NEW - integrated download)
                _logger.LogInformation("About to call DownloadMissingVCRedistLibrariesAsync", "VCRedistBundler");
                await DownloadMissingVCRedistLibrariesAsync();
                _logger.LogInformation("Completed DownloadMissingVCRedistLibrariesAsync", "VCRedistBundler");

                // Verify bundling
                bool success = await VerifyVCRedistBundlingAsync();

                _logger.LogInformation($"Visual C++ Redistributable bundling completed. Success: {success}", "VCRedistBundler");
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error bundling Visual C++ Redistributables: {ex.Message}", "VCRedistBundler", ex);
                return false;
            }
        }

        /// <summary>
        /// Copies Visual C++ Redistributable libraries from pre-bundled Libraries directory
        /// </summary>
        private async Task CopyPreBundledLibrariesAsync()
        {
            _logger.LogInformation("Copying pre-bundled Visual C++ Redistributable libraries", "VCRedistBundler");

            // Check for pre-bundled libraries in Libraries directory
            string librariesPath = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "", "Libraries");

            if (!Directory.Exists(librariesPath))
            {
                _logger.LogInformation("Pre-bundled Libraries directory not found, skipping", "VCRedistBundler");
                return;
            }

            foreach (var library in RequiredLibraries.Values)
            {
                string sourcePath = Path.Combine(librariesPath, library.Name);
                string targetPath = Path.Combine(_vcRedistPath, library.Name);

                if (File.Exists(sourcePath) && !File.Exists(targetPath))
                {
                    try
                    {
                        File.Copy(sourcePath, targetPath, true);
                        _logger.LogInformation($"Copied pre-bundled library: {library.Name}", "VCRedistBundler");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to copy pre-bundled library {library.Name}: {ex.Message}", "VCRedistBundler");
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task CopySystemVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Copying system Visual C++ Redistributable libraries", "VCRedistBundler");

            var systemPaths = new[]
            {
                Environment.GetFolderPath(Environment.SpecialFolder.System),
                Environment.GetFolderPath(Environment.SpecialFolder.SystemX86),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "SysWOW64"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Microsoft Visual Studio", "2022", "Redistributable", "MSVC"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Microsoft Visual Studio", "2019", "Redistributable", "MSVC"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Microsoft Visual Studio", "2017", "Redistributable", "MSVC")
            };

            foreach (var library in RequiredLibraries.Values)
            {
                string targetPath = Path.Combine(_vcRedistPath, library.Name);

                if (!File.Exists(targetPath))
                {
                    bool found = false;
                    foreach (string systemPath in systemPaths)
                    {
                        if (!Directory.Exists(systemPath))
                            continue;

                        // Check direct path
                        string sourcePath = Path.Combine(systemPath, library.Name);
                        if (File.Exists(sourcePath))
                        {
                            try
                            {
                                File.Copy(sourcePath, targetPath, true);
                                _logger.LogInformation($"Copied {library.Name} from {sourcePath}", "VCRedistBundler");
                                found = true;
                                break;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to copy {library.Name} from {sourcePath}: {ex.Message}", "VCRedistBundler");
                            }
                        }

                        // Check subdirectories for MSVC redistributables
                        try
                        {
                            var subdirs = Directory.GetDirectories(systemPath, "*", SearchOption.AllDirectories);
                            foreach (string subdir in subdirs)
                            {
                                string subSourcePath = Path.Combine(subdir, library.Name);
                                if (File.Exists(subSourcePath))
                                {
                                    try
                                    {
                                        File.Copy(subSourcePath, targetPath, true);
                                        _logger.LogInformation($"Copied {library.Name} from {subSourcePath}", "VCRedistBundler");
                                        found = true;
                                        break;
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogDebug($"Failed to copy {library.Name} from {subSourcePath}: {ex.Message}", "VCRedistBundler");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug($"Error searching subdirectories in {systemPath}: {ex.Message}", "VCRedistBundler");
                        }

                        if (found) break;
                    }

                    if (!found && library.IsRequired)
                    {
                        _logger.LogWarning($"Required Visual C++ library not found in system: {library.Name}", "VCRedistBundler");
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task ExtractEmbeddedVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Extracting embedded Visual C++ Redistributable libraries", "VCRedistBundler");

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();

            foreach (string resourceName in resourceNames)
            {
                if (resourceName.Contains("vcredist") || resourceName.Contains("msvc"))
                {
                    try
                    {
                        string fileName = Path.GetFileName(resourceName);
                        if (RequiredLibraries.ContainsKey(fileName))
                        {
                            string targetPath = Path.Combine(_vcRedistPath, fileName);

                            if (!File.Exists(targetPath))
                            {
                                using var stream = assembly.GetManifestResourceStream(resourceName);
                                if (stream != null)
                                {
                                    using var fileStream = File.Create(targetPath);
                                    await stream.CopyToAsync(fileStream);
                                    _logger.LogInformation($"Extracted embedded VC++ library: {fileName}", "VCRedistBundler");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to extract embedded VC++ resource {resourceName}: {ex.Message}", "VCRedistBundler");
                    }
                }
            }
        }

        /// <summary>
        /// Downloads missing critical Visual C++ Redistributable libraries
        /// </summary>
        private async Task DownloadMissingVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Checking for missing VC++ libraries to download", "VCRedistBundler");

            var missingLibraries = new List<string>();

            // Check which critical libraries are missing
            foreach (var library in RequiredLibraries)
            {
                string targetPath = Path.Combine(_vcRedistPath, library.Key);
                if (!File.Exists(targetPath))
                {
                    missingLibraries.Add(library.Key);
                }
            }

            if (missingLibraries.Count == 0)
            {
                _logger.LogInformation("All VC++ libraries are available, no download needed", "VCRedistBundler");
                return;
            }

            _logger.LogInformation($"Found {missingLibraries.Count} missing VC++ libraries: {string.Join(", ", missingLibraries)}", "VCRedistBundler");

            // Try to download and extract from Microsoft's redistributable
            if (missingLibraries.Contains("msvcr140.dll") || missingLibraries.Contains("msvcp140.dll") || missingLibraries.Contains("vcruntime140.dll"))
            {
                await DownloadAndExtractVCRedistPackageAsync();
            }

            // Copy any newly extracted libraries to the main application directory for immediate access
            await CopyVCRedistToApplicationDirectoryAsync();
        }

        /// <summary>
        /// Downloads and extracts the Visual C++ Redistributable package
        /// </summary>
        private async Task<bool> DownloadAndExtractVCRedistPackageAsync()
        {
            try
            {
                _logger.LogInformation("Attempting to download Visual C++ Redistributable package", "VCRedistBundler");

                string vcRedistUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe";
                string tempPath = Path.Combine(Path.GetTempPath(), "vc_redist_temp.exe");

                // Download with timeout and retry
                bool downloadSuccess = await DownloadWithRetryAsync(vcRedistUrl, tempPath, maxRetries: 3, timeoutSeconds: 120);

                if (!downloadSuccess)
                {
                    _logger.LogWarning("Failed to download VC++ Redistributable package", "VCRedistBundler");
                    return false;
                }

                // Try to extract the package
                return await ExtractVCRedistPackageAsync(tempPath);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during VC++ Redistributable download: {ex.Message}", "VCRedistBundler");
                return false;
            }
        }

        /// <summary>
        /// Downloads a file with retry logic
        /// </summary>
        private async Task<bool> DownloadWithRetryAsync(string url, string outputPath, int maxRetries = 3, int timeoutSeconds = 60)
        {
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger.LogInformation($"Download attempt {attempt}/{maxRetries} from: {url}", "VCRedistBundler");

                    var response = await httpClient.GetAsync(url);
                    response.EnsureSuccessStatusCode();

                    var content = await response.Content.ReadAsByteArrayAsync();
                    await File.WriteAllBytesAsync(outputPath, content);

                    if (File.Exists(outputPath) && new FileInfo(outputPath).Length > 1000)
                    {
                        _logger.LogInformation($"Download successful! File size: {new FileInfo(outputPath).Length} bytes", "VCRedistBundler");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Download attempt {attempt} failed: {ex.Message}", "VCRedistBundler");

                    if (File.Exists(outputPath))
                    {
                        try { File.Delete(outputPath); } catch { }
                    }
                }

                if (attempt < maxRetries)
                {
                    await Task.Delay(3000); // Wait 3 seconds before retry
                }
            }

            return false;
        }

        /// <summary>
        /// Extracts libraries from the VC++ Redistributable package
        /// </summary>
        private async Task<bool> ExtractVCRedistPackageAsync(string packagePath)
        {
            try
            {
                string extractDir = Path.Combine(Path.GetTempPath(), "vcredist_extract_" + Guid.NewGuid().ToString("N")[..8]);

                if (Directory.Exists(extractDir))
                {
                    Directory.Delete(extractDir, true);
                }
                Directory.CreateDirectory(extractDir);

                _logger.LogInformation($"Extracting VC++ package to: {extractDir}", "VCRedistBundler");

                // Try to extract using the /extract switch
                var processInfo = new ProcessStartInfo
                {
                    FileName = packagePath,
                    Arguments = $"/extract:\"{extractDir}\" /quiet",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    await Task.Delay(2000); // Give time for extraction to complete

                    // Look for extracted DLL files
                    var extractedFiles = Directory.GetFiles(extractDir, "*.dll", SearchOption.AllDirectories);

                    bool foundLibraries = false;
                    foreach (string file in extractedFiles)
                    {
                        string fileName = Path.GetFileName(file);
                        if (RequiredLibraries.ContainsKey(fileName))
                        {
                            string targetPath = Path.Combine(_vcRedistPath, fileName);
                            File.Copy(file, targetPath, true);
                            _logger.LogInformation($"Extracted and copied: {fileName}", "VCRedistBundler");
                            foundLibraries = true;
                        }
                    }

                    // Clean up
                    try { Directory.Delete(extractDir, true); } catch { }
                    try { File.Delete(packagePath); } catch { }

                    return foundLibraries;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during VC++ package extraction: {ex.Message}", "VCRedistBundler");
                return false;
            }
        }

        /// <summary>
        /// Copies VCRedist libraries to the main application directory for immediate loading
        /// </summary>
        private async Task CopyVCRedistToApplicationDirectoryAsync()
        {
            try
            {
                string appDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;

                var vcRedistFiles = Directory.GetFiles(_vcRedistPath, "*.dll");
                foreach (string file in vcRedistFiles)
                {
                    string fileName = Path.GetFileName(file);
                    string targetPath = Path.Combine(appDirectory, fileName);

                    if (!File.Exists(targetPath))
                    {
                        File.Copy(file, targetPath, true);
                        _logger.LogInformation($"Copied {fileName} to application directory for immediate access", "VCRedistBundler");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error copying VCRedist libraries to application directory: {ex.Message}", "VCRedistBundler");
            }

            await Task.CompletedTask;
        }

        private async Task<bool> VerifyVCRedistBundlingAsync()
        {
            _logger.LogInformation("Verifying Visual C++ Redistributable bundling", "VCRedistBundler");

            int foundCount = 0;
            int requiredCount = 0;

            foreach (var library in RequiredLibraries.Values)
            {
                if (library.IsRequired)
                {
                    requiredCount++;
                    string targetPath = Path.Combine(_vcRedistPath, library.Name);

                    if (File.Exists(targetPath))
                    {
                        foundCount++;
                        _logger.LogInformation($"✓ Verified VC++ library: {library.Name}", "VCRedistBundler");

                        // Try to load the library to verify it's valid
                        try
                        {
                            IntPtr handle = LoadLibrary(targetPath);
                            if (handle != IntPtr.Zero)
                            {
                                FreeLibrary(handle);
                                _logger.LogDebug($"Successfully loaded and verified: {library.Name}", "VCRedistBundler");
                            }
                            else
                            {
                                _logger.LogWarning($"Library exists but failed to load: {library.Name}", "VCRedistBundler");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Error verifying library {library.Name}: {ex.Message}", "VCRedistBundler");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"✗ Missing VC++ library: {library.Name}", "VCRedistBundler");
                    }
                }
            }

            double successRate = requiredCount > 0 ? (double)foundCount / requiredCount * 100 : 100;
            _logger.LogInformation($"VC++ Redistributable verification: {foundCount}/{requiredCount} ({successRate:F1}%) required libraries found", "VCRedistBundler");

            await Task.CompletedTask;
            return successRate >= 70; // Consider successful if 70% or more libraries are found
        }

        /// <summary>
        /// Gets the bundling status
        /// </summary>
        public async Task<VCRedistStatus> GetStatusAsync()
        {
            var status = new VCRedistStatus
            {
                VCRedistPath = _vcRedistPath,
                AvailableLibraries = new List<string>(),
                MissingLibraries = new List<string>()
            };

            foreach (var library in RequiredLibraries.Values)
            {
                string targetPath = Path.Combine(_vcRedistPath, library.Name);
                if (File.Exists(targetPath))
                {
                    var fileInfo = new FileInfo(targetPath);
                    status.AvailableLibraries.Add($"{library.Name} ({library.Description}, {fileInfo.Length} bytes)");
                }
                else
                {
                    status.MissingLibraries.Add($"{library.Name} ({library.Description})");
                }
            }

            await Task.CompletedTask;
            return status;
        }
    }

    /// <summary>
    /// Information about a Visual C++ Redistributable library
    /// </summary>
    public class VCRedistLibrary
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Architecture { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
    }

    /// <summary>
    /// Status of Visual C++ Redistributable bundling
    /// </summary>
    public class VCRedistStatus
    {
        public string VCRedistPath { get; set; } = string.Empty;
        public List<string> AvailableLibraries { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
    }
}
