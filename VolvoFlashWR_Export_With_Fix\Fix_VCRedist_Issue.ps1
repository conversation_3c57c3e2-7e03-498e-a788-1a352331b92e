# PowerShell script to fix Visual C++ Redistributable issues
# This script addresses the missing msvcr140.dll and architecture mismatch problems

Write-Host "=== VolvoFlashWR VCRedist Issue Fix ===" -ForegroundColor Green
Write-Host ""

# Function to copy file with error handling
function Copy-FileWithCheck {
    param(
        [string]$Source,
        [string]$Destination,
        [string]$Description
    )
    
    try {
        if (Test-Path $Source) {
            Copy-Item -Path $Source -Destination $Destination -Force
            Write-Host "[OK] Copied $Description" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[SKIP] $Description not found at $Source" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "[ERROR] Failed to copy $Description`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Create necessary directories
$LibrariesDir = ".\Libraries"
$VCRedistDir = ".\Libraries\VCRedist"

if (!(Test-Path $VCRedistDir)) {
    New-Item -ItemType Directory -Path $VCRedistDir -Force | Out-Null
    Write-Host "Created VCRedist directory" -ForegroundColor Yellow
}

Write-Host "=== Step 1: Searching for missing msvcr140.dll ===" -ForegroundColor Cyan

# Search for msvcr140.dll in common locations
$searchPaths = @(
    "$env:SystemRoot\System32",
    "$env:SystemRoot\SysWOW64",
    "$env:ProgramFiles\Microsoft Visual Studio\2022\*\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT",
    "$env:ProgramFiles(x86)\Microsoft Visual Studio\2022\*\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT",
    "$env:ProgramFiles\Microsoft Visual Studio\2019\*\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT",
    "$env:ProgramFiles(x86)\Microsoft Visual Studio\2019\*\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT",
    "$env:TEMP\*\packages\vcredist*\*",
    "$env:USERPROFILE\Downloads"
)

$msvcr140Found = $false

foreach ($searchPath in $searchPaths) {
    if ($searchPath -contains "*") {
        # Handle wildcard paths
        try {
            $basePath = ($searchPath -split '\*')[0]
            if (Test-Path $basePath) {
                $expandedPaths = Get-ChildItem -Path $basePath -Directory -ErrorAction SilentlyContinue | ForEach-Object {
                    $tempPath = $searchPath -replace '\*', $_.Name
                    if (Test-Path $tempPath) { $tempPath }
                }
                
                foreach ($expandedPath in $expandedPaths) {
                    $msvcr140Path = Join-Path $expandedPath "msvcr140.dll"
                    if (Test-Path $msvcr140Path) {
                        Write-Host "Found msvcr140.dll at: $msvcr140Path" -ForegroundColor Green
                        $destPath = Join-Path $VCRedistDir "msvcr140.dll"
                        if (Copy-FileWithCheck -Source $msvcr140Path -Destination $destPath -Description "msvcr140.dll") {
                            $msvcr140Found = $true
                            break
                        }
                    }
                }
            }
        }
        catch {
            # Ignore errors in wildcard expansion
        }
    } else {
        $msvcr140Path = Join-Path $searchPath "msvcr140.dll"
        if (Test-Path $msvcr140Path) {
            Write-Host "Found msvcr140.dll at: $msvcr140Path" -ForegroundColor Green
            $destPath = Join-Path $VCRedistDir "msvcr140.dll"
            if (Copy-FileWithCheck -Source $msvcr140Path -Destination $destPath -Description "msvcr140.dll") {
                $msvcr140Found = $true
                break
            }
        }
    }
    
    if ($msvcr140Found) { break }
}

Write-Host ""
Write-Host "=== Step 2: Copying missing libraries to application directory ===" -ForegroundColor Cyan

# Copy all VCRedist libraries to the main application directory for immediate access
if (Test-Path $VCRedistDir) {
    $vcredistFiles = Get-ChildItem -Path $VCRedistDir -Name "*.dll" -ErrorAction SilentlyContinue

    foreach ($file in $vcredistFiles) {
        $sourcePath = Join-Path $VCRedistDir $file
        $destPath = Join-Path "." $file
        Copy-FileWithCheck -Source $sourcePath -Destination $destPath -Description $file | Out-Null
    }
}

Write-Host ""
Write-Host "=== Step 3: Architecture Bridge Setup ===" -ForegroundColor Cyan

# Check if the Bridge directory exists and is properly configured
if (Test-Path ".\Bridge\VolvoFlashWR.VocomBridge.exe") {
    Write-Host "[OK] Architecture bridge executable found" -ForegroundColor Green
    
    # Ensure bridge has necessary libraries
    $bridgeLibraries = @("apci.dll", "Volvo.ApciPlus.dll", "Volvo.ApciPlusData.dll")
    
    foreach ($lib in $bridgeLibraries) {
        $libPath = ".\Libraries\$lib"
        $bridgePath = ".\Bridge\$lib"
        
        if ((Test-Path $libPath) -and !(Test-Path $bridgePath)) {
            Copy-FileWithCheck -Source $libPath -Destination $bridgePath -Description "$lib to Bridge" | Out-Null
        }
    }
} else {
    Write-Host "[WARNING] Architecture bridge not found - x86 libraries may not work with x64 process" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Step 4: Environment Configuration ===" -ForegroundColor Cyan

# Create a temporary batch file to set up the environment properly
$envBatchContent = @"
@echo off
echo Setting up VolvoFlashWR environment with VCRedist fix...

REM Add current directory to PATH for immediate library access
set PATH=%CD%;%CD%\Libraries;%CD%\Libraries\VCRedist;%PATH%

REM Set architecture bridge environment
set FORCE_ARCHITECTURE_BRIDGE=true
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%CD%\Libraries

echo Environment configured for VCRedist compatibility
echo Starting VolvoFlashWR...
echo.

REM Try to run the application
if exist "VolvoFlashWR.Launcher.exe" (
    "VolvoFlashWR.Launcher.exe"
) else if exist "VolvoFlashWR.UI.exe" (
    "VolvoFlashWR.UI.exe"
) else (
    echo ERROR: No VolvoFlashWR executable found!
    pause
    exit /b 1
)

if errorlevel 1 (
    echo.
    echo Application exited with error. Check the Logs folder for details.
    echo.
    echo If you still see VCRedist errors:
    echo 1. Restart your computer ^(VCRedist installation requires reboot^)
    echo 2. Run Install_VCRedist.bat as Administrator
    echo 3. Check Windows Update for system updates
    echo.
    pause
)
"@

$envBatchPath = ".\Run_With_VCRedist_Fix.bat"
$envBatchContent | Out-File -FilePath $envBatchPath -Encoding ASCII
Write-Host "[OK] Created Run_With_VCRedist_Fix.bat" -ForegroundColor Green

Write-Host ""
Write-Host "Fix script completed!" -ForegroundColor Green
