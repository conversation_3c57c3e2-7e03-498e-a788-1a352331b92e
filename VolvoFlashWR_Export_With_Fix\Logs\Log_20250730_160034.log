Log started at 7/30/2025 4:00:34 PM
2025-07-30 16:00:34.813 [Information] LoggingService: Logging service initialized
2025-07-30 16:00:34.830 [Information] App: Starting integrated application initialization
2025-07-30 16:00:34.832 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-30 16:00:34.835 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-30 16:00:34.837 [Information] IntegratedStartupService: Setting up application environment
2025-07-30 16:00:34.838 [Information] IntegratedStartupService: Application environment setup completed
2025-07-30 16:00:34.842 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-30 16:00:34.846 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-30 16:00:34.848 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-30 16:00:34.854 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-30 16:00:34.871 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.887 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.893 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.894 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-30 16:00:34.897 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.902 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.905 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.907 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:00:34.913 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.916 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.919 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.921 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 16:00:34.926 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.929 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.933 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.934 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 16:00:34.938 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.941 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.944 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.945 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 16:00:34.948 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.953 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.956 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.958 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 16:00:34.963 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 16:00:34.966 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.969 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 16:00:34.970 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 16:00:34.973 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-30 16:00:34.977 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-30 16:00:34.977 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-30 16:00:34.996 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-30 16:00:34.997 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-30 16:00:35.009 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-30 16:00:35.009 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-30 16:00:35.010 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-30 16:00:35.249 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-30 16:00:35.249 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-30 16:00:35.323 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-30 16:00:35.325 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:00:35.325 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 16:00:35.326 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 16:00:35.327 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 16:00:35.327 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 16:00:35.328 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 16:00:35.337 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-30 16:00:35.338 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-30 16:00:35.339 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-30 16:00:35.348 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-30 16:00:35.355 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-30 16:00:35.365 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-30 16:00:35.373 [Information] LibraryExtractor: Starting library extraction process
2025-07-30 16:00:35.384 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-30 16:00:35.388 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-30 16:00:35.393 [Information] LibraryExtractor: Copying system libraries
2025-07-30 16:00:35.417 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-30 16:00:35.450 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-30 16:01:05.473 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 16:01:06.476 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-30 16:01:36.483 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 16:01:36.485 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:02:06.761 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 16:02:07.821 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-30 16:02:37.829 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 16:02:37.831 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 16:03:07.906 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 16:03:08.908 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-30 16:03:38.912 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 16:03:38.917 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-30 16:03:38.917 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-30 16:03:38.923 [Information] LibraryExtractor: Verifying library extraction
2025-07-30 16:03:38.924 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-30 16:03:38.924 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-30 16:03:38.924 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-30 16:03:38.925 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-30 16:03:38.925 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-30 16:03:38.931 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-30 16:03:38.934 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-30 16:03:38.937 [Information] DependencyManager: Initializing dependency manager
2025-07-30 16:03:38.939 [Information] DependencyManager: Setting up library search paths
2025-07-30 16:03:38.941 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 16:03:38.942 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 16:03:38.942 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-30 16:03:38.944 [Information] DependencyManager: Updated PATH environment variable
2025-07-30 16:03:38.947 [Information] DependencyManager: Verifying required directories
2025-07-30 16:03:38.948 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 16:03:38.948 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 16:03:38.949 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-30 16:03:38.949 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-30 16:03:38.951 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-30 16:03:39.017 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-30 16:03:39.042 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-30 16:03:39.044 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-07-30 16:03:39.059 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-30 16:03:39.099 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-30 16:03:39.113 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-30 16:03:39.113 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-07-30 16:03:39.123 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-30 16:03:39.127 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-30 16:03:39.128 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-30 16:03:39.151 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-30 16:03:39.336 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-07-30 16:03:39.364 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-30 16:03:39.375 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-07-30 16:03:39.378 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:03:39.379 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:03:39.381 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 16:03:39.382 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 16:03:39.383 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 16:03:39.384 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 16:03:39.385 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 16:03:39.386 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 16:03:39.387 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 16:03:39.389 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 16:03:39.391 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 16:03:39.392 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 16:03:39.393 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-30 16:03:39.394 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-30 16:03:39.395 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-30 16:03:39.396 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-30 16:03:39.397 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-30 16:03:39.398 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-30 16:03:39.398 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-07-30 16:03:39.399 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-30 16:03:39.400 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-30 16:03:39.500 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-30 16:03:39.728 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 16:03:39.729 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-30 16:03:39.967 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 16:03:39.968 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 16:03:39.971 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-30 16:03:40.128 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-30 16:03:40.129 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-30 16:03:40.287 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-30 16:03:40.290 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-30 16:03:40.291 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-30 16:03:40.862 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-30 16:03:40.864 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-30 16:03:41.396 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-30 16:03:41.397 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-30 16:03:41.404 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-30 16:03:41.631 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-30 16:03:41.631 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-30 16:03:41.894 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-30 16:03:41.894 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-30 16:03:41.896 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-30 16:03:42.286 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-30 16:03:42.318 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-30 16:03:42.320 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-30 16:03:42.364 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-30 16:03:42.365 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-30 16:03:42.365 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-07-30 16:03:42.366 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-30 16:03:42.367 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-30 16:03:42.368 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-30 16:03:42.369 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-07-30 16:03:42.369 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-30 16:03:42.374 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-30 16:03:42.376 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-30 16:03:42.386 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-07-30 16:03:42.394 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-30 16:03:42.399 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-07-30 16:03:42.409 [Information] DependencyManager: Setting up environment variables
2025-07-30 16:03:42.409 [Information] DependencyManager: Environment variables configured
2025-07-30 16:03:42.412 [Information] DependencyManager: Verifying library loading status
2025-07-30 16:03:43.308 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-30 16:03:43.309 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-30 16:03:43.309 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-30 16:03:43.313 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-30 16:03:43.316 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-30 16:03:43.348 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-30 16:03:43.354 [Information] IntegratedStartupService: Verifying system readiness
2025-07-30 16:03:43.356 [Information] IntegratedStartupService: System readiness verification passed
2025-07-30 16:03:43.357 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-30 16:03:43.361 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-30 16:03:43.361 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-30 16:03:43.362 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 16:03:43.363 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 16:03:43.363 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-30 16:03:43.364 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-30 16:03:43.364 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-30 16:03:43.364 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 16:03:43.365 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-30 16:03:43.365 [Information] App: Integrated startup completed successfully
2025-07-30 16:03:43.370 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-30 16:03:43.943 [Information] App: Initializing application services
2025-07-30 16:03:43.946 [Information] AppConfigurationService: Initializing configuration service
2025-07-30 16:03:43.947 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-30 16:03:44.096 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-30 16:03:44.097 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-30 16:03:44.099 [Information] App: Configuration service initialized successfully
2025-07-30 16:03:44.104 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-30 16:03:44.104 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-30 16:03:44.117 [Information] App: Environment variable exists: True, not 'false': False
2025-07-30 16:03:44.117 [Information] App: Final useDummyImplementations value: False
2025-07-30 16:03:44.118 [Information] App: Updating config to NOT use dummy implementations
2025-07-30 16:03:44.122 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-30 16:03:44.168 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-30 16:03:44.171 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-30 16:03:44.182 [Information] App: usePatchedImplementation flag is: True
2025-07-30 16:03:44.183 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-30 16:03:44.184 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-30 16:03:44.184 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-30 16:03:44.185 [Information] App: verboseLogging flag is: True
2025-07-30 16:03:44.191 [Information] App: Verifying real hardware requirements...
2025-07-30 16:03:44.192 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-30 16:03:44.214 [Information] App: ✓ Found critical library: apci.dll
2025-07-30 16:03:44.218 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-30 16:03:44.219 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-30 16:03:44.220 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-30 16:03:44.222 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-30 16:03:44.225 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-30 16:03:44.226 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-30 16:03:44.238 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-30 16:03:44.248 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-30 16:03:44.249 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-30 16:03:44.258 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-30 16:03:44.269 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-30 16:05:46.033 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-30 16:05:46.034 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-30 16:05:46.035 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-30 16:05:46.036 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:05:46.036 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 16:05:46.037 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 16:05:46.039 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 16:05:46.041 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 16:05:46.041 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 16:05:46.042 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-30 16:05:46.044 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-30 16:05:46.045 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-30 16:05:46.045 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-30 16:05:46.046 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-30 16:05:46.046 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-30 16:05:46.049 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-30 16:05:46.050 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-30 16:05:46.051 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-30 16:05:46.056 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-30 16:05:46.056 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-30 16:05:46.060 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-30 16:05:46.061 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-30 16:05:46.062 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-30 16:05:46.063 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-30 16:05:46.067 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-30 16:05:46.068 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-30 16:05:46.073 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-30 16:05:46.075 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-30 16:05:46.077 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-30 16:05:46.078 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-30 16:05:46.349 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-30 16:05:46.353 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-30 16:05:46.360 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-30 16:05:46.361 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-30 16:05:46.363 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-30 16:05:46.364 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-30 16:05:46.369 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-30 16:05:46.369 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-30 16:05:46.370 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-30 16:05:46.370 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:05:46.370 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-30 16:05:46.371 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-30 16:05:46.371 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-30 16:05:46.375 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-30 16:05:46.380 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-30 16:05:46.420 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-30 16:05:46.422 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-30 16:05:46.425 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-30 16:05:46.601 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-30 16:05:46.820 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-30 16:05:46.822 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-30 16:05:46.823 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-30 16:05:46.825 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 16:05:46.826 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-30 16:05:46.826 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 16:05:46.827 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-30 16:05:47.100 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 16:05:47.145 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-30 16:05:47.147 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-30 16:05:47.148 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-30 16:05:47.149 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-30 16:05:47.152 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-30 16:05:47.152 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-30 16:05:47.153 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-30 16:05:47.158 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-30 16:05:47.160 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-30 16:05:47.161 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-30 16:05:47.161 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-30 16:05:47.162 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-30 16:05:47.165 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-30 16:05:47.167 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-30 16:05:47.168 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-30 16:05:47.168 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-30 16:05:47.169 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-30 16:05:47.169 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-30 16:05:47.170 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-30 16:05:47.171 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-30 16:05:47.171 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-30 16:05:47.172 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-30 16:05:47.175 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-30 16:05:47.175 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-30 16:05:47.176 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-30 16:05:47.177 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:05:47.178 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-30 16:05:47.181 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-30 16:05:47.182 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-30 16:05:47.183 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-30 16:05:47.183 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-30 16:05:47.184 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-30 16:05:47.185 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-30 16:05:47.186 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-30 16:05:47.189 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-30 16:05:47.194 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-30 16:05:47.195 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-30 16:05:47.961 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-30 16:05:47.963 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-30 16:05:47.964 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 16:05:47.964 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 16:05:47.987 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-30 16:05:47.990 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:47.990 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:47.991 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-30 16:05:47.992 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:47.993 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:47.995 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-30 16:05:48.504 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:48.504 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-30 16:05:48.768 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:48.770 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-30 16:05:48.772 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:48.773 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:48.773 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-30 16:05:49.022 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:49.249 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:49.250 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-30 16:05:49.446 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 16:05:49.752 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 16:05:50.029 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:50.352 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:50.353 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-30 16:05:50.355 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 16:05:51.164 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:51.164 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-30 16:05:51.429 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:51.620 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:51.621 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-30 16:05:52.176 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 16:05:52.608 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 16:05:52.609 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-30 16:05:52.911 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 16:05:53.285 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 16:05:53.431 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 16:05:53.440 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-30 16:05:53.458 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 16:05:53.469 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-30 16:05:53.478 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-30 16:05:53.481 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-30 16:05:53.488 [Information] VocomDriver: Initializing Vocom driver
2025-07-30 16:05:53.491 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-30 16:05:53.497 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-30 16:05:53.498 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 16:05:53.498 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 16:05:53.500 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 16:05:53.502 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 16:05:53.507 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-30 16:05:53.511 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-30 16:05:53.515 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-30 16:05:53.515 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-07-30 16:05:53.516 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-07-30 16:05:53.518 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 16:05:53.649 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-30 16:05:54.018 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-30 16:05:54.413 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-30 16:05:54.493 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-30 16:05:54.494 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-30 16:05:54.495 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-30 16:05:54.498 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-30 16:05:54.499 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-30 16:05:54.500 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-30 16:05:54.501 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-30 16:05:54.501 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-30 16:05:54.502 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-30 16:05:54.502 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-30 16:05:54.503 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-30 16:05:54.503 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-30 16:05:54.504 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-30 16:05:54.504 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-30 16:05:54.504 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-30 16:05:54.505 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-30 16:05:54.505 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-30 16:05:54.506 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-30 16:05:54.506 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-30 16:05:54.507 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-30 16:05:54.507 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-30 16:05:54.508 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-30 16:05:54.508 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-30 16:05:54.508 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-30 16:05:54.509 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-30 16:05:54.509 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-30 16:05:54.510 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-30 16:05:54.510 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-30 16:05:54.511 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-30 16:05:54.511 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-30 16:05:54.512 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-30 16:05:54.512 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-30 16:05:54.513 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-30 16:05:54.513 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-30 16:05:54.514 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-30 16:05:54.515 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-30 16:05:54.515 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-30 16:05:54.516 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-30 16:05:54.516 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-30 16:05:54.517 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-30 16:05:54.519 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-30 16:05:54.519 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-30 16:05:54.520 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-30 16:05:54.520 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-30 16:05:54.521 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-30 16:05:54.521 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-30 16:05:54.523 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-30 16:05:54.524 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-30 16:05:54.526 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-30 16:05:54.526 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-30 16:05:54.526 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-30 16:05:54.527 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-30 16:05:54.527 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-30 16:05:54.528 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-30 16:05:54.538 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-30 16:05:54.540 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-30 16:05:54.542 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-30 16:05:54.545 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-30 16:05:54.720 [Information] WiFiCommunicationService: WiFi is available
2025-07-30 16:05:54.722 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-30 16:05:54.725 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-30 16:05:54.727 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-30 16:05:54.731 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-30 16:05:54.733 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-30 16:05:54.737 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 16:05:54.740 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 16:05:54.742 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 16:05:54.745 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 16:05:54.754 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 16:05:54.755 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 16:05:54.755 [Information] VocomService: Native USB communication service initialized
2025-07-30 16:05:54.756 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 16:05:54.757 [Information] VocomService: Connection recovery service initialized
2025-07-30 16:05:54.757 [Information] VocomService: Enhanced services initialization completed
2025-07-30 16:05:54.761 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:05:54.796 [Information] VocomService: PTT application is not running
2025-07-30 16:05:54.800 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:05:54.808 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:05:54.811 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 16:05:54.813 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-30 16:05:54.815 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-30 16:05:54.821 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 16:05:54.822 [Information] VocomService: Using new enhanced device detection service
2025-07-30 16:05:54.825 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 16:05:54.828 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 16:05:55.835 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 16:05:55.837 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 16:05:55.839 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 16:05:55.844 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 16:05:55.846 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 16:05:55.848 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 16:05:55.853 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 16:05:55.859 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 16:05:56.736 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 16:05:56.741 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 16:05:56.745 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 16:05:56.746 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 16:05:56.748 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 16:05:56.765 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 16:05:56.766 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 16:05:56.767 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 16:05:56.768 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 16:05:56.768 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:05:56.770 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:05:56.774 [Debug] VocomService: Checking if WiFi is available
2025-07-30 16:05:56.777 [Debug] VocomService: WiFi is available
2025-07-30 16:05:56.778 [Information] VocomService: Found 3 Vocom devices
2025-07-30 16:05:56.779 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-30 16:05:56.782 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: cd6a546b-5e5a-41ae-a384-86b7476e0e8e, Type: USB)
2025-07-30 16:05:56.782 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: e2ca5316-30d9-449f-8865-730109f1059b, Type: Bluetooth)
2025-07-30 16:05:56.783 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 896c58f7-7a58-459a-8163-9e818de0dba1, Type: WiFi)
2025-07-30 16:05:56.784 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-30 16:05:56.785 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-30 16:05:56.786 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-30 16:05:56.786 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-30 16:05:56.787 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-30 16:05:56.787 [Information] App: Architecture-aware Vocom service created successfully
2025-07-30 16:05:56.787 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 16:05:56.788 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 16:05:56.788 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 16:05:56.788 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 16:05:56.790 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 16:05:56.790 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 16:05:56.791 [Information] VocomService: Native USB communication service initialized
2025-07-30 16:05:56.791 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 16:05:56.791 [Information] VocomService: Connection recovery service initialized
2025-07-30 16:05:56.792 [Information] VocomService: Enhanced services initialization completed
2025-07-30 16:05:56.792 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:05:56.817 [Information] VocomService: PTT application is not running
2025-07-30 16:05:56.818 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:05:56.819 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:05:56.820 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 16:05:56.821 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-30 16:05:56.821 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-30 16:05:56.822 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-30 16:05:56.894 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 16:05:56.895 [Information] VocomService: Using new enhanced device detection service
2025-07-30 16:05:56.895 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 16:05:56.896 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 16:05:57.350 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 16:05:57.352 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 16:05:57.352 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 16:05:57.353 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 16:05:57.353 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 16:05:57.354 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 16:05:57.354 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 16:05:57.355 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 16:05:57.846 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 16:05:57.847 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 16:05:57.847 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 16:05:57.848 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 16:05:57.849 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 16:05:57.861 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 16:05:57.862 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 16:05:57.862 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 16:05:57.863 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 16:05:57.863 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:05:57.864 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:05:57.865 [Debug] VocomService: Checking if WiFi is available
2025-07-30 16:05:57.865 [Debug] VocomService: WiFi is available
2025-07-30 16:05:57.866 [Information] VocomService: Found 3 Vocom devices
2025-07-30 16:05:57.866 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-30 16:05:57.870 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 16:05:57.871 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:05:57.896 [Information] VocomService: PTT application is not running
2025-07-30 16:05:57.903 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 16:05:57.904 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 16:05:57.904 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:05:57.929 [Information] VocomService: PTT application is not running
2025-07-30 16:05:57.938 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 16:05:57.948 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 16:05:57.951 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:05:57.955 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:05:57.958 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:57.960 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:57.961 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:57.961 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:57.962 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:57.962 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:57.963 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:57.963 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:57.964 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:57.964 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:05:57.965 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:57.965 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:57.966 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:57.966 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:57.966 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:57.967 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:05:57.969 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:05:57.969 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:05:57.970 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:05:57.970 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:05:57.971 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:05:57.972 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:05:57.972 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 16:05:58.973 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 16:05:58.973 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:05:58.974 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:05:58.975 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:58.975 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:58.975 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:58.976 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:58.976 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:58.977 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:58.977 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:58.977 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:58.978 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:58.978 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:05:58.978 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:58.979 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:58.979 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:58.979 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:58.980 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:58.980 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:05:58.981 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:05:58.981 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:05:58.982 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:05:58.982 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:05:58.982 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:05:58.982 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:05:58.983 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 16:05:59.983 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 16:05:59.984 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:05:59.984 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:05:59.985 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:59.986 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:59.986 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:59.986 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:59.987 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:59.987 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:59.988 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:59.988 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:59.988 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:59.989 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:05:59.990 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:59.990 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:59.991 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:05:59.991 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:05:59.991 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:05:59.992 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:05:59.992 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:05:59.992 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:05:59.992 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:05:59.993 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:05:59.994 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:05:59.994 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:05:59.994 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 16:05:59.995 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 16:05:59.995 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 16:06:00.443 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 16:06:00.446 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 16:06:00.448 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 16:06:00.449 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 16:06:00.450 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 16:06:00.451 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 16:06:00.452 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 16:06:00.457 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 16:06:00.458 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 16:06:00.464 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 16:06:00.468 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 16:06:00.468 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 16:06:00.468 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 16:06:00.469 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:06:00.470 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:06:00.471 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-30 16:06:00.478 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-30 16:06:00.485 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:00.486 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-30 16:06:00.504 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-30 16:06:00.508 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-30 16:06:00.508 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-30 16:06:00.511 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 16:06:00.512 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-30 16:06:00.512 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 16:06:00.513 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-30 16:06:00.513 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-30 16:06:00.518 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-30 16:06:00.519 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 16:06:00.519 [Information] VocomService: Using new enhanced device detection service
2025-07-30 16:06:00.519 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 16:06:00.520 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 16:06:01.093 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 16:06:01.094 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 16:06:01.094 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 16:06:01.095 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 16:06:01.095 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 16:06:01.096 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 16:06:01.097 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 16:06:01.097 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 16:06:01.400 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 16:06:01.401 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 16:06:01.402 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 16:06:01.402 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 16:06:01.403 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 16:06:01.412 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 16:06:01.413 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 16:06:01.413 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 16:06:01.413 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 16:06:01.414 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:06:01.416 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:06:01.418 [Debug] VocomService: Checking if WiFi is available
2025-07-30 16:06:01.418 [Debug] VocomService: WiFi is available
2025-07-30 16:06:01.419 [Information] VocomService: Found 3 Vocom devices
2025-07-30 16:06:01.420 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:06:01.420 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 16:06:01.421 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:06:01.444 [Information] VocomService: PTT application is not running
2025-07-30 16:06:01.444 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 16:06:01.445 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 16:06:01.445 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:06:01.464 [Information] VocomService: PTT application is not running
2025-07-30 16:06:01.465 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 16:06:01.465 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 16:06:01.466 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:06:01.466 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:06:01.467 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:01.467 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:01.468 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:01.468 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:01.468 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:01.469 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:01.469 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:01.470 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:01.470 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:01.471 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:06:01.471 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:01.472 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:01.472 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:01.473 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:01.473 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:01.474 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:06:01.474 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:06:01.475 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:06:01.475 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:06:01.475 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:06:01.476 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:06:01.476 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:06:01.476 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 16:06:02.477 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 16:06:02.477 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:06:02.478 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:06:02.479 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:02.479 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:02.479 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:02.480 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:02.480 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:02.481 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:02.481 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:02.481 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:02.482 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:02.482 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:06:02.482 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:02.482 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:02.483 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:02.483 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:02.483 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:02.484 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:06:02.484 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:06:02.484 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:06:02.485 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:06:02.485 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:06:02.485 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:06:02.486 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:06:02.486 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 16:06:03.499 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 16:06:03.500 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:06:03.500 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:06:03.501 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:03.501 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:03.502 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:03.502 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:03.503 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:03.505 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:03.505 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:03.505 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:03.506 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:03.514 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:06:03.515 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:03.517 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:03.519 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:06:03.520 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:06:03.521 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:06:03.522 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:06:03.522 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:06:03.523 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:06:03.523 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:06:03.523 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:06:03.524 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:06:03.524 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:06:03.525 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 16:06:03.525 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 16:06:03.526 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 16:06:03.527 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 16:06:03.527 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 16:06:03.528 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 16:06:03.528 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 16:06:03.529 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 16:06:03.529 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 16:06:03.529 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 16:06:03.530 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 16:06:03.530 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 16:06:03.531 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 16:06:03.531 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 16:06:03.531 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 16:06:03.532 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 16:06:03.532 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:06:03.533 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:06:03.534 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:06:03.534 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:06:03.535 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:06:03.535 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-30 16:06:03.536 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-30 16:06:03.536 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:06:03.560 [Information] VocomService: PTT application is not running
2025-07-30 16:06:03.563 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-30 16:06:03.563 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:06:03.564 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:06:03.567 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-30 16:06:04.376 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-30 16:06:04.376 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-30 16:06:04.377 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-30 16:06:04.378 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-30 16:06:04.382 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-30 16:06:04.384 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-30 16:06:04.408 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-30 16:06:04.411 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-30 16:06:04.438 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-30 16:06:04.450 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-30 16:06:04.455 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-30 16:06:04.467 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 16:06:04.470 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-30 16:06:04.471 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-30 16:06:04.472 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-30 16:06:04.472 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-30 16:06:04.472 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-30 16:06:04.473 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-30 16:06:04.473 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-30 16:06:04.474 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-30 16:06:04.474 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-30 16:06:04.474 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-30 16:06:04.475 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-30 16:06:04.475 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-30 16:06:04.476 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-30 16:06:04.476 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-30 16:06:04.476 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-30 16:06:04.477 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-30 16:06:04.482 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-30 16:06:04.489 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-30 16:06:04.490 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-30 16:06:04.496 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-30 16:06:04.499 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:04.507 [Information] CANRegisterAccess: Read value 0x77 from register 0x0141 (simulated)
2025-07-30 16:06:04.509 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-30 16:06:04.510 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-30 16:06:04.510 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-30 16:06:04.517 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-30 16:06:04.518 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-30 16:06:04.524 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-30 16:06:04.524 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-30 16:06:04.525 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-30 16:06:04.531 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-30 16:06:04.531 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-30 16:06:04.532 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-30 16:06:04.538 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-30 16:06:04.538 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-30 16:06:04.545 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-30 16:06:04.545 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-30 16:06:04.552 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-30 16:06:04.552 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-30 16:06:04.558 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-30 16:06:04.558 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-30 16:06:04.564 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-30 16:06:04.564 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-30 16:06:04.571 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-30 16:06:04.571 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-30 16:06:04.578 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-30 16:06:04.578 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-30 16:06:04.585 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-30 16:06:04.585 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-30 16:06:04.591 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-30 16:06:04.591 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-30 16:06:04.597 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-30 16:06:04.597 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-30 16:06:04.604 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-30 16:06:04.604 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-30 16:06:04.611 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-30 16:06:04.611 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-30 16:06:04.618 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-30 16:06:04.618 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-30 16:06:04.625 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-30 16:06:04.625 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-30 16:06:04.633 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-30 16:06:04.634 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-30 16:06:04.641 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-30 16:06:04.641 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-30 16:06:04.649 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-30 16:06:04.649 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-30 16:06:04.650 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-30 16:06:04.656 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-30 16:06:04.656 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-30 16:06:04.657 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-30 16:06:04.657 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:04.663 [Information] CANRegisterAccess: Read value 0xF1 from register 0x0141 (simulated)
2025-07-30 16:06:04.669 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:04.676 [Information] CANRegisterAccess: Read value 0x50 from register 0x0141 (simulated)
2025-07-30 16:06:04.676 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-30 16:06:04.678 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-30 16:06:04.679 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-30 16:06:04.679 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 16:06:04.686 [Information] CANRegisterAccess: Read value 0x89 from register 0x0140 (simulated)
2025-07-30 16:06:04.692 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 16:06:04.698 [Information] CANRegisterAccess: Read value 0x10 from register 0x0140 (simulated)
2025-07-30 16:06:04.698 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-30 16:06:04.699 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 16:06:04.704 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-30 16:06:04.705 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-30 16:06:04.716 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-30 16:06:04.717 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-30 16:06:04.718 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-30 16:06:04.727 [Information] VocomService: Sending data and waiting for response
2025-07-30 16:06:04.727 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-30 16:06:04.781 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-30 16:06:04.783 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-30 16:06:04.784 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-30 16:06:04.787 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-30 16:06:04.788 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-30 16:06:04.799 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 16:06:04.800 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-30 16:06:04.801 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-30 16:06:04.812 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-30 16:06:04.824 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-30 16:06:04.835 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-30 16:06:04.846 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-30 16:06:04.858 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 16:06:04.861 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-30 16:06:04.862 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-30 16:06:04.874 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 16:06:04.875 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-30 16:06:04.876 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-30 16:06:04.888 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-30 16:06:04.901 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-30 16:06:04.912 [Information] IICProtocolHandler: Enabling IIC module
2025-07-30 16:06:04.924 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-30 16:06:04.935 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-30 16:06:04.946 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 16:06:04.950 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-30 16:06:04.950 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-30 16:06:04.962 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 16:06:04.965 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-30 16:06:04.965 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-30 16:06:04.966 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-30 16:06:04.966 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-30 16:06:04.967 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-30 16:06:04.967 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-30 16:06:04.967 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-30 16:06:04.968 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-30 16:06:04.968 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-30 16:06:04.968 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-30 16:06:04.969 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-30 16:06:04.969 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-30 16:06:04.970 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-30 16:06:04.970 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-30 16:06:04.970 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-30 16:06:04.971 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-30 16:06:05.072 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 16:06:05.072 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-30 16:06:05.078 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-30 16:06:05.080 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:05.081 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-30 16:06:05.081 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-30 16:06:05.082 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:05.083 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-30 16:06:05.083 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-30 16:06:05.084 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:05.084 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-30 16:06:05.085 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-30 16:06:05.086 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:05.086 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-30 16:06:05.087 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-30 16:06:05.089 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-30 16:06:05.090 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-30 16:06:05.091 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-30 16:06:05.098 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-30 16:06:05.100 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-30 16:06:05.107 [Information] BackupService: Initializing backup service
2025-07-30 16:06:05.107 [Information] BackupService: Backup service initialized successfully
2025-07-30 16:06:05.107 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-30 16:06:05.108 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-30 16:06:05.112 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-30 16:06:05.181 [Information] BackupService: Compressing backup data
2025-07-30 16:06:05.199 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-30 16:06:05.201 [Information] BackupServiceFactory: Created template for category: Production
2025-07-30 16:06:05.202 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-30 16:06:05.202 [Information] BackupService: Compressing backup data
2025-07-30 16:06:05.205 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-30 16:06:05.205 [Information] BackupServiceFactory: Created template for category: Development
2025-07-30 16:06:05.206 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-30 16:06:05.208 [Information] BackupService: Compressing backup data
2025-07-30 16:06:05.210 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-07-30 16:06:05.210 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-30 16:06:05.211 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-30 16:06:05.212 [Information] BackupService: Compressing backup data
2025-07-30 16:06:05.213 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-30 16:06:05.214 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-30 16:06:05.214 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-30 16:06:05.215 [Information] BackupService: Compressing backup data
2025-07-30 16:06:05.216 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (452 bytes)
2025-07-30 16:06:05.217 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-30 16:06:05.218 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-30 16:06:05.218 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-30 16:06:05.219 [Information] BackupService: Compressing backup data
2025-07-30 16:06:05.222 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-07-30 16:06:05.222 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-30 16:06:05.224 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-30 16:06:05.226 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-30 16:06:05.231 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-30 16:06:05.235 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-30 16:06:05.378 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-30 16:06:05.379 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-30 16:06:05.382 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-30 16:06:05.382 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-30 16:06:05.383 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-30 16:06:05.386 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-30 16:06:05.387 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-30 16:06:05.394 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-30 16:06:05.394 [Information] App: Flash operation monitor service initialized successfully
2025-07-30 16:06:05.413 [Information] LicensingService: Initializing licensing service
2025-07-30 16:06:05.550 [Information] LicensingService: License information loaded successfully
2025-07-30 16:06:05.555 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-30 16:06:05.558 [Information] App: Licensing service initialized successfully
2025-07-30 16:06:05.559 [Information] App: License status: Trial
2025-07-30 16:06:05.560 [Information] App: Trial period: 27 days remaining
2025-07-30 16:06:05.565 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-30 16:06:05.620 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-30 16:06:05.843 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 16:06:05.844 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 16:06:05.844 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 16:06:05.845 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 16:06:05.845 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 16:06:05.846 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 16:06:05.846 [Information] VocomService: Native USB communication service initialized
2025-07-30 16:06:05.847 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 16:06:05.847 [Information] VocomService: Connection recovery service initialized
2025-07-30 16:06:05.847 [Information] VocomService: Enhanced services initialization completed
2025-07-30 16:06:05.848 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:06:05.869 [Information] VocomService: PTT application is not running
2025-07-30 16:06:05.870 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:06:05.871 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:06:05.872 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 16:06:05.923 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-30 16:06:05.924 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-30 16:06:05.924 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-30 16:06:05.925 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 16:06:05.925 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-30 16:06:05.925 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 5fd6f85b-256f-40b6-9d1c-97e0b7d657d1
2025-07-30 16:06:05.928 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-30 16:06:05.928 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 16:06:05.929 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-30 16:06:05.929 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-30 16:06:05.932 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-30 16:06:05.933 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-30 16:06:05.935 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-30 16:06:05.935 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-30 16:06:05.936 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-30 16:06:05.948 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 16:06:05.948 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-30 16:06:05.949 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-30 16:06:05.949 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-30 16:06:05.950 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-30 16:06:05.950 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-30 16:06:05.950 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-30 16:06:05.951 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-30 16:06:05.951 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-30 16:06:05.952 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-30 16:06:05.952 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-30 16:06:05.953 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-30 16:06:05.953 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-30 16:06:05.953 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-30 16:06:05.954 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-30 16:06:05.954 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-30 16:06:05.955 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-30 16:06:05.955 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-30 16:06:05.961 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-30 16:06:05.962 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-30 16:06:05.962 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-30 16:06:05.962 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:05.968 [Information] CANRegisterAccess: Read value 0x99 from register 0x0141 (simulated)
2025-07-30 16:06:05.969 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-30 16:06:05.969 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-30 16:06:05.970 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-30 16:06:05.975 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-30 16:06:05.976 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-30 16:06:05.982 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-30 16:06:05.982 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-30 16:06:05.983 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-30 16:06:05.989 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-30 16:06:05.989 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-30 16:06:05.990 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-30 16:06:05.996 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-30 16:06:05.996 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-30 16:06:06.003 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-30 16:06:06.003 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-30 16:06:06.010 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-30 16:06:06.010 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-30 16:06:06.017 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-30 16:06:06.017 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-30 16:06:06.024 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-30 16:06:06.024 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-30 16:06:06.031 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-30 16:06:06.031 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-30 16:06:06.038 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-30 16:06:06.038 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-30 16:06:06.045 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-30 16:06:06.045 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-30 16:06:06.052 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-30 16:06:06.052 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-30 16:06:06.059 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-30 16:06:06.059 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-30 16:06:06.066 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-30 16:06:06.066 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-30 16:06:06.073 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-30 16:06:06.074 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-30 16:06:06.081 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-30 16:06:06.081 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-30 16:06:06.088 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-30 16:06:06.088 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-30 16:06:06.095 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-30 16:06:06.095 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-30 16:06:06.102 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-30 16:06:06.103 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-30 16:06:06.109 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-30 16:06:06.109 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-30 16:06:06.110 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-30 16:06:06.116 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-30 16:06:06.116 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-30 16:06:06.117 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-30 16:06:06.117 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:06.124 [Information] CANRegisterAccess: Read value 0x3F from register 0x0141 (simulated)
2025-07-30 16:06:06.130 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:06.136 [Information] CANRegisterAccess: Read value 0x8F from register 0x0141 (simulated)
2025-07-30 16:06:06.142 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 16:06:06.148 [Information] CANRegisterAccess: Read value 0xE4 from register 0x0141 (simulated)
2025-07-30 16:06:06.148 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-30 16:06:06.149 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-30 16:06:06.149 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-30 16:06:06.150 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 16:06:06.156 [Information] CANRegisterAccess: Read value 0xBA from register 0x0140 (simulated)
2025-07-30 16:06:06.156 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-30 16:06:06.157 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 16:06:06.157 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-30 16:06:06.158 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-30 16:06:06.169 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-30 16:06:06.169 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-30 16:06:06.170 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-30 16:06:06.170 [Information] VocomService: Sending data and waiting for response
2025-07-30 16:06:06.171 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-30 16:06:06.222 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-30 16:06:06.223 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-30 16:06:06.223 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-30 16:06:06.224 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-30 16:06:06.224 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-30 16:06:06.236 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 16:06:06.236 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-30 16:06:06.237 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-30 16:06:06.248 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-30 16:06:06.259 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-30 16:06:06.269 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-30 16:06:06.279 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-30 16:06:06.290 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 16:06:06.290 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-30 16:06:06.291 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-30 16:06:06.302 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 16:06:06.302 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-30 16:06:06.303 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-30 16:06:06.314 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-30 16:06:06.325 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-30 16:06:06.336 [Information] IICProtocolHandler: Enabling IIC module
2025-07-30 16:06:06.347 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-30 16:06:06.357 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-30 16:06:06.368 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 16:06:06.369 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-30 16:06:06.370 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-30 16:06:06.380 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 16:06:06.381 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-30 16:06:06.382 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-30 16:06:06.382 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-30 16:06:06.382 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-30 16:06:06.383 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-30 16:06:06.383 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-30 16:06:06.384 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-30 16:06:06.384 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-30 16:06:06.384 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-30 16:06:06.385 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-30 16:06:06.385 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-30 16:06:06.386 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-30 16:06:06.386 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-30 16:06:06.386 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-30 16:06:06.387 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-30 16:06:06.388 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-30 16:06:06.488 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 16:06:06.488 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-30 16:06:06.489 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-30 16:06:06.489 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:06.490 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-30 16:06:06.490 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-30 16:06:06.491 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:06.491 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-30 16:06:06.492 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-30 16:06:06.492 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:06.493 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-30 16:06:06.493 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-30 16:06:06.494 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 16:06:06.494 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-30 16:06:06.495 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-30 16:06:06.546 [Information] BackupService: Initializing backup service
2025-07-30 16:06:06.547 [Information] BackupService: Backup service initialized successfully
2025-07-30 16:06:06.598 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-30 16:06:06.599 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-30 16:06:06.602 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-30 16:06:06.602 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-30 16:06:06.656 [Information] BackupService: Getting predefined backup categories
2025-07-30 16:06:06.708 [Information] MainViewModel: Services initialized successfully
2025-07-30 16:06:06.713 [Information] MainViewModel: Scanning for Vocom devices
2025-07-30 16:06:06.715 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 16:06:06.716 [Information] VocomService: Using new enhanced device detection service
2025-07-30 16:06:06.716 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 16:06:06.717 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 16:06:07.224 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 16:06:07.225 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 16:06:07.225 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 16:06:07.226 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 16:06:07.226 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 16:06:07.227 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 16:06:07.227 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 16:06:07.228 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 16:06:07.648 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 16:06:07.648 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 16:06:07.649 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 16:06:07.650 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 16:06:07.651 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 16:06:07.661 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 16:06:07.661 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 16:06:07.662 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 16:06:07.662 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 16:06:07.663 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 16:06:07.666 [Debug] VocomService: Bluetooth is enabled
2025-07-30 16:06:07.666 [Debug] VocomService: Checking if WiFi is available
2025-07-30 16:06:07.681 [Debug] VocomService: WiFi is available
2025-07-30 16:06:07.681 [Information] VocomService: Found 3 Vocom devices
2025-07-30 16:06:07.683 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-30 16:07:55.192 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-30 16:07:55.193 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:55.196 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-30 16:07:55.197 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-30 16:07:55.599 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-30 16:07:55.600 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-30 16:07:55.601 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-30 16:07:55.604 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-30 16:07:55.605 [Information] ECUCommunicationService: No ECUs are connected
2025-07-30 16:07:55.605 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-30 16:07:55.606 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-30 16:07:55.606 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-30 16:07:55.606 [Information] ECUCommunicationService: No ECUs are connected
2025-07-30 16:07:55.607 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:07:55.623 [Information] VocomService: PTT application is not running
2025-07-30 16:07:55.623 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 16:07:55.624 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 16:07:55.624 [Information] VocomService: Checking if PTT application is running
2025-07-30 16:07:55.638 [Information] VocomService: PTT application is not running
2025-07-30 16:07:55.638 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 16:07:55.639 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 16:07:55.639 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:07:55.639 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:07:55.640 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:55.640 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:55.641 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:55.641 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:55.641 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:55.641 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:55.642 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:55.642 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:55.642 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:55.643 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:07:55.643 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:55.643 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:55.643 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:55.644 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:55.644 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:55.644 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:07:55.645 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:07:55.645 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:07:55.645 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:07:55.646 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:07:55.646 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:07:55.646 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:07:55.647 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 16:07:56.648 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 16:07:56.648 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:07:56.648 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:07:56.649 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:56.650 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:56.650 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:56.650 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:56.651 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:56.651 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:56.651 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:56.651 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:56.652 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:56.652 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:07:56.652 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:56.653 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:56.653 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:56.653 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:56.653 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:56.654 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:07:56.654 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:07:56.654 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:07:56.655 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:07:56.655 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:07:56.655 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:07:56.655 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:07:56.656 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 16:07:57.655 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 16:07:57.656 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 16:07:57.656 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 16:07:57.657 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:57.657 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:57.657 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:57.658 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:57.658 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:57.658 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:57.659 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:57.659 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:57.659 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:57.660 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 16:07:57.660 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:57.660 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:57.660 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 16:07:57.661 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 16:07:57.661 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 16:07:57.661 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 16:07:57.662 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:07:57.662 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 16:07:57.662 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 16:07:57.663 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 16:07:57.663 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 16:07:57.663 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 16:07:57.663 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 16:07:57.664 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 16:07:57.664 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 16:07:57.664 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 16:07:57.665 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 16:07:57.665 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 16:07:57.665 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 16:07:57.665 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 16:07:57.665 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 16:07:57.666 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 16:07:57.666 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 16:07:57.666 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 16:07:57.667 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 16:07:57.667 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 16:07:57.667 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 16:07:57.668 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 16:07:57.668 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:07:57.668 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:07:57.669 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:07:57.669 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:07:57.670 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:07:57.670 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 16:07:57.670 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:57.671 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:57.671 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:57.671 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:57.672 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:57.672 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 16:07:57.673 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
