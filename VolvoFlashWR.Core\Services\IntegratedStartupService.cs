using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Integrated startup service that handles all dependencies, libraries, and tools
    /// automatically without requiring external scripts or manual installation
    /// </summary>
    public class IntegratedStartupService
    {
        private readonly ILoggingService _logger;
        private readonly DependencyManager _dependencyManager;
        private readonly LibraryExtractor _libraryExtractor;
        private readonly VCRedistBundler _vcRedistBundler;
        private readonly X64LibraryResolver _x64LibraryResolver;
        private readonly string _applicationPath;
        private bool _isInitialized = false;

        public IntegratedStartupService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _dependencyManager = new DependencyManager(_logger);
            _libraryExtractor = new LibraryExtractor(_logger);
            _vcRedistBundler = new VCRedistBundler(_logger);
            _x64LibraryResolver = new X64LibraryResolver(_logger);
        }

        /// <summary>
        /// Initializes the application with all required dependencies and tools
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized)
                return true;

            try
            {
                _logger.LogInformation("=== Starting Integrated Application Initialization ===", "IntegratedStartupService");

                // Step 1: Setup application environment
                await SetupApplicationEnvironmentAsync();

                // Step 2: Resolve x64 architecture library compatibility
                await ResolveX64LibraryCompatibilityAsync();

                // Step 3: Bundle Visual C++ Redistributables (with integrated download)
                await BundleVCRedistLibrariesAsync();

                // Step 3.5: Initialize Architecture Bridge if needed
                await InitializeArchitectureBridgeIfNeededAsync();

                // Step 4: Extract and verify libraries
                await ExtractAndVerifyLibrariesAsync();

                // Step 5: Initialize dependency manager
                await InitializeDependencyManagerAsync();

                // Step 6: Setup Vocom-specific environment
                await SetupVocomEnvironmentAsync();

                // Step 7: Verify system readiness
                bool isReady = await VerifySystemReadinessAsync();

                if (isReady)
                {
                    _isInitialized = true;
                    _logger.LogInformation("=== Integrated Application Initialization Complete ===", "IntegratedStartupService");
                    await LogSystemStatusAsync();
                }
                else
                {
                    _logger.LogWarning("System initialization completed with warnings - some functionality may be limited", "IntegratedStartupService");
                }

                return isReady;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to initialize integrated startup service: {ex.Message}", "IntegratedStartupService", ex);
                return false;
            }
        }

        private async Task SetupApplicationEnvironmentAsync()
        {
            _logger.LogInformation("Setting up application environment", "IntegratedStartupService");

            try
            {
                // Create required directories
                var requiredDirectories = new[]
                {
                    Path.Combine(_applicationPath, "Libraries"),
                    Path.Combine(_applicationPath, "Drivers", "Vocom"),
                    Path.Combine(_applicationPath, "Config"),
                    Path.Combine(_applicationPath, "Logs"),
                    Path.Combine(_applicationPath, "Backups"),
                    Path.Combine(_applicationPath, "Temp")
                };

                foreach (string directory in requiredDirectories)
                {
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                        _logger.LogInformation($"Created directory: {directory}", "IntegratedStartupService");
                    }
                }

                // Set up basic environment variables
                Environment.SetEnvironmentVariable("VOLVOFLASHWR_HOME", _applicationPath);
                Environment.SetEnvironmentVariable("VOLVOFLASHWR_LIBRARIES", Path.Combine(_applicationPath, "Libraries"));
                Environment.SetEnvironmentVariable("VOLVOFLASHWR_DRIVERS", Path.Combine(_applicationPath, "Drivers"));

                _logger.LogInformation("Application environment setup completed", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error setting up application environment: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }

            await Task.CompletedTask;
        }

        private async Task ResolveX64LibraryCompatibilityAsync()
        {
            _logger.LogInformation("Resolving x64 architecture library compatibility", "IntegratedStartupService");

            try
            {
                // Use the X64LibraryResolver to handle architecture compatibility
                var resolutionResult = await _x64LibraryResolver.ResolveLibrariesAsync();

                if (resolutionResult.IsSuccessful)
                {
                    _logger.LogInformation("x64 library resolution completed successfully", "IntegratedStartupService");
                }
                else
                {
                    _logger.LogWarning($"x64 library resolution completed with issues: {resolutionResult.ErrorMessage}", "IntegratedStartupService");
                }

                // Log resolution details
                _logger.LogInformation($"Resolved libraries: {resolutionResult.ResolvedLibraries.Count}", "IntegratedStartupService");
                _logger.LogInformation($"Missing libraries: {resolutionResult.MissingLibraries.Count}", "IntegratedStartupService");
                _logger.LogInformation($"Compatible libraries: {resolutionResult.CompatibleLibraries.Count}", "IntegratedStartupService");
                _logger.LogInformation($"Incompatible libraries: {resolutionResult.IncompatibleLibraries.Count}", "IntegratedStartupService");

                if (resolutionResult.RequiresArchitectureBridge)
                {
                    _logger.LogInformation($"Architecture bridge required: {resolutionResult.ArchitectureBridgeAvailable}", "IntegratedStartupService");
                    if (resolutionResult.ArchitectureBridgeAvailable)
                    {
                        _logger.LogInformation($"Bridge path: {resolutionResult.BridgePath}", "IntegratedStartupService");
                    }
                }

                // Log environment variables set by the resolver
                foreach (var envVar in resolutionResult.EnvironmentVariables)
                {
                    _logger.LogInformation($"Environment variable set: {envVar.Key} = {envVar.Value}", "IntegratedStartupService");
                }

                // Log any recommendations
                foreach (var recommendation in resolutionResult.Recommendations)
                {
                    _logger.LogInformation($"Recommendation: {recommendation}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during x64 library compatibility resolution: {ex.Message}", "IntegratedStartupService", ex);
                // Don't throw - continue with other initialization steps
            }
        }

        private async Task BundleVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Bundling Visual C++ Redistributable libraries", "IntegratedStartupService");

            try
            {
                // Bundle VC++ redistributables
                bool bundlingSuccess = await _vcRedistBundler.BundleVCRedistLibrariesAsync();
                if (!bundlingSuccess)
                {
                    _logger.LogWarning("VC++ Redistributable bundling completed with warnings", "IntegratedStartupService");
                }

                // Get bundling status
                var bundlingStatus = await _vcRedistBundler.GetStatusAsync();
                _logger.LogInformation($"VC++ Redistributable bundling status: {bundlingStatus.AvailableLibraries.Count} available, {bundlingStatus.MissingLibraries.Count} missing", "IntegratedStartupService");

                // Log missing libraries
                if (bundlingStatus.MissingLibraries.Count > 0)
                {
                    _logger.LogWarning($"Missing VC++ libraries: {string.Join(", ", bundlingStatus.MissingLibraries)}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during VC++ Redistributable bundling: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        /// <summary>
        /// Initializes the architecture bridge if x86/x64 compatibility issues are detected
        /// </summary>
        private async Task InitializeArchitectureBridgeIfNeededAsync()
        {
            _logger.LogInformation("Checking if architecture bridge initialization is needed", "IntegratedStartupService");

            try
            {
                // Check if we're running in x64 mode
                bool is64BitProcess = Environment.Is64BitProcess;
                _logger.LogInformation($"Current process architecture: {(is64BitProcess ? "x64" : "x86")}", "IntegratedStartupService");

                if (!is64BitProcess)
                {
                    _logger.LogInformation("Running in x86 mode - architecture bridge not needed", "IntegratedStartupService");
                    return;
                }

                // Check for x86 libraries that would cause compatibility issues
                string librariesPath = Path.Combine(_applicationPath, "Libraries");
                var criticalLibraries = new[] { "apci.dll", "Volvo.ApciPlus.dll", "Volvo.ApciPlusData.dll" };

                bool hasArchitectureMismatch = false;
                foreach (string library in criticalLibraries)
                {
                    string libraryPath = Path.Combine(librariesPath, library);
                    if (File.Exists(libraryPath))
                    {
                        bool isCompatible = CheckLibraryArchitectureCompatibility(libraryPath);
                        if (!isCompatible)
                        {
                            _logger.LogWarning($"Architecture mismatch detected for {library} - x86 library in x64 process", "IntegratedStartupService");
                            hasArchitectureMismatch = true;
                        }
                    }
                }

                if (hasArchitectureMismatch)
                {
                    _logger.LogInformation("Architecture mismatch detected - setting up bridge environment", "IntegratedStartupService");

                    // Set environment variables to enable bridge mode
                    Environment.SetEnvironmentVariable("FORCE_ARCHITECTURE_BRIDGE", "true");
                    Environment.SetEnvironmentVariable("USE_PATCHED_IMPLEMENTATION", "true");
                    Environment.SetEnvironmentVariable("PHOENIX_VOCOM_ENABLED", "true");

                    // Verify bridge executable exists
                    string bridgePath = Path.Combine(_applicationPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");
                    if (File.Exists(bridgePath))
                    {
                        _logger.LogInformation($"Architecture bridge executable found at: {bridgePath}", "IntegratedStartupService");

                        // Copy critical x86 libraries to bridge directory
                        await CopyLibrariesToBridgeDirectoryAsync();
                    }
                    else
                    {
                        _logger.LogWarning($"Architecture bridge executable not found at: {bridgePath}", "IntegratedStartupService");
                        _logger.LogWarning("Application may experience compatibility issues with x86 libraries", "IntegratedStartupService");
                    }
                }
                else
                {
                    _logger.LogInformation("No architecture compatibility issues detected", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during architecture bridge initialization: {ex.Message}", "IntegratedStartupService", ex);
                // Don't throw - this is not critical for basic functionality
            }
        }

        /// <summary>
        /// Checks if a library is compatible with the current process architecture
        /// </summary>
        private bool CheckLibraryArchitectureCompatibility(string libraryPath)
        {
            try
            {
                using var fileStream = new FileStream(libraryPath, FileMode.Open, FileAccess.Read);
                using var reader = new BinaryReader(fileStream);

                // Read DOS header
                fileStream.Seek(0x3C, SeekOrigin.Begin);
                int peHeaderOffset = reader.ReadInt32();

                // Read PE header
                fileStream.Seek(peHeaderOffset + 4, SeekOrigin.Begin);
                ushort machineType = reader.ReadUInt16();

                // Determine compatibility
                bool is64BitLibrary = (machineType == 0x8664); // IMAGE_FILE_MACHINE_AMD64
                bool is32BitLibrary = (machineType == 0x014c); // IMAGE_FILE_MACHINE_I386
                bool is64BitProcess = Environment.Is64BitProcess;

                return (is64BitProcess && is64BitLibrary) || (!is64BitProcess && is32BitLibrary);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking architecture for {libraryPath}: {ex.Message}", "IntegratedStartupService");
                return false; // Assume incompatible if we can't check
            }
        }

        /// <summary>
        /// Copies critical libraries to the bridge directory
        /// </summary>
        private async Task CopyLibrariesToBridgeDirectoryAsync()
        {
            try
            {
                string librariesPath = Path.Combine(_applicationPath, "Libraries");
                string bridgeDir = Path.Combine(_applicationPath, "Bridge");

                if (!Directory.Exists(bridgeDir))
                {
                    Directory.CreateDirectory(bridgeDir);
                    _logger.LogInformation($"Created bridge directory: {bridgeDir}", "IntegratedStartupService");
                }

                var criticalLibraries = new[] { "apci.dll", "Volvo.ApciPlus.dll", "Volvo.ApciPlusData.dll" };

                foreach (string library in criticalLibraries)
                {
                    string sourcePath = Path.Combine(librariesPath, library);
                    string targetPath = Path.Combine(bridgeDir, library);

                    if (File.Exists(sourcePath) && !File.Exists(targetPath))
                    {
                        File.Copy(sourcePath, targetPath, true);
                        _logger.LogInformation($"Copied {library} to bridge directory", "IntegratedStartupService");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error copying libraries to bridge directory: {ex.Message}", "IntegratedStartupService");
            }

            await Task.CompletedTask;
        }

        private async Task ExtractAndVerifyLibrariesAsync()
        {
            _logger.LogInformation("Extracting and verifying libraries", "IntegratedStartupService");

            try
            {
                // Extract libraries
                bool extractionSuccess = await _libraryExtractor.ExtractLibrariesAsync();
                if (!extractionSuccess)
                {
                    _logger.LogWarning("Library extraction completed with warnings", "IntegratedStartupService");
                }

                // Get extraction status
                var extractionStatus = await _libraryExtractor.GetStatusAsync();
                _logger.LogInformation($"Library extraction status: {extractionStatus.AvailableLibraries.Count} available, {extractionStatus.MissingLibraries.Count} missing", "IntegratedStartupService");

                // Log missing libraries
                if (extractionStatus.MissingLibraries.Count > 0)
                {
                    _logger.LogWarning($"Missing libraries: {string.Join(", ", extractionStatus.MissingLibraries)}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during library extraction: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task InitializeDependencyManagerAsync()
        {
            _logger.LogInformation("Initializing dependency manager", "IntegratedStartupService");

            try
            {
                bool dependencySuccess = await _dependencyManager.InitializeAsync();
                if (!dependencySuccess)
                {
                    _logger.LogWarning("Dependency manager initialization completed with warnings", "IntegratedStartupService");
                }

                // Get dependency status
                var dependencyStatus = await _dependencyManager.GetStatusAsync();
                _logger.LogInformation($"Dependency status: {dependencyStatus.CriticalLibrariesFound.Count} found, {dependencyStatus.MissingLibraries.Count} missing", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error initializing dependency manager: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task SetupVocomEnvironmentAsync()
        {
            _logger.LogInformation("Setting up Vocom-specific environment", "IntegratedStartupService");

            try
            {
                // Set Vocom-specific environment variables
                Environment.SetEnvironmentVariable("USE_PATCHED_IMPLEMENTATION", "true");
                Environment.SetEnvironmentVariable("PHOENIX_VOCOM_ENABLED", "true");
                Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");
                Environment.SetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS", "false");

                // Set library paths
                string librariesPath = Path.Combine(_applicationPath, "Libraries");
                Environment.SetEnvironmentVariable("APCI_LIBRARY_PATH", librariesPath);

                // Update PATH to include our libraries
                string currentPath = Environment.GetEnvironmentVariable("PATH") ?? "";
                string newPath = $"{librariesPath};{Path.Combine(_applicationPath, "Drivers", "Vocom")};{currentPath}";
                Environment.SetEnvironmentVariable("PATH", newPath);

                // Create Vocom configuration if it doesn't exist
                await CreateVocomConfigurationAsync();

                _logger.LogInformation("Vocom environment setup completed", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error setting up Vocom environment: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task CreateVocomConfigurationAsync()
        {
            try
            {
                string configPath = Path.Combine(_applicationPath, "Drivers", "Vocom", "config.json");
                
                if (!File.Exists(configPath))
                {
                    var config = new
                    {
                        VocomDriver = new
                        {
                            DriverPath = "WUDFPuma.dll",
                            ApciPath = "apci.dll",
                            EnableLogging = true,
                            ConnectionTimeout = 5000,
                            RetryAttempts = 3
                        },
                        Communication = new
                        {
                            BaudRate = 115200,
                            DataBits = 8,
                            StopBits = 1,
                            Parity = "None",
                            Timeout = 1000
                        },
                        Detection = new
                        {
                            UseEnhancedDetection = true,
                            ScanUSB = true,
                            ScanBluetooth = true,
                            ScanWiFi = true,
                            AutoConnect = false
                        }
                    };

                    string configJson = System.Text.Json.JsonSerializer.Serialize(config, new System.Text.Json.JsonSerializerOptions
                    {
                        WriteIndented = true
                    });

                    await File.WriteAllTextAsync(configPath, configJson);
                    _logger.LogInformation($"Created Vocom configuration: {configPath}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error creating Vocom configuration: {ex.Message}", "IntegratedStartupService");
            }
        }

        private async Task<bool> VerifySystemReadinessAsync()
        {
            _logger.LogInformation("Verifying system readiness", "IntegratedStartupService");

            try
            {
                bool isReady = true;
                var issues = new List<string>();

                // Check critical directories
                var criticalDirectories = new[]
                {
                    Path.Combine(_applicationPath, "Libraries"),
                    Path.Combine(_applicationPath, "Drivers", "Vocom")
                };

                foreach (string directory in criticalDirectories)
                {
                    if (!Directory.Exists(directory))
                    {
                        issues.Add($"Missing critical directory: {directory}");
                        isReady = false;
                    }
                }

                // Check for critical libraries
                var criticalLibraries = new[]
                {
                    "WUDFPuma.dll",
                    "apci.dll",
                    "Volvo.ApciPlus.dll"
                };

                foreach (string library in criticalLibraries)
                {
                    string libraryPath = Path.Combine(_applicationPath, "Libraries", library);
                    if (!File.Exists(libraryPath))
                    {
                        // Check in drivers folder as fallback
                        string driverPath = Path.Combine(_applicationPath, "Drivers", "Vocom", library);
                        if (!File.Exists(driverPath))
                        {
                            issues.Add($"Missing critical library: {library}");
                            // Don't mark as not ready for missing libraries - they might be available in system
                        }
                    }
                }

                // Check environment variables
                var requiredEnvVars = new[]
                {
                    "VOLVOFLASHWR_HOME",
                    "VOLVOFLASHWR_LIBRARIES",
                    "USE_PATCHED_IMPLEMENTATION"
                };

                foreach (string envVar in requiredEnvVars)
                {
                    if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable(envVar)))
                    {
                        issues.Add($"Missing environment variable: {envVar}");
                        // Don't mark as not ready for missing env vars - they're not critical
                    }
                }

                // Log issues
                if (issues.Count > 0)
                {
                    _logger.LogWarning($"System readiness issues found: {string.Join(", ", issues)}", "IntegratedStartupService");
                }
                else
                {
                    _logger.LogInformation("System readiness verification passed", "IntegratedStartupService");
                }

                await Task.CompletedTask;
                return isReady;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error verifying system readiness: {ex.Message}", "IntegratedStartupService", ex);
                return false;
            }
        }

        private async Task LogSystemStatusAsync()
        {
            try
            {
                _logger.LogInformation("=== System Status Summary ===", "IntegratedStartupService");
                _logger.LogInformation($"Application Path: {_applicationPath}", "IntegratedStartupService");
                _logger.LogInformation($"Libraries Path: {Path.Combine(_applicationPath, "Libraries")}", "IntegratedStartupService");
                _logger.LogInformation($"Drivers Path: {Path.Combine(_applicationPath, "Drivers", "Vocom")}", "IntegratedStartupService");
                
                // Log environment variables
                var envVars = new[]
                {
                    "USE_PATCHED_IMPLEMENTATION",
                    "PHOENIX_VOCOM_ENABLED",
                    "VERBOSE_LOGGING",
                    "APCI_LIBRARY_PATH"
                };

                foreach (string envVar in envVars)
                {
                    string value = Environment.GetEnvironmentVariable(envVar) ?? "Not Set";
                    _logger.LogInformation($"Environment Variable {envVar}: {value}", "IntegratedStartupService");
                }

                _logger.LogInformation("=== End System Status Summary ===", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error logging system status: {ex.Message}", "IntegratedStartupService");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Gets the current initialization status
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets comprehensive system status
        /// </summary>
        public async Task<SystemStatus> GetSystemStatusAsync()
        {
            var status = new SystemStatus
            {
                IsInitialized = _isInitialized,
                ApplicationPath = _applicationPath,
                DependencyStatus = await _dependencyManager.GetStatusAsync(),
                ExtractionStatus = await _libraryExtractor.GetStatusAsync(),
                VCRedistStatus = await _vcRedistBundler.GetStatusAsync()
            };

            return status;
        }
    }

    /// <summary>
    /// Comprehensive system status
    /// </summary>
    public class SystemStatus
    {
        public bool IsInitialized { get; set; }
        public string ApplicationPath { get; set; } = string.Empty;
        public DependencyStatus? DependencyStatus { get; set; }
        public ExtractionStatus? ExtractionStatus { get; set; }
        public VCRedistStatus? VCRedistStatus { get; set; }
    }
}
