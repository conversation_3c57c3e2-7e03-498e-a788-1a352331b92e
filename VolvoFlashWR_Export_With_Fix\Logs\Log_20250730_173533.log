Log started at 7/30/2025 5:35:33 PM
2025-07-30 17:35:33.392 [Information] LoggingService: Logging service initialized
2025-07-30 17:35:33.412 [Information] App: Starting integrated application initialization
2025-07-30 17:35:33.415 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-30 17:35:33.419 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-30 17:35:33.421 [Information] IntegratedStartupService: Setting up application environment
2025-07-30 17:35:33.422 [Information] IntegratedStartupService: Application environment setup completed
2025-07-30 17:35:33.424 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-30 17:35:33.427 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-30 17:35:33.430 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-30 17:35:33.436 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-30 17:35:33.446 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.449 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.453 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.454 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-30 17:35:33.458 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.461 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.465 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.466 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:35:33.471 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.474 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.478 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.479 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:35:33.484 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.487 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.490 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.491 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:35:33.495 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.498 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.502 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.503 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:35:33.507 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.510 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.513 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.513 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:35:33.519 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-30 17:35:33.522 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.526 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-30 17:35:33.527 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:35:33.530 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-30 17:35:33.532 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-30 17:35:33.533 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-30 17:35:33.550 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-30 17:35:33.551 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-30 17:35:33.559 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-30 17:35:33.560 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-30 17:35:33.560 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-30 17:35:33.693 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-30 17:35:33.693 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-30 17:35:33.756 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-30 17:35:33.757 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:35:33.757 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:35:33.758 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:35:33.758 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:35:33.758 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:35:33.759 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:35:33.768 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-30 17:35:33.769 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-30 17:35:33.769 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-30 17:35:33.778 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-30 17:35:33.779 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-30 17:35:33.782 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-30 17:35:33.784 [Information] LibraryExtractor: Starting library extraction process
2025-07-30 17:35:33.788 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-30 17:35:33.794 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-30 17:35:33.797 [Information] LibraryExtractor: Copying system libraries
2025-07-30 17:35:33.808 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-30 17:35:33.829 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-30 17:36:03.849 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:36:04.852 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-30 17:36:34.856 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:36:34.857 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:37:04.862 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:37:05.863 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-30 17:37:35.865 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:37:35.866 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:38:05.868 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:38:06.869 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-30 17:38:36.872 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-30 17:38:36.873 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-30 17:38:36.873 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-30 17:38:36.880 [Information] LibraryExtractor: Verifying library extraction
2025-07-30 17:38:36.881 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-30 17:38:36.881 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-30 17:38:36.882 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-30 17:38:36.882 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-30 17:38:36.882 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-30 17:38:36.887 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-30 17:38:36.889 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-30 17:38:36.890 [Information] DependencyManager: Initializing dependency manager
2025-07-30 17:38:36.891 [Information] DependencyManager: Setting up library search paths
2025-07-30 17:38:36.893 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:38:36.894 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:38:36.895 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-30 17:38:36.895 [Information] DependencyManager: Updated PATH environment variable
2025-07-30 17:38:36.897 [Information] DependencyManager: Verifying required directories
2025-07-30 17:38:36.897 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:38:36.898 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:38:36.898 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-30 17:38:36.899 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-30 17:38:36.901 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-30 17:38:36.923 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-30 17:38:36.934 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-30 17:38:36.935 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-07-30 17:38:36.942 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-30 17:38:36.956 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-30 17:38:36.963 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-30 17:38:36.963 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-07-30 17:38:36.974 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-30 17:38:36.977 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-30 17:38:36.978 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-30 17:38:36.996 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-30 17:38:37.055 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-07-30 17:38:37.070 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-30 17:38:37.071 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-07-30 17:38:37.072 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:38:37.072 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:38:37.073 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:38:37.073 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:38:37.074 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:38:37.074 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:38:37.075 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:38:37.075 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:38:37.076 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:38:37.077 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:38:37.078 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:38:37.078 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:38:37.079 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-30 17:38:37.080 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-30 17:38:37.080 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-30 17:38:37.081 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-30 17:38:37.082 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-30 17:38:37.082 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-30 17:38:37.083 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-07-30 17:38:37.083 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-30 17:38:37.085 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-30 17:38:37.126 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-30 17:38:37.212 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:38:37.213 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-30 17:38:37.299 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:38:37.299 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:38:37.300 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-30 17:38:37.368 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-30 17:38:37.369 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-30 17:38:37.424 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-30 17:38:37.425 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-30 17:38:37.426 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-30 17:38:37.693 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-30 17:38:37.693 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-30 17:38:37.948 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-30 17:38:37.949 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-30 17:38:37.950 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-30 17:38:38.050 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-30 17:38:38.051 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-30 17:38:38.156 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-30 17:38:38.156 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-30 17:38:38.157 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-30 17:38:38.222 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-30 17:38:38.223 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-30 17:38:38.224 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-30 17:38:38.225 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-30 17:38:38.225 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-30 17:38:38.225 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcr120.dll
2025-07-30 17:38:38.226 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-30 17:38:38.227 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-30 17:38:38.227 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-30 17:38:38.228 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp120.dll
2025-07-30 17:38:38.228 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-30 17:38:38.229 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-30 17:38:38.230 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-30 17:38:38.230 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll (x64)
2025-07-30 17:38:38.231 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-30 17:38:38.232 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll (x64)
2025-07-30 17:38:38.232 [Information] DependencyManager: Setting up environment variables
2025-07-30 17:38:38.233 [Information] DependencyManager: Environment variables configured
2025-07-30 17:38:38.234 [Information] DependencyManager: Verifying library loading status
2025-07-30 17:38:38.560 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-30 17:38:38.560 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-30 17:38:38.561 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-30 17:38:38.564 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-30 17:38:38.565 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-30 17:38:38.570 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-30 17:38:38.573 [Information] IntegratedStartupService: Verifying system readiness
2025-07-30 17:38:38.573 [Information] IntegratedStartupService: System readiness verification passed
2025-07-30 17:38:38.574 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-30 17:38:38.575 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-30 17:38:38.576 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-30 17:38:38.576 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:38:38.576 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:38:38.577 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-30 17:38:38.577 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-30 17:38:38.577 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-30 17:38:38.578 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:38:38.578 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-30 17:38:38.578 [Information] App: Integrated startup completed successfully
2025-07-30 17:38:38.582 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-30 17:38:38.807 [Information] App: Initializing application services
2025-07-30 17:38:38.809 [Information] AppConfigurationService: Initializing configuration service
2025-07-30 17:38:38.809 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-30 17:38:38.869 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-30 17:38:38.870 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-30 17:38:38.871 [Information] App: Configuration service initialized successfully
2025-07-30 17:38:38.872 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-30 17:38:38.873 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-30 17:38:38.879 [Information] App: Environment variable exists: True, not 'false': False
2025-07-30 17:38:38.880 [Information] App: Final useDummyImplementations value: False
2025-07-30 17:38:38.880 [Information] App: Updating config to NOT use dummy implementations
2025-07-30 17:38:38.882 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-30 17:38:38.897 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-30 17:38:38.897 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-30 17:38:38.898 [Information] App: usePatchedImplementation flag is: True
2025-07-30 17:38:38.898 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-30 17:38:38.898 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-30 17:38:38.899 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-30 17:38:38.899 [Information] App: verboseLogging flag is: True
2025-07-30 17:38:38.901 [Information] App: Verifying real hardware requirements...
2025-07-30 17:38:38.901 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-30 17:38:38.902 [Information] App: ✓ Found critical library: apci.dll
2025-07-30 17:38:38.902 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-30 17:38:38.902 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-30 17:38:38.903 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-30 17:38:38.903 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-30 17:38:38.903 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-30 17:38:38.903 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-30 17:38:38.915 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-30 17:38:38.917 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-30 17:38:38.917 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-30 17:38:38.919 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-30 17:38:38.923 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-30 17:39:52.300 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-30 17:39:52.301 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-30 17:39:52.301 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-30 17:39:52.302 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:39:52.303 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-30 17:39:52.303 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-30 17:39:52.304 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-30 17:39:52.304 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-30 17:39:52.305 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-30 17:39:52.305 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-30 17:39:52.307 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-30 17:39:52.308 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-30 17:39:52.308 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-30 17:39:52.308 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-30 17:39:52.309 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-30 17:39:52.309 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-30 17:39:52.309 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-30 17:39:52.310 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-30 17:39:52.313 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-30 17:39:52.313 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-30 17:39:52.315 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-30 17:39:52.315 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-30 17:39:52.316 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-30 17:39:52.316 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-30 17:39:52.318 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-30 17:39:52.318 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-30 17:39:52.321 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-30 17:39:52.322 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-30 17:39:52.324 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-30 17:39:52.325 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-30 17:39:52.352 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-30 17:39:52.353 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-30 17:39:52.359 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-30 17:39:52.359 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-30 17:39:52.360 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-30 17:39:52.360 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-30 17:39:52.364 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-30 17:39:52.364 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-30 17:39:52.364 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-30 17:39:52.365 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:39:52.365 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-30 17:39:52.365 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-30 17:39:52.366 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-30 17:39:52.368 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-30 17:39:52.371 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-30 17:39:52.378 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-30 17:39:52.379 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-30 17:39:52.379 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-30 17:39:52.395 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-30 17:39:52.416 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-30 17:39:52.417 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-30 17:39:52.418 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-30 17:39:52.420 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:39:52.421 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-30 17:39:52.422 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:39:52.422 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-30 17:39:52.517 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-30 17:39:52.518 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-30 17:39:52.519 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-30 17:39:52.519 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-30 17:39:52.520 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-30 17:39:52.522 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-30 17:39:52.523 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-30 17:39:52.523 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-30 17:39:52.526 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-30 17:39:52.527 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-30 17:39:52.528 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-30 17:39:52.528 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-30 17:39:52.529 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-30 17:39:52.532 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-30 17:39:52.533 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-30 17:39:52.533 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-30 17:39:52.533 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-30 17:39:52.534 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-30 17:39:52.534 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-30 17:39:52.534 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-30 17:39:52.535 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-30 17:39:52.536 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-30 17:39:52.536 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-30 17:39:52.539 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-30 17:39:52.539 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-30 17:39:52.539 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-30 17:39:52.540 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:39:52.540 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-30 17:39:52.543 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-30 17:39:52.543 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-30 17:39:52.544 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-30 17:39:52.544 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-30 17:39:52.545 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-30 17:39:52.545 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-30 17:39:52.546 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-30 17:39:52.548 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-30 17:39:52.551 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-30 17:39:52.551 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-30 17:39:52.788 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-30 17:39:52.789 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-30 17:39:52.789 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:39:52.790 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:39:52.792 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-30 17:39:52.794 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:52.795 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:52.795 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-30 17:39:52.796 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:52.797 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:52.798 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-30 17:39:53.005 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:53.006 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-30 17:39:53.126 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:53.127 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-30 17:39:53.128 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:53.129 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:53.129 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-30 17:39:53.251 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:53.349 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:53.349 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-30 17:39:53.433 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:39:53.579 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:39:53.700 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:53.828 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:53.828 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-30 17:39:53.829 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-30 17:39:54.077 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:54.077 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-30 17:39:54.167 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:54.254 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:54.255 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-30 17:39:54.411 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-30 17:39:54.549 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-30 17:39:54.549 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-30 17:39:54.680 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:39:54.831 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:39:54.892 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-30 17:39:54.895 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-30 17:39:54.896 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-30 17:39:54.896 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-30 17:39:54.897 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-30 17:39:54.897 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-30 17:39:54.899 [Information] VocomDriver: Initializing Vocom driver
2025-07-30 17:39:54.901 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-30 17:39:54.905 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-30 17:39:54.905 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 17:39:54.905 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 17:39:54.907 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-30 17:39:54.907 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-30 17:40:03.368 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-30 17:40:04.086 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-30 17:40:04.090 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-30 17:40:04.091 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\msvcp140.dll
2025-07-30 17:40:04.092 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\vcruntime140.dll
2025-07-30 17:40:04.093 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-30 17:40:04.165 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-30 17:40:04.343 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-30 17:40:04.502 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-30 17:40:04.549 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-30 17:40:04.550 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-30 17:40:04.550 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-30 17:40:04.552 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-30 17:40:04.553 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-30 17:40:04.553 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-30 17:40:04.554 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-30 17:40:04.554 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-30 17:40:04.554 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-30 17:40:04.555 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-30 17:40:04.555 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-30 17:40:04.555 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-30 17:40:04.556 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-30 17:40:04.556 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-30 17:40:04.556 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-30 17:40:04.557 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-30 17:40:04.557 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-30 17:40:04.557 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-30 17:40:04.558 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-30 17:40:04.559 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-30 17:40:04.559 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-30 17:40:04.560 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-30 17:40:04.560 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-30 17:40:04.561 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-30 17:40:04.561 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-30 17:40:04.561 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-30 17:40:04.562 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-30 17:40:04.563 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-30 17:40:04.563 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-30 17:40:04.564 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-30 17:40:04.564 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-30 17:40:04.564 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-30 17:40:04.565 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-30 17:40:04.565 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-30 17:40:04.565 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-30 17:40:04.565 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-30 17:40:04.566 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-30 17:40:04.566 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-30 17:40:04.566 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-30 17:40:04.566 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-30 17:40:04.567 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-30 17:40:04.567 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-30 17:40:04.567 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-30 17:40:04.567 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-30 17:40:04.568 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-30 17:40:04.568 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-30 17:40:04.569 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-30 17:40:04.570 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-30 17:40:04.571 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-30 17:40:04.572 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-30 17:40:04.572 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-30 17:40:04.572 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-30 17:40:04.573 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-30 17:40:04.573 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-30 17:40:04.578 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-30 17:40:04.579 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-30 17:40:04.580 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-30 17:40:04.582 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-30 17:40:04.651 [Information] WiFiCommunicationService: WiFi is available
2025-07-30 17:40:04.652 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-30 17:40:04.653 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-30 17:40:04.655 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-30 17:40:04.657 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-30 17:40:04.658 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-30 17:40:04.660 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 17:40:04.661 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 17:40:04.663 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 17:40:04.665 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 17:40:04.669 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 17:40:04.669 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 17:40:04.669 [Information] VocomService: Native USB communication service initialized
2025-07-30 17:40:04.670 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 17:40:04.671 [Information] VocomService: Connection recovery service initialized
2025-07-30 17:40:04.671 [Information] VocomService: Enhanced services initialization completed
2025-07-30 17:40:04.673 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:04.693 [Information] VocomService: PTT application is not running
2025-07-30 17:40:04.695 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:04.697 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:04.698 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 17:40:04.698 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-30 17:40:04.698 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-30 17:40:04.701 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:40:04.702 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:40:04.703 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:40:04.706 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:40:05.094 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:40:05.095 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:40:05.096 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:40:05.098 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:40:05.098 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:40:05.100 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:40:05.102 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:40:05.105 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:40:05.486 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:40:05.488 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:40:05.491 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:40:05.492 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:40:05.494 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:40:05.504 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:40:05.505 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:40:05.506 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:40:05.506 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:40:05.506 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:05.507 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:05.511 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:40:05.513 [Debug] VocomService: WiFi is available
2025-07-30 17:40:05.513 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:40:05.514 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-30 17:40:05.516 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: be003e6e-1b87-424f-8841-ca497cf0bee0, Type: USB)
2025-07-30 17:40:05.516 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 1ceafe87-7bea-4447-b98b-7ddae8560456, Type: Bluetooth)
2025-07-30 17:40:05.516 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 2e25d56f-3b67-4e40-89cb-c8cafb963a9f, Type: WiFi)
2025-07-30 17:40:05.517 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-30 17:40:05.518 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-30 17:40:05.518 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-30 17:40:05.518 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-30 17:40:05.519 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-30 17:40:05.519 [Information] App: Architecture-aware Vocom service created successfully
2025-07-30 17:40:05.519 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 17:40:05.520 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 17:40:05.520 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 17:40:05.520 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 17:40:05.521 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 17:40:05.521 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 17:40:05.522 [Information] VocomService: Native USB communication service initialized
2025-07-30 17:40:05.522 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 17:40:05.522 [Information] VocomService: Connection recovery service initialized
2025-07-30 17:40:05.522 [Information] VocomService: Enhanced services initialization completed
2025-07-30 17:40:05.523 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:05.538 [Information] VocomService: PTT application is not running
2025-07-30 17:40:05.538 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:05.539 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:05.539 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 17:40:05.539 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-30 17:40:05.540 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-30 17:40:05.540 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-30 17:40:05.583 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:40:05.583 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:40:05.584 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:40:05.584 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:40:05.872 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:40:05.873 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:40:05.873 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:40:05.874 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:40:05.874 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:40:05.874 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:40:05.875 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:40:05.875 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:40:06.138 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:40:06.139 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:40:06.139 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:40:06.140 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:40:06.141 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:40:06.147 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:40:06.148 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:40:06.148 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:40:06.149 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:40:06.149 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:06.150 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:06.150 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:40:06.151 [Debug] VocomService: WiFi is available
2025-07-30 17:40:06.151 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:40:06.151 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-30 17:40:06.153 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:06.154 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:06.168 [Information] VocomService: PTT application is not running
2025-07-30 17:40:06.171 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 17:40:06.171 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 17:40:06.172 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:06.185 [Information] VocomService: PTT application is not running
2025-07-30 17:40:06.185 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 17:40:06.188 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 17:40:06.190 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:40:06.193 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:40:06.194 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:06.195 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:06.195 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:06.196 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:06.196 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:06.196 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:06.197 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:06.197 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:06.197 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:06.198 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:40:06.198 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:06.198 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:06.199 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:06.199 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:06.199 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:06.199 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:40:06.200 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:06.201 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:40:06.201 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:06.201 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:40:06.202 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:40:06.202 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:40:06.202 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 17:40:07.204 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 17:40:07.204 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:40:07.204 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:40:07.205 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:07.205 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:07.206 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:07.206 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:07.206 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:07.206 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:07.207 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:07.207 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:07.207 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:07.207 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:40:07.208 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:07.208 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:07.208 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:07.208 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:07.209 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:07.209 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:40:07.209 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:07.210 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:40:07.210 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:07.210 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:40:07.210 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:40:07.211 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:40:07.211 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 17:40:08.211 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 17:40:08.211 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:40:08.211 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.212 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.212 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.213 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.213 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.214 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.214 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.214 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.214 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.215 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.215 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:40:08.215 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.216 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.216 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.216 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.217 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.217 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.217 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:08.218 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:40:08.218 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:08.218 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:40:08.218 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.219 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:40:08.219 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 17:40:08.220 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 17:40:08.220 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 17:40:08.379 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 17:40:08.380 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.382 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 17:40:08.383 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 17:40:08.383 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 17:40:08.383 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 17:40:08.384 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 17:40:08.387 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 17:40:08.388 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 17:40:08.393 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:40:08.394 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 17:40:08.395 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 17:40:08.395 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 17:40:08.395 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:40:08.396 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:08.396 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-30 17:40:08.401 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-30 17:40:08.406 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:08.406 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-30 17:40:08.412 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-30 17:40:08.415 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-30 17:40:08.415 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-30 17:40:08.416 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:40:08.417 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-30 17:40:08.417 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:40:08.418 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-30 17:40:08.418 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-30 17:40:08.421 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-30 17:40:08.422 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:40:08.422 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:40:08.422 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:40:08.423 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:40:08.687 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:40:08.687 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:40:08.688 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:40:08.688 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:40:08.688 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:40:08.688 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:40:08.689 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:40:08.689 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:40:08.931 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:40:08.931 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:40:08.932 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:40:08.932 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:40:08.933 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:40:08.938 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:40:08.939 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:40:08.939 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:40:08.940 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:40:08.940 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:08.940 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:08.941 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:40:08.941 [Debug] VocomService: WiFi is available
2025-07-30 17:40:08.941 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:40:08.944 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:08.945 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:08.945 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:08.961 [Information] VocomService: PTT application is not running
2025-07-30 17:40:08.962 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-30 17:40:08.962 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-30 17:40:08.962 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:08.980 [Information] VocomService: PTT application is not running
2025-07-30 17:40:08.980 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-30 17:40:08.980 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-30 17:40:08.981 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:40:08.981 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.982 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.982 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.982 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.983 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.983 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.983 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.983 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.984 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.984 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.984 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:40:08.984 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.985 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.985 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:08.985 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:08.986 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:08.986 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.986 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:08.986 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:40:08.987 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:08.987 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:40:08.987 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:40:08.987 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:40:08.988 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-30 17:40:09.988 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-30 17:40:09.988 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:40:09.989 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:40:09.989 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:09.990 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:09.990 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:09.990 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:09.991 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:09.991 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:09.991 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:09.991 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:09.992 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:09.992 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:40:09.992 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:09.993 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:09.994 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:09.994 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:09.995 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:09.995 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:40:09.995 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:09.996 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:40:09.996 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:09.996 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:40:09.997 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:40:09.997 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:40:09.997 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-30 17:40:10.998 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-30 17:40:10.998 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-30 17:40:10.998 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-30 17:40:10.999 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:11.000 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:11.000 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:11.000 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:11.001 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:11.001 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:11.001 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:11.001 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:11.002 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:11.002 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-30 17:40:11.002 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:11.002 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:11.003 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-30 17:40:11.003 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-30 17:40:11.004 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-30 17:40:11.004 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-30 17:40:11.004 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:11.005 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-30 17:40:11.005 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-30 17:40:11.005 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-30 17:40:11.006 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-30 17:40:11.006 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-30 17:40:11.006 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-30 17:40:11.006 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-30 17:40:11.007 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-30 17:40:11.007 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-30 17:40:11.007 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-30 17:40:11.008 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-30 17:40:11.008 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-30 17:40:11.008 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-30 17:40:11.008 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-30 17:40:11.009 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-30 17:40:11.009 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-30 17:40:11.009 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-30 17:40:11.010 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-30 17:40:11.010 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-30 17:40:11.011 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-30 17:40:11.011 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-30 17:40:11.011 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:40:11.012 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-30 17:40:11.012 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:11.013 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:11.013 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-30 17:40:11.013 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-30 17:40:11.014 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:40:11.014 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:11.029 [Information] VocomService: PTT application is not running
2025-07-30 17:40:11.032 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:40:11.032 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:11.032 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:11.034 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-30 17:40:11.838 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:40:11.838 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-30 17:40:11.839 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-30 17:40:11.839 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-30 17:40:11.842 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-30 17:40:11.845 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-30 17:40:11.850 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-30 17:40:11.852 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-30 17:40:11.856 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-30 17:40:11.863 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:40:11.866 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:40:11.878 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:40:11.879 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-30 17:40:11.879 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-30 17:40:11.880 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:40:11.880 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:40:11.880 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-30 17:40:11.881 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:40:11.881 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-30 17:40:11.881 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-30 17:40:11.881 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-30 17:40:11.882 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-30 17:40:11.882 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-30 17:40:11.882 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-30 17:40:11.882 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-30 17:40:11.883 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-30 17:40:11.883 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-30 17:40:11.883 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-30 17:40:11.887 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-30 17:40:11.894 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-30 17:40:11.895 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-30 17:40:11.899 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-30 17:40:11.900 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:11.907 [Information] CANRegisterAccess: Read value 0xBD from register 0x0141 (simulated)
2025-07-30 17:40:11.909 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-30 17:40:11.910 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-30 17:40:11.910 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-30 17:40:11.916 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-30 17:40:11.916 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-30 17:40:11.922 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-30 17:40:11.922 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-30 17:40:11.923 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-30 17:40:11.929 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-30 17:40:11.929 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-30 17:40:11.930 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-30 17:40:11.936 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-30 17:40:11.936 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-30 17:40:11.942 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-30 17:40:11.942 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-30 17:40:11.948 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-30 17:40:11.948 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-30 17:40:11.954 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-30 17:40:11.954 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-30 17:40:11.960 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-30 17:40:11.960 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-30 17:40:11.967 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-30 17:40:11.967 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-30 17:40:11.973 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-30 17:40:11.973 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-30 17:40:11.979 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-30 17:40:11.979 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-30 17:40:11.985 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-30 17:40:11.985 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-30 17:40:11.991 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-30 17:40:11.991 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-30 17:40:11.997 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-30 17:40:11.997 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-30 17:40:12.003 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-30 17:40:12.003 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-30 17:40:12.009 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-30 17:40:12.009 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-30 17:40:12.015 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-30 17:40:12.016 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-30 17:40:12.022 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-30 17:40:12.022 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-30 17:40:12.027 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-30 17:40:12.027 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-30 17:40:12.033 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-30 17:40:12.033 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-30 17:40:12.033 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-30 17:40:12.040 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-30 17:40:12.040 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-30 17:40:12.041 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-30 17:40:12.041 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:12.046 [Information] CANRegisterAccess: Read value 0x6F from register 0x0141 (simulated)
2025-07-30 17:40:12.052 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:12.058 [Information] CANRegisterAccess: Read value 0x47 from register 0x0141 (simulated)
2025-07-30 17:40:12.064 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:12.070 [Information] CANRegisterAccess: Read value 0x7A from register 0x0141 (simulated)
2025-07-30 17:40:12.070 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-30 17:40:12.071 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-30 17:40:12.071 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-30 17:40:12.071 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 17:40:12.077 [Information] CANRegisterAccess: Read value 0xAF from register 0x0140 (simulated)
2025-07-30 17:40:12.083 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 17:40:12.089 [Information] CANRegisterAccess: Read value 0x78 from register 0x0140 (simulated)
2025-07-30 17:40:12.089 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-30 17:40:12.090 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:40:12.092 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:40:12.093 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:40:12.104 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-30 17:40:12.105 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-30 17:40:12.105 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-30 17:40:12.110 [Information] VocomService: Sending data and waiting for response
2025-07-30 17:40:12.110 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-30 17:40:12.163 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-30 17:40:12.164 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-30 17:40:12.165 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-30 17:40:12.166 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:40:12.167 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:40:12.178 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:40:12.179 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-30 17:40:12.179 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-30 17:40:12.190 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-30 17:40:12.201 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-30 17:40:12.212 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-30 17:40:12.223 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-30 17:40:12.234 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:40:12.236 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:40:12.236 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:40:12.248 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:40:12.249 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-30 17:40:12.249 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-30 17:40:12.260 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-30 17:40:12.272 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-30 17:40:12.283 [Information] IICProtocolHandler: Enabling IIC module
2025-07-30 17:40:12.294 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-30 17:40:12.305 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-30 17:40:12.316 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:40:12.318 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:40:12.318 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:40:12.330 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:40:12.331 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-30 17:40:12.331 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:40:12.332 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:40:12.332 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-30 17:40:12.332 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:40:12.333 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-30 17:40:12.333 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-30 17:40:12.333 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-30 17:40:12.333 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-30 17:40:12.334 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-30 17:40:12.334 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-30 17:40:12.334 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-30 17:40:12.335 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-30 17:40:12.335 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-30 17:40:12.335 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-30 17:40:12.336 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-30 17:40:12.436 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:40:12.437 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-30 17:40:12.440 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-30 17:40:12.441 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:12.442 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-30 17:40:12.442 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-30 17:40:12.443 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:12.443 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-30 17:40:12.444 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-30 17:40:12.445 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:12.445 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-30 17:40:12.445 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-30 17:40:12.446 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:12.446 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-30 17:40:12.447 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-30 17:40:12.447 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-30 17:40:12.448 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-30 17:40:12.449 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-30 17:40:12.452 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-30 17:40:12.453 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-30 17:40:12.457 [Information] BackupService: Initializing backup service
2025-07-30 17:40:12.458 [Information] BackupService: Backup service initialized successfully
2025-07-30 17:40:12.458 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-30 17:40:12.458 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-30 17:40:12.461 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-30 17:40:12.503 [Information] BackupService: Compressing backup data
2025-07-30 17:40:12.513 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-30 17:40:12.515 [Information] BackupServiceFactory: Created template for category: Production
2025-07-30 17:40:12.515 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-30 17:40:12.516 [Information] BackupService: Compressing backup data
2025-07-30 17:40:12.517 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-30 17:40:12.519 [Information] BackupServiceFactory: Created template for category: Development
2025-07-30 17:40:12.520 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-30 17:40:12.520 [Information] BackupService: Compressing backup data
2025-07-30 17:40:12.524 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-30 17:40:12.524 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-30 17:40:12.525 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-30 17:40:12.526 [Information] BackupService: Compressing backup data
2025-07-30 17:40:12.528 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-30 17:40:12.528 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-30 17:40:12.528 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-30 17:40:12.529 [Information] BackupService: Compressing backup data
2025-07-30 17:40:12.530 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-07-30 17:40:12.530 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-30 17:40:12.531 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-30 17:40:12.531 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-30 17:40:12.532 [Information] BackupService: Compressing backup data
2025-07-30 17:40:12.533 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-30 17:40:12.533 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-30 17:40:12.533 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-30 17:40:12.535 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-30 17:40:12.539 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-30 17:40:12.541 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-30 17:40:12.611 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-30 17:40:12.612 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-30 17:40:12.613 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-30 17:40:12.614 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-30 17:40:12.614 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-30 17:40:12.616 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-30 17:40:12.616 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-30 17:40:12.621 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-30 17:40:12.621 [Information] App: Flash operation monitor service initialized successfully
2025-07-30 17:40:12.631 [Information] LicensingService: Initializing licensing service
2025-07-30 17:40:12.684 [Information] LicensingService: License information loaded successfully
2025-07-30 17:40:12.687 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-30 17:40:12.688 [Information] App: Licensing service initialized successfully
2025-07-30 17:40:12.688 [Information] App: License status: Trial
2025-07-30 17:40:12.689 [Information] App: Trial period: 27 days remaining
2025-07-30 17:40:12.689 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-30 17:40:12.720 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-30 17:40:12.871 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-30 17:40:12.872 [Information] VocomService: Initializing enhanced Vocom services
2025-07-30 17:40:12.872 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-30 17:40:12.872 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-30 17:40:12.873 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-30 17:40:12.873 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-30 17:40:12.874 [Information] VocomService: Native USB communication service initialized
2025-07-30 17:40:12.874 [Information] VocomService: Enhanced device detection service initialized
2025-07-30 17:40:12.874 [Information] VocomService: Connection recovery service initialized
2025-07-30 17:40:12.875 [Information] VocomService: Enhanced services initialization completed
2025-07-30 17:40:12.875 [Information] VocomService: Checking if PTT application is running
2025-07-30 17:40:12.888 [Information] VocomService: PTT application is not running
2025-07-30 17:40:12.888 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:12.889 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:12.889 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-30 17:40:12.940 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-30 17:40:12.940 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-30 17:40:12.941 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-30 17:40:12.941 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:40:12.941 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-30 17:40:12.941 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 0f46d5c3-5b5e-4f6c-a6bb-b6c8c3c5d95f
2025-07-30 17:40:12.943 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-30 17:40:12.944 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-30 17:40:12.944 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-30 17:40:12.944 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-30 17:40:12.946 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-30 17:40:12.946 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-30 17:40:12.947 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-30 17:40:12.948 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:40:12.948 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-30 17:40:12.959 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:40:12.959 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-30 17:40:12.960 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-30 17:40:12.960 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:40:12.960 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:40:12.961 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-30 17:40:12.961 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:40:12.961 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-30 17:40:12.962 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-30 17:40:12.962 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-30 17:40:12.962 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-30 17:40:12.963 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-30 17:40:12.963 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-30 17:40:12.964 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-30 17:40:12.964 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-30 17:40:12.964 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-30 17:40:12.965 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-30 17:40:12.965 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-30 17:40:12.971 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-30 17:40:12.971 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-30 17:40:12.972 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-30 17:40:12.972 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:12.977 [Information] CANRegisterAccess: Read value 0x3E from register 0x0141 (simulated)
2025-07-30 17:40:12.983 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:12.989 [Information] CANRegisterAccess: Read value 0xC2 from register 0x0141 (simulated)
2025-07-30 17:40:12.995 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:13.001 [Information] CANRegisterAccess: Read value 0x52 from register 0x0141 (simulated)
2025-07-30 17:40:13.007 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:13.013 [Information] CANRegisterAccess: Read value 0x77 from register 0x0141 (simulated)
2025-07-30 17:40:13.013 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-30 17:40:13.014 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-30 17:40:13.014 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-30 17:40:13.020 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-30 17:40:13.020 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-30 17:40:13.027 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-30 17:40:13.028 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-30 17:40:13.028 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-30 17:40:13.034 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-30 17:40:13.034 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-30 17:40:13.035 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-30 17:40:13.041 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-30 17:40:13.041 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-30 17:40:13.048 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-30 17:40:13.048 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-30 17:40:13.055 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-30 17:40:13.055 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-30 17:40:13.061 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-30 17:40:13.061 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-30 17:40:13.068 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-30 17:40:13.068 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-30 17:40:13.075 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-30 17:40:13.075 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-30 17:40:13.082 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-30 17:40:13.082 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-30 17:40:13.089 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-30 17:40:13.089 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-30 17:40:13.095 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-30 17:40:13.095 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-30 17:40:13.101 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-30 17:40:13.101 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-30 17:40:13.108 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-30 17:40:13.108 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-30 17:40:13.115 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-30 17:40:13.115 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-30 17:40:13.122 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-30 17:40:13.122 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-30 17:40:13.128 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-30 17:40:13.128 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-30 17:40:13.135 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-30 17:40:13.135 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-30 17:40:13.141 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-30 17:40:13.141 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-30 17:40:13.147 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-30 17:40:13.147 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-30 17:40:13.148 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-30 17:40:13.154 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-30 17:40:13.154 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-30 17:40:13.154 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-30 17:40:13.155 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-30 17:40:13.161 [Information] CANRegisterAccess: Read value 0x34 from register 0x0141 (simulated)
2025-07-30 17:40:13.163 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-30 17:40:13.163 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-30 17:40:13.164 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-30 17:40:13.164 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 17:40:13.169 [Information] CANRegisterAccess: Read value 0x85 from register 0x0140 (simulated)
2025-07-30 17:40:13.175 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-30 17:40:13.181 [Information] CANRegisterAccess: Read value 0xD2 from register 0x0140 (simulated)
2025-07-30 17:40:13.181 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-30 17:40:13.182 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-30 17:40:13.182 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:40:13.183 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-30 17:40:13.194 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-30 17:40:13.195 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-30 17:40:13.195 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-30 17:40:13.196 [Information] VocomService: Sending data and waiting for response
2025-07-30 17:40:13.196 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-30 17:40:13.246 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-30 17:40:13.246 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-30 17:40:13.247 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-30 17:40:13.247 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:40:13.247 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-30 17:40:13.259 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:40:13.259 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-30 17:40:13.260 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-30 17:40:13.271 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-30 17:40:13.282 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-30 17:40:13.293 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-30 17:40:13.304 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-30 17:40:13.315 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-30 17:40:13.315 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:40:13.315 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-30 17:40:13.327 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:40:13.328 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-30 17:40:13.328 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-30 17:40:13.339 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-30 17:40:13.350 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-30 17:40:13.361 [Information] IICProtocolHandler: Enabling IIC module
2025-07-30 17:40:13.372 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-30 17:40:13.383 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-30 17:40:13.394 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-30 17:40:13.394 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:40:13.395 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-30 17:40:13.406 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:40:13.406 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-30 17:40:13.407 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-30 17:40:13.407 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-30 17:40:13.407 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-30 17:40:13.407 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-30 17:40:13.408 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-30 17:40:13.408 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-30 17:40:13.408 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-30 17:40:13.409 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-30 17:40:13.409 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-30 17:40:13.409 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-30 17:40:13.410 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-30 17:40:13.410 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-30 17:40:13.410 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-30 17:40:13.410 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-30 17:40:13.411 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-30 17:40:13.511 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-30 17:40:13.512 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-30 17:40:13.512 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-30 17:40:13.513 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:13.513 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-30 17:40:13.513 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-30 17:40:13.514 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:13.514 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-30 17:40:13.514 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-30 17:40:13.515 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:13.515 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-30 17:40:13.515 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-30 17:40:13.516 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-30 17:40:13.516 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-30 17:40:13.517 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-30 17:40:13.567 [Information] BackupService: Initializing backup service
2025-07-30 17:40:13.568 [Information] BackupService: Backup service initialized successfully
2025-07-30 17:40:13.619 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-30 17:40:13.620 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-30 17:40:13.621 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-30 17:40:13.622 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-30 17:40:13.673 [Information] BackupService: Getting predefined backup categories
2025-07-30 17:40:13.725 [Information] MainViewModel: Services initialized successfully
2025-07-30 17:40:13.728 [Information] MainViewModel: Scanning for Vocom devices
2025-07-30 17:40:13.729 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-30 17:40:13.729 [Information] VocomService: Using new enhanced device detection service
2025-07-30 17:40:13.730 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-30 17:40:13.730 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-30 17:40:13.969 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-30 17:40:13.969 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-30 17:40:13.970 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-30 17:40:13.970 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-30 17:40:13.970 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-30 17:40:13.971 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-30 17:40:13.971 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-30 17:40:13.971 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-30 17:40:14.247 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-30 17:40:14.247 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-30 17:40:14.248 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-30 17:40:14.249 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-30 17:40:14.249 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-30 17:40:14.258 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-30 17:40:14.258 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-30 17:40:14.258 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-30 17:40:14.259 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-30 17:40:14.259 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-30 17:40:14.261 [Debug] VocomService: Bluetooth is enabled
2025-07-30 17:40:14.261 [Debug] VocomService: Checking if WiFi is available
2025-07-30 17:40:14.271 [Debug] VocomService: WiFi is available
2025-07-30 17:40:14.272 [Information] VocomService: Found 3 Vocom devices
2025-07-30 17:40:14.273 [Information] MainViewModel: Found 3 Vocom device(s)
