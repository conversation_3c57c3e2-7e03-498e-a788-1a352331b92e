# PowerShell script to download missing Visual C++ Redistributable libraries
# Specifically targeting msvcr140.dll which is missing

Write-Host "=== VolvoFlashWR Missing VCRedist Library Downloader ===" -ForegroundColor Green
Write-Host ""

# Create Libraries directory if it doesn't exist
$LibrariesDir = ".\Libraries"
$VCRedistDir = ".\Libraries\VCRedist"

if (!(Test-Path $LibrariesDir)) {
    New-Item -ItemType Directory -Path $LibrariesDir -Force
    Write-Host "Created Libraries directory" -ForegroundColor Yellow
}

if (!(Test-Path $VCRedistDir)) {
    New-Item -ItemType Directory -Path $VCRedistDir -Force
    Write-Host "Created VCRedist directory" -ForegroundColor Yellow
}

# Function to download a file with retry logic
function Download-FileWithRetry {
    param(
        [string]$Url,
        [string]$OutputPath,
        [int]$MaxRetries = 3
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            Write-Host "Attempting to download from: $Url (Attempt $i/$MaxRetries)" -ForegroundColor Cyan
            Invoke-WebRequest -Uri $Url -OutFile $OutputPath -TimeoutSec 120 -UseBasicParsing
            
            if (Test-Path $OutputPath) {
                $fileSize = (Get-Item $OutputPath).Length
                Write-Host "Download successful! File size: $fileSize bytes" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Host "Download attempt $i failed: $($_.Exception.Message)" -ForegroundColor Red
            if ($i -lt $MaxRetries) {
                Write-Host "Retrying in 5 seconds..." -ForegroundColor Yellow
                Start-Sleep -Seconds 5
            }
        }
    }
    return $false
}

# Since the VCRedist installer was already run and requires a reboot,
# let's try to copy msvcr140.dll from the application's embedded resources
Write-Host "Checking for msvcr140.dll in application directories..." -ForegroundColor Cyan

# Check if we can find msvcr140.dll in the current system after the VCRedist installation
$systemPaths = @(
    "$env:SystemRoot\System32",
    "$env:SystemRoot\SysWOW64",
    "$env:ProgramFiles\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT",
    "$env:ProgramFiles(x86)\Microsoft Visual Studio\2019\*\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT",
    "$env:ProgramFiles(x86)\Microsoft Visual Studio\2017\*\VC\Redist\MSVC\*\x64\Microsoft.VC143.CRT"
)

$found = $false
foreach ($path in $systemPaths) {
    if ($path -like "*\*\*") {
        # Handle wildcard paths
        $expandedPaths = Get-ChildItem -Path ($path -replace '\*.*$', '') -Directory -ErrorAction SilentlyContinue | ForEach-Object {
            $subPath = $path -replace '\*', $_.Name
            if (Test-Path $subPath) { $subPath }
        }
        foreach ($expandedPath in $expandedPaths) {
            $msvcr140Path = Join-Path $expandedPath "msvcr140.dll"
            if (Test-Path $msvcr140Path) {
                Write-Host "Found msvcr140.dll at: $msvcr140Path" -ForegroundColor Green
                $destPath = Join-Path $VCRedistDir "msvcr140.dll"
                Copy-Item -Path $msvcr140Path -Destination $destPath -Force
                Write-Host "Copied msvcr140.dll to: $destPath" -ForegroundColor Green
                $found = $true
                break
            }
        }
    } else {
        $msvcr140Path = Join-Path $path "msvcr140.dll"
        if (Test-Path $msvcr140Path) {
            Write-Host "Found msvcr140.dll at: $msvcr140Path" -ForegroundColor Green
            $destPath = Join-Path $VCRedistDir "msvcr140.dll"
            Copy-Item -Path $msvcr140Path -Destination $destPath -Force
            Write-Host "Copied msvcr140.dll to: $destPath" -ForegroundColor Green
            $found = $true
            break
        }
    }
    if ($found) { break }
}

if (!$found) {
    Write-Host "msvcr140.dll not found in system directories" -ForegroundColor Yellow
    Write-Host "This is expected since a reboot is required after VCRedist installation" -ForegroundColor Yellow
}

# Check what we have now
Write-Host ""
Write-Host "=== Current VCRedist Library Status ===" -ForegroundColor Green
$vcredistFiles = @("msvcr120.dll", "msvcp120.dll", "msvcr140.dll", "msvcp140.dll", "vcruntime140.dll")

foreach ($file in $vcredistFiles) {
    $filePath = Join-Path $VCRedistDir $file
    if (Test-Path $filePath) {
        $fileSize = (Get-Item $filePath).Length
        Write-Host "[OK] $file ($fileSize bytes)" -ForegroundColor Green
    } else {
        Write-Host "[MISSING] $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Green
Write-Host "1. RESTART YOUR COMPUTER (required after VCRedist installation)" -ForegroundColor Red
Write-Host "2. After restart, try running: .\Run_x64_Compatible.bat" -ForegroundColor White
Write-Host "3. If still having issues, run this script again after restart" -ForegroundColor White
Write-Host "4. Check the Logs folder for detailed error information" -ForegroundColor White
Write-Host ""

Write-Host "Script completed!" -ForegroundColor Green
