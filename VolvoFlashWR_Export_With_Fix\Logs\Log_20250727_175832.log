Log started at 7/27/2025 5:58:32 PM
2025-07-27 17:58:32.786 [Information] LoggingService: Logging service initialized
2025-07-27 17:58:32.801 [Information] App: Starting integrated application initialization
2025-07-27 17:58:32.802 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 17:58:32.806 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 17:58:32.808 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 17:58:32.809 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 17:58:32.811 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 17:58:32.814 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 17:58:32.816 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 17:58:32.821 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 17:58:32.830 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.833 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.835 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.836 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 17:58:32.840 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.844 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.847 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.848 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 17:58:32.852 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.855 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.857 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.858 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 17:58:32.862 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.865 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.868 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.869 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 17:58:32.872 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.875 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.878 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.879 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 17:58:32.882 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.885 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.887 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.887 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 17:58:32.890 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 17:58:32.893 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.895 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 17:58:32.896 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 17:58:32.898 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 17:58:32.901 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 17:58:32.901 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 17:58:32.914 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 17:58:32.915 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 17:58:32.921 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 17:58:32.922 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 17:58:32.922 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 17:58:32.984 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-27 17:58:32.984 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 17:58:33.030 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-27 17:58:33.030 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 17:58:33.031 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 17:58:33.031 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 17:58:33.031 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 17:58:33.032 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 17:58:33.032 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 17:58:33.037 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 17:58:33.038 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 17:58:33.038 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 17:58:33.042 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 17:58:33.043 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 17:58:33.044 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 17:58:33.046 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 17:58:33.049 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 17:58:33.052 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 17:58:33.054 [Information] LibraryExtractor: Copying system libraries
2025-07-27 17:58:33.060 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 17:58:33.068 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 17:59:03.082 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 17:59:04.084 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 17:59:34.087 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 17:59:34.088 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:00:04.090 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 18:00:05.091 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 18:00:35.092 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 18:00:35.093 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 18:01:05.097 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 18:01:06.098 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 18:01:36.101 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 18:01:36.102 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 18:01:36.102 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 18:01:36.106 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 18:01:36.107 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 18:01:36.107 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 18:01:36.108 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 18:01:36.108 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 18:01:36.108 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 18:01:36.113 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 18:01:36.115 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 18:01:36.117 [Information] DependencyManager: Initializing dependency manager
2025-07-27 18:01:36.118 [Information] DependencyManager: Setting up library search paths
2025-07-27 18:01:36.119 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 18:01:36.119 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 18:01:36.119 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 18:01:36.120 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 18:01:36.121 [Information] DependencyManager: Verifying required directories
2025-07-27 18:01:36.122 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 18:01:36.122 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 18:01:36.123 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 18:01:36.124 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 18:01:36.126 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 18:01:36.147 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 18:01:36.155 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 18:01:36.170 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 18:01:36.177 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 18:01:36.179 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 18:01:36.180 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 18:01:36.198 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 18:01:56.048 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 18:01:56.050 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 18:01:56.052 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-27 18:01:56.052 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 18:01:56.487 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 18:01:56.487 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 18:01:56.488 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 18:01:56.490 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 18:01:56.491 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:01:56.491 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:01:56.492 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 18:01:56.493 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 18:01:56.494 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 18:01:56.494 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 18:01:56.495 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 18:01:56.495 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 18:01:56.496 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 18:01:56.496 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 18:01:56.497 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 18:01:56.498 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 18:01:56.499 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 18:01:56.499 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 18:01:56.500 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 18:01:56.502 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 18:01:56.503 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 18:01:56.504 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 18:01:56.504 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 18:01:56.504 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 18:01:56.506 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 18:01:56.560 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 18:01:56.681 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 18:01:56.681 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 18:01:56.793 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 18:01:56.793 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 18:01:56.795 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 18:01:56.879 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 18:01:56.879 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 18:01:56.946 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 18:01:56.947 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 18:01:56.948 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 18:01:57.240 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 18:01:57.240 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 18:01:57.507 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 18:01:57.508 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 18:01:57.509 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 18:01:57.621 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 18:01:57.621 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 18:01:57.730 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 18:01:57.731 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 18:01:57.731 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 18:01:57.801 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-27 18:01:57.801 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 18:01:57.802 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 18:01:57.803 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 18:01:57.803 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 18:01:57.804 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 18:01:57.804 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 18:01:57.805 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 18:01:57.806 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 18:01:57.807 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 18:01:57.808 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 18:01:57.808 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 18:01:57.809 [Information] DependencyManager: Setting up environment variables
2025-07-27 18:01:57.809 [Information] DependencyManager: Environment variables configured
2025-07-27 18:01:57.811 [Information] DependencyManager: Verifying library loading status
2025-07-27 18:01:58.147 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 18:01:58.147 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 18:01:58.148 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 18:01:58.150 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 18:01:58.152 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 18:01:58.156 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 18:01:58.158 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 18:01:58.158 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 18:01:58.159 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 18:01:58.160 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 18:01:58.161 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 18:01:58.161 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 18:01:58.161 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 18:01:58.162 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 18:01:58.162 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 18:01:58.162 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 18:01:58.162 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 18:01:58.163 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 18:01:58.163 [Information] App: Integrated startup completed successfully
2025-07-27 18:01:58.166 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 18:01:58.394 [Information] App: Initializing application services
2025-07-27 18:01:58.396 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 18:01:58.396 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 18:01:58.448 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 18:01:58.449 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 18:01:58.450 [Information] App: Configuration service initialized successfully
2025-07-27 18:01:58.452 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 18:01:58.453 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 18:01:58.460 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 18:01:58.461 [Information] App: Final useDummyImplementations value: False
2025-07-27 18:01:58.461 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 18:01:58.463 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-27 18:01:58.481 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 18:01:58.481 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 18:01:58.481 [Information] App: usePatchedImplementation flag is: True
2025-07-27 18:01:58.482 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 18:01:58.482 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 18:01:58.482 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 18:01:58.482 [Information] App: verboseLogging flag is: True
2025-07-27 18:01:58.485 [Information] App: Verifying real hardware requirements...
2025-07-27 18:01:58.485 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 18:01:58.486 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 18:01:58.486 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 18:01:58.486 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 18:01:58.487 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 18:01:58.487 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 18:01:58.490 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 18:01:58.491 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 18:01:58.502 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 18:01:58.505 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 18:01:58.506 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 18:01:58.508 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 18:01:58.514 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-27 18:02:19.665 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (aka.ms:443)
2025-07-27 18:02:19.666 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 18:02:19.667 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 18:02:19.667 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 18:02:19.668 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:02:19.668 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 18:02:19.668 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 18:02:19.669 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 18:02:19.669 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 18:02:19.669 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 18:02:19.670 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 18:02:19.671 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 18:02:19.671 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 18:02:19.672 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 18:02:19.672 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 18:02:19.672 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 18:02:19.673 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 18:02:19.673 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 18:02:19.673 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 18:02:19.675 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 18:02:19.675 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 18:02:19.677 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 18:02:19.678 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 18:02:19.678 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 18:02:19.679 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 18:02:19.680 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 18:02:19.681 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 18:02:19.683 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 18:02:19.684 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 18:02:19.685 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 18:02:19.686 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 18:02:19.716 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 18:02:19.717 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 18:02:19.720 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 18:02:19.721 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 18:02:19.722 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 18:02:19.722 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 18:02:19.725 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 18:02:19.725 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 18:02:19.726 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 18:02:19.726 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:02:19.726 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 18:02:19.727 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 18:02:19.727 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 18:02:19.729 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 18:02:19.733 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 18:02:19.737 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 18:02:19.738 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 18:02:19.739 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 18:02:19.754 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 18:02:19.778 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 18:02:19.780 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 18:02:19.780 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 18:02:19.782 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 18:02:19.782 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 18:02:19.782 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 18:02:19.783 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 18:02:19.891 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 18:02:19.892 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 18:02:19.893 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 18:02:19.893 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 18:02:19.894 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 18:02:19.895 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 18:02:19.896 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 18:02:19.896 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 18:02:19.898 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 18:02:19.899 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 18:02:19.900 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 18:02:19.900 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 18:02:19.901 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 18:02:19.906 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 18:02:19.907 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 18:02:19.907 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 18:02:19.907 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 18:02:19.908 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 18:02:19.908 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 18:02:19.908 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 18:02:19.909 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 18:02:19.910 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 18:02:19.910 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 18:02:19.912 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 18:02:19.913 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 18:02:19.913 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 18:02:19.913 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:02:19.914 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 18:02:19.916 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 18:02:19.916 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 18:02:19.917 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 18:02:19.918 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 18:02:19.920 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 18:02:19.920 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 18:02:19.920 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 18:02:19.922 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 18:02:19.925 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 18:02:19.925 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 18:02:20.175 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 18:02:20.176 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 18:02:20.176 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 18:02:20.177 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 18:02:20.179 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 18:02:20.180 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:20.181 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:20.182 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 18:02:20.182 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:20.183 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:20.183 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 18:02:20.407 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:20.407 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 18:02:20.506 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:20.506 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 18:02:20.507 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:20.508 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:20.508 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 18:02:20.621 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:20.716 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:20.717 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 18:02:20.804 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 18:02:20.947 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 18:02:21.079 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:21.208 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:21.208 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 18:02:21.209 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 18:02:21.486 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:21.486 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 18:02:21.573 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:21.664 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:21.665 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 18:02:21.825 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 18:02:21.980 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 18:02:21.981 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 18:02:22.124 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 18:02:22.281 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 18:02:22.348 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 18:02:22.352 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 18:02:22.353 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 18:02:22.353 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 18:02:22.354 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 18:02:22.354 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 18:02:22.356 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 18:02:22.358 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 18:02:22.362 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 18:02:22.362 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 18:02:22.362 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 18:02:22.364 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 18:02:22.365 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 18:02:41.168 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 18:02:41.382 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 18:02:41.387 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 18:02:41.615 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 18:02:42.264 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 18:02:42.264 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 18:02:42.342 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 18:02:42.513 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 18:02:42.670 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 18:02:42.713 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 18:02:42.713 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 18:02:42.714 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 18:02:42.715 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 18:02:42.716 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 18:02:42.717 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 18:02:42.717 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 18:02:42.717 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 18:02:42.717 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 18:02:42.718 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 18:02:42.718 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 18:02:42.719 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 18:02:42.719 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 18:02:42.719 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 18:02:42.719 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 18:02:42.720 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 18:02:42.720 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 18:02:42.720 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 18:02:42.720 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 18:02:42.721 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 18:02:42.721 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 18:02:42.721 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 18:02:42.721 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 18:02:42.722 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 18:02:42.722 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 18:02:42.722 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 18:02:42.722 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 18:02:42.723 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 18:02:42.723 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 18:02:42.723 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 18:02:42.723 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 18:02:42.724 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 18:02:42.724 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 18:02:42.724 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 18:02:42.724 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 18:02:42.725 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 18:02:42.725 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 18:02:42.725 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 18:02:42.725 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 18:02:42.725 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 18:02:42.726 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 18:02:42.726 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 18:02:42.726 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 18:02:42.726 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 18:02:42.727 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 18:02:42.727 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 18:02:42.728 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 18:02:42.729 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 18:02:42.730 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 18:02:42.731 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 18:02:42.731 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 18:02:42.731 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 18:02:42.731 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 18:02:42.732 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 18:02:42.735 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 18:02:42.736 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 18:02:42.738 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 18:02:42.739 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 18:02:42.816 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 18:02:42.817 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 18:02:42.818 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 18:02:42.820 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 18:02:42.822 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 18:02:42.822 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 18:02:42.824 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 18:02:42.825 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 18:02:42.827 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 18:02:42.828 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 18:02:42.833 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 18:02:42.833 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 18:02:42.833 [Information] VocomService: Native USB communication service initialized
2025-07-27 18:02:42.834 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 18:02:42.834 [Information] VocomService: Connection recovery service initialized
2025-07-27 18:02:42.835 [Information] VocomService: Enhanced services initialization completed
2025-07-27 18:02:42.837 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:42.853 [Information] VocomService: PTT application is not running
2025-07-27 18:02:42.854 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:42.856 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:42.857 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 18:02:42.857 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 18:02:42.858 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 18:02:42.861 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 18:02:42.861 [Information] VocomService: Using new enhanced device detection service
2025-07-27 18:02:42.863 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 18:02:42.864 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 18:02:43.254 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 18:02:43.256 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 18:02:43.257 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 18:02:43.258 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 18:02:43.259 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 18:02:43.260 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 18:02:43.262 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 18:02:43.265 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 18:02:43.702 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 18:02:43.705 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 18:02:43.706 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 18:02:43.707 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 18:02:43.709 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 18:02:43.719 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 18:02:43.720 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 18:02:43.720 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 18:02:43.721 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 18:02:43.721 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:43.722 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:43.724 [Debug] VocomService: Checking if WiFi is available
2025-07-27 18:02:43.726 [Debug] VocomService: WiFi is available
2025-07-27 18:02:43.727 [Information] VocomService: Found 3 Vocom devices
2025-07-27 18:02:43.727 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 18:02:43.729 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 00970fa9-36b3-4ec0-a271-ba33094d353c, Type: USB)
2025-07-27 18:02:43.729 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: e8c8fd34-fb0a-45b6-a561-a70826b005b8, Type: Bluetooth)
2025-07-27 18:02:43.730 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: cf59045a-38c3-4545-aaa6-47184b78eb4f, Type: WiFi)
2025-07-27 18:02:43.731 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 18:02:43.731 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 18:02:43.731 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 18:02:43.732 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 18:02:43.732 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 18:02:43.732 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 18:02:43.733 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 18:02:43.733 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 18:02:43.733 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 18:02:43.733 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 18:02:43.734 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 18:02:43.735 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 18:02:43.735 [Information] VocomService: Native USB communication service initialized
2025-07-27 18:02:43.735 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 18:02:43.736 [Information] VocomService: Connection recovery service initialized
2025-07-27 18:02:43.736 [Information] VocomService: Enhanced services initialization completed
2025-07-27 18:02:43.736 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:43.749 [Information] VocomService: PTT application is not running
2025-07-27 18:02:43.750 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:43.750 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:43.751 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 18:02:43.751 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 18:02:43.751 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 18:02:43.752 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 18:02:43.789 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 18:02:43.789 [Information] VocomService: Using new enhanced device detection service
2025-07-27 18:02:43.789 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 18:02:43.789 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 18:02:44.070 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 18:02:44.071 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 18:02:44.071 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 18:02:44.071 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 18:02:44.072 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 18:02:44.072 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 18:02:44.072 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 18:02:44.073 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 18:02:44.346 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 18:02:44.347 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 18:02:44.347 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 18:02:44.348 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 18:02:44.349 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 18:02:44.356 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 18:02:44.357 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 18:02:44.357 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 18:02:44.357 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 18:02:44.358 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:44.358 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:44.358 [Debug] VocomService: Checking if WiFi is available
2025-07-27 18:02:44.359 [Debug] VocomService: WiFi is available
2025-07-27 18:02:44.360 [Information] VocomService: Found 3 Vocom devices
2025-07-27 18:02:44.360 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 18:02:44.362 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:44.363 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:44.376 [Information] VocomService: PTT application is not running
2025-07-27 18:02:44.379 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 18:02:44.379 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 18:02:44.380 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:44.394 [Information] VocomService: PTT application is not running
2025-07-27 18:02:44.395 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 18:02:44.397 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 18:02:44.398 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 18:02:44.401 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 18:02:44.402 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:44.403 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:44.404 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:44.404 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:44.404 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:44.405 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:44.405 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:44.405 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:44.406 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:44.406 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 18:02:44.406 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:44.406 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:44.407 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:44.407 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:44.407 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:44.408 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 18:02:44.409 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:44.409 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 18:02:44.409 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:44.410 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 18:02:44.410 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 18:02:44.410 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 18:02:44.410 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 18:02:45.412 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 18:02:45.412 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 18:02:45.412 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 18:02:45.413 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:45.413 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:45.414 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:45.414 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:45.414 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:45.414 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:45.415 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:45.415 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:45.415 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:45.415 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 18:02:45.416 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:45.416 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:45.416 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:45.417 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:45.417 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:45.417 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 18:02:45.418 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:45.418 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 18:02:45.418 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:45.419 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 18:02:45.419 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 18:02:45.419 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 18:02:45.420 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 18:02:46.419 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 18:02:46.420 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 18:02:46.420 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 18:02:46.420 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:46.421 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:46.421 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:46.421 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:46.422 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:46.422 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:46.422 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:46.422 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:46.423 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:46.423 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 18:02:46.423 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:46.423 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:46.424 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:46.424 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:46.424 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:46.424 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 18:02:46.425 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:46.425 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 18:02:46.425 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:46.425 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 18:02:46.426 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 18:02:46.426 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 18:02:46.426 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 18:02:46.427 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 18:02:46.427 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 18:02:46.608 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 18:02:46.609 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 18:02:46.610 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 18:02:46.611 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 18:02:46.612 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 18:02:46.612 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 18:02:46.612 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 18:02:46.615 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 18:02:46.616 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 18:02:46.619 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-07-27 18:02:46.621 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 18:02:46.622 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 18:02:46.623 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 18:02:46.623 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 18:02:46.623 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 18:02:46.624 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:46.625 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 18:02:46.627 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 18:02:46.630 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:46.631 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 18:02:46.634 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 18:02:46.637 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 18:02:46.637 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 18:02:46.638 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 18:02:46.638 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 18:02:46.639 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 18:02:46.639 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 18:02:46.639 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 18:02:46.641 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 18:02:46.642 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 18:02:46.642 [Information] VocomService: Using new enhanced device detection service
2025-07-27 18:02:46.642 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 18:02:46.643 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 18:02:46.882 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 18:02:46.882 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 18:02:46.883 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 18:02:46.883 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 18:02:46.883 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 18:02:46.884 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 18:02:46.884 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 18:02:46.884 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 18:02:47.154 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 18:02:47.154 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 18:02:47.155 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 18:02:47.155 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 18:02:47.158 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 18:02:47.164 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 18:02:47.165 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 18:02:47.165 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 18:02:47.165 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 18:02:47.166 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:47.166 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:47.166 [Debug] VocomService: Checking if WiFi is available
2025-07-27 18:02:47.168 [Debug] VocomService: WiFi is available
2025-07-27 18:02:47.168 [Information] VocomService: Found 3 Vocom devices
2025-07-27 18:02:47.169 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:47.170 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:47.170 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:47.183 [Information] VocomService: PTT application is not running
2025-07-27 18:02:47.183 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 18:02:47.183 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 18:02:47.184 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:47.200 [Information] VocomService: PTT application is not running
2025-07-27 18:02:47.201 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 18:02:47.201 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 18:02:47.202 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 18:02:47.202 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 18:02:47.203 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:47.203 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:47.203 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:47.204 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:47.204 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:47.204 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:47.205 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:47.205 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:47.205 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:47.205 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 18:02:47.206 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:47.206 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:47.206 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:47.206 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:47.207 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:47.207 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 18:02:47.207 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:47.208 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 18:02:47.208 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:47.208 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 18:02:47.208 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 18:02:47.208 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 18:02:47.209 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 18:02:48.208 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 18:02:48.209 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 18:02:48.209 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 18:02:48.210 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:48.210 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:48.210 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:48.211 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:48.211 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:48.211 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:48.211 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:48.212 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:48.212 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:48.212 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 18:02:48.212 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:48.213 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:48.213 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:48.213 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:48.214 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:48.214 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 18:02:48.214 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:48.215 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 18:02:48.215 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:48.215 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 18:02:48.215 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 18:02:48.216 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 18:02:48.216 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 18:02:49.215 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 18:02:49.216 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 18:02:49.216 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 18:02:49.217 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:49.217 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:49.217 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:49.218 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:49.218 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:49.218 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:49.219 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:49.219 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:49.219 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:49.220 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 18:02:49.220 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:49.221 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:49.221 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 18:02:49.221 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 18:02:49.221 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 18:02:49.222 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 18:02:49.222 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:49.223 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 18:02:49.223 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 18:02:49.223 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 18:02:49.223 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 18:02:49.224 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 18:02:49.224 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 18:02:49.224 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 18:02:49.225 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 18:02:49.225 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 18:02:49.225 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 18:02:49.226 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 18:02:49.226 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 18:02:49.226 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 18:02:49.226 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 18:02:49.227 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 18:02:49.227 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 18:02:49.227 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 18:02:49.228 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-07-27 18:02:49.228 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 18:02:49.228 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 18:02:49.229 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 18:02:49.229 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 18:02:49.229 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 18:02:49.230 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 18:02:49.230 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:49.231 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:49.231 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 18:02:49.231 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 18:02:49.231 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 18:02:49.232 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:49.248 [Information] VocomService: PTT application is not running
2025-07-27 18:02:49.251 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 18:02:49.251 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:49.251 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:49.253 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 18:02:50.058 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 18:02:50.058 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 18:02:50.059 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 18:02:50.059 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 18:02:50.062 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 18:02:50.064 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 18:02:50.082 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 18:02:50.085 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 18:02:50.089 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 18:02:50.097 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 18:02:50.100 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 18:02:50.111 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 18:02:50.112 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 18:02:50.113 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 18:02:50.113 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 18:02:50.113 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 18:02:50.113 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 18:02:50.114 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 18:02:50.114 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 18:02:50.114 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 18:02:50.115 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 18:02:50.115 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 18:02:50.115 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 18:02:50.115 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 18:02:50.116 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 18:02:50.116 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 18:02:50.116 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 18:02:50.117 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 18:02:50.120 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 18:02:50.127 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 18:02:50.128 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 18:02:50.131 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 18:02:50.133 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 18:02:50.139 [Information] CANRegisterAccess: Read value 0xD3 from register 0x0141 (simulated)
2025-07-27 18:02:50.140 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 18:02:50.141 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 18:02:50.142 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 18:02:50.148 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 18:02:50.148 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 18:02:50.153 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 18:02:50.154 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 18:02:50.154 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 18:02:50.159 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 18:02:50.160 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 18:02:50.160 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 18:02:50.165 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 18:02:50.166 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 18:02:50.171 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 18:02:50.172 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 18:02:50.177 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 18:02:50.178 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 18:02:50.183 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 18:02:50.184 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 18:02:50.189 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 18:02:50.190 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 18:02:50.195 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 18:02:50.196 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 18:02:50.201 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 18:02:50.202 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 18:02:50.207 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 18:02:50.208 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 18:02:50.213 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 18:02:50.214 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 18:02:50.219 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 18:02:50.220 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 18:02:50.225 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 18:02:50.226 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 18:02:50.231 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 18:02:50.232 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 18:02:50.237 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 18:02:50.238 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 18:02:50.243 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 18:02:50.244 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 18:02:50.248 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 18:02:50.249 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 18:02:50.254 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 18:02:50.255 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 18:02:50.260 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 18:02:50.261 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 18:02:50.261 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 18:02:50.267 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 18:02:50.268 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 18:02:50.268 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 18:02:50.269 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 18:02:50.274 [Information] CANRegisterAccess: Read value 0x6B from register 0x0141 (simulated)
2025-07-27 18:02:50.280 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 18:02:50.286 [Information] CANRegisterAccess: Read value 0x5A from register 0x0141 (simulated)
2025-07-27 18:02:50.287 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 18:02:50.287 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 18:02:50.287 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 18:02:50.288 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 18:02:50.293 [Information] CANRegisterAccess: Read value 0x9A from register 0x0140 (simulated)
2025-07-27 18:02:50.294 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 18:02:50.294 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 18:02:50.297 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 18:02:50.297 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 18:02:50.308 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 18:02:50.309 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 18:02:50.310 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 18:02:50.314 [Information] VocomService: Sending data and waiting for response
2025-07-27 18:02:50.314 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 18:02:50.365 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 18:02:50.367 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 18:02:50.367 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 18:02:50.369 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 18:02:50.370 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 18:02:50.381 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 18:02:50.382 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 18:02:50.382 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 18:02:50.393 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 18:02:50.404 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 18:02:50.415 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 18:02:50.426 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 18:02:50.437 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 18:02:50.438 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 18:02:50.439 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 18:02:50.450 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 18:02:50.451 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 18:02:50.451 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 18:02:50.462 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 18:02:50.473 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 18:02:50.484 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 18:02:50.495 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 18:02:50.506 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 18:02:50.517 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 18:02:50.518 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 18:02:50.519 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 18:02:50.531 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 18:02:50.532 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 18:02:50.532 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 18:02:50.532 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 18:02:50.533 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 18:02:50.533 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 18:02:50.533 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 18:02:50.534 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 18:02:50.534 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 18:02:50.534 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 18:02:50.534 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 18:02:50.535 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 18:02:50.535 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 18:02:50.535 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 18:02:50.536 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 18:02:50.536 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 18:02:50.536 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 18:02:50.637 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 18:02:50.638 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 18:02:50.640 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 18:02:50.642 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:50.642 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 18:02:50.642 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 18:02:50.643 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:50.643 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 18:02:50.643 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 18:02:50.644 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:50.644 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 18:02:50.644 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 18:02:50.645 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:50.645 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 18:02:50.646 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 18:02:50.647 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 18:02:50.648 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-27 18:02:50.648 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-27 18:02:50.651 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 18:02:50.653 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 18:02:50.656 [Information] BackupService: Initializing backup service
2025-07-27 18:02:50.656 [Information] BackupService: Backup service initialized successfully
2025-07-27 18:02:50.656 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 18:02:50.657 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 18:02:50.659 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 18:02:50.699 [Information] BackupService: Compressing backup data
2025-07-27 18:02:50.707 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-27 18:02:50.708 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 18:02:50.709 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 18:02:50.709 [Information] BackupService: Compressing backup data
2025-07-27 18:02:50.711 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-27 18:02:50.711 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 18:02:50.711 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 18:02:50.712 [Information] BackupService: Compressing backup data
2025-07-27 18:02:50.713 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-27 18:02:50.713 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 18:02:50.714 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 18:02:50.714 [Information] BackupService: Compressing backup data
2025-07-27 18:02:50.715 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-27 18:02:50.715 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 18:02:50.716 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 18:02:50.716 [Information] BackupService: Compressing backup data
2025-07-27 18:02:50.717 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-27 18:02:50.718 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 18:02:50.718 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 18:02:50.719 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 18:02:50.719 [Information] BackupService: Compressing backup data
2025-07-27 18:02:50.720 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (511 bytes)
2025-07-27 18:02:50.720 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 18:02:50.721 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 18:02:50.723 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 18:02:50.726 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 18:02:50.728 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 18:02:50.807 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 18:02:50.809 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 18:02:50.810 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 18:02:50.810 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 18:02:50.811 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 18:02:50.812 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 18:02:50.813 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 18:02:50.817 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 18:02:50.818 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 18:02:50.828 [Information] LicensingService: Initializing licensing service
2025-07-27 18:02:50.872 [Information] LicensingService: License information loaded successfully
2025-07-27 18:02:50.875 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 18:02:50.876 [Information] App: Licensing service initialized successfully
2025-07-27 18:02:50.876 [Information] App: License status: Trial
2025-07-27 18:02:50.877 [Information] App: Trial period: 30 days remaining
2025-07-27 18:02:50.877 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 18:02:50.901 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-27 18:02:51.053 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 18:02:51.054 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 18:02:51.054 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 18:02:51.054 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 18:02:51.055 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 18:02:51.056 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 18:02:51.056 [Information] VocomService: Native USB communication service initialized
2025-07-27 18:02:51.056 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 18:02:51.056 [Information] VocomService: Connection recovery service initialized
2025-07-27 18:02:51.057 [Information] VocomService: Enhanced services initialization completed
2025-07-27 18:02:51.057 [Information] VocomService: Checking if PTT application is running
2025-07-27 18:02:51.073 [Information] VocomService: PTT application is not running
2025-07-27 18:02:51.074 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:51.075 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:51.075 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 18:02:51.126 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 18:02:51.126 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 18:02:51.126 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 18:02:51.127 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 18:02:51.127 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 18:02:51.127 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: dd296b0f-ec51-483a-9301-a8d2a3bf4967
2025-07-27 18:02:51.129 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 18:02:51.129 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 18:02:51.130 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 18:02:51.130 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 18:02:51.132 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 18:02:51.132 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 18:02:51.133 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 18:02:51.134 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 18:02:51.134 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 18:02:51.146 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 18:02:51.146 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 18:02:51.146 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 18:02:51.147 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 18:02:51.147 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 18:02:51.147 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 18:02:51.147 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 18:02:51.148 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 18:02:51.148 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 18:02:51.148 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 18:02:51.149 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 18:02:51.149 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 18:02:51.149 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 18:02:51.149 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 18:02:51.150 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 18:02:51.150 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 18:02:51.150 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 18:02:51.150 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 18:02:51.156 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 18:02:51.157 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 18:02:51.157 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 18:02:51.158 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 18:02:51.164 [Information] CANRegisterAccess: Read value 0x5B from register 0x0141 (simulated)
2025-07-27 18:02:51.164 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 18:02:51.164 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 18:02:51.165 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 18:02:51.177 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 18:02:51.177 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 18:02:51.183 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 18:02:51.184 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 18:02:51.184 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 18:02:51.190 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 18:02:51.191 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 18:02:51.191 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 18:02:51.197 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 18:02:51.198 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 18:02:51.204 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 18:02:51.205 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 18:02:51.211 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 18:02:51.211 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 18:02:51.217 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 18:02:51.218 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 18:02:51.224 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 18:02:51.225 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 18:02:51.230 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 18:02:51.231 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 18:02:51.237 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 18:02:51.238 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 18:02:51.244 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 18:02:51.245 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 18:02:51.251 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 18:02:51.252 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 18:02:51.258 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 18:02:51.259 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 18:02:51.265 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 18:02:51.266 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 18:02:51.272 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 18:02:51.273 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 18:02:51.279 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 18:02:51.280 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 18:02:51.286 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 18:02:51.287 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 18:02:51.293 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 18:02:51.294 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 18:02:51.300 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 18:02:51.301 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 18:02:51.307 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 18:02:51.308 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 18:02:51.308 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 18:02:51.314 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 18:02:51.315 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 18:02:51.315 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 18:02:51.316 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 18:02:51.321 [Information] CANRegisterAccess: Read value 0xEF from register 0x0141 (simulated)
2025-07-27 18:02:51.327 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 18:02:51.333 [Information] CANRegisterAccess: Read value 0x0C from register 0x0141 (simulated)
2025-07-27 18:02:51.334 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 18:02:51.334 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 18:02:51.335 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 18:02:51.335 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 18:02:51.340 [Information] CANRegisterAccess: Read value 0xF0 from register 0x0140 (simulated)
2025-07-27 18:02:51.341 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 18:02:51.341 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 18:02:51.342 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 18:02:51.342 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 18:02:51.352 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 18:02:51.353 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 18:02:51.353 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 18:02:51.353 [Information] VocomService: Sending data and waiting for response
2025-07-27 18:02:51.354 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 18:02:51.404 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 18:02:51.404 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 18:02:51.405 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 18:02:51.405 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 18:02:51.406 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 18:02:51.417 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 18:02:51.417 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 18:02:51.418 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 18:02:51.429 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 18:02:51.440 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 18:02:51.451 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 18:02:51.462 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 18:02:51.473 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 18:02:51.473 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 18:02:51.474 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 18:02:51.485 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 18:02:51.485 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 18:02:51.486 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 18:02:51.497 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 18:02:51.508 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 18:02:51.519 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 18:02:51.530 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 18:02:51.541 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 18:02:51.552 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 18:02:51.552 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 18:02:51.553 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 18:02:51.564 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 18:02:51.564 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 18:02:51.565 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 18:02:51.565 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 18:02:51.565 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 18:02:51.565 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 18:02:51.566 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 18:02:51.566 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 18:02:51.566 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 18:02:51.566 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 18:02:51.567 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 18:02:51.567 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 18:02:51.567 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 18:02:51.568 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 18:02:51.568 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 18:02:51.568 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 18:02:51.568 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 18:02:51.669 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 18:02:51.669 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 18:02:51.670 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 18:02:51.670 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:51.671 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 18:02:51.671 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 18:02:51.671 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:51.672 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 18:02:51.672 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 18:02:51.672 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:51.673 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 18:02:51.673 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 18:02:51.673 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 18:02:51.674 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 18:02:51.674 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 18:02:51.724 [Information] BackupService: Initializing backup service
2025-07-27 18:02:51.724 [Information] BackupService: Backup service initialized successfully
2025-07-27 18:02:51.775 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 18:02:51.775 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 18:02:51.777 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 18:02:51.777 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 18:02:51.827 [Information] BackupService: Getting predefined backup categories
2025-07-27 18:02:51.878 [Information] MainViewModel: Services initialized successfully
2025-07-27 18:02:51.881 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 18:02:51.882 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 18:02:51.883 [Information] VocomService: Using new enhanced device detection service
2025-07-27 18:02:51.883 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 18:02:51.883 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 18:02:52.134 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 18:02:52.134 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 18:02:52.134 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 18:02:52.134 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 18:02:52.135 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 18:02:52.135 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 18:02:52.135 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 18:02:52.136 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 18:02:52.416 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 18:02:52.417 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 18:02:52.417 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 18:02:52.418 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 18:02:52.419 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 18:02:52.426 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 18:02:52.426 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 18:02:52.426 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 18:02:52.427 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 18:02:52.427 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 18:02:52.429 [Debug] VocomService: Bluetooth is enabled
2025-07-27 18:02:52.430 [Debug] VocomService: Checking if WiFi is available
2025-07-27 18:02:52.430 [Debug] VocomService: WiFi is available
2025-07-27 18:02:52.431 [Information] VocomService: Found 3 Vocom devices
2025-07-27 18:02:52.432 [Information] MainViewModel: Found 3 Vocom device(s)
